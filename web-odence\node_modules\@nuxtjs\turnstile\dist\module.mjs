import fs from 'node:fs';
import { fileURLToPath } from 'node:url';
import { defineNuxtModule, useLogger, installModule, addImports, addComponent, addServerHandler } from '@nuxt/kit';
import { resolve, join } from 'pathe';
import { defu } from 'defu';

const module = defineNuxtModule({
  meta: {
    configKey: "turnstile",
    name: "@nuxtjs/turnstile",
    compatibility: {
      bridge: false,
      nuxt: ">=3"
    }
  },
  defaults: (nuxt) => ({
    secretKey: nuxt.options.dev ? "1x0000000000000000000000000000000AA" : void 0,
    siteKey: nuxt.options.dev ? "1x00000000000000000000AA" : void 0,
    addValidateEndpoint: false
  }),
  async setup(options, nuxt) {
    const logger = useLogger("turnstile");
    const siteKey = options.siteKey || nuxt.options.runtimeConfig.public?.turnstile?.siteKey;
    if (!siteKey) {
      logger.warn(
        "No site key was provided. Make sure you pass one at runtime by setting NUXT_PUBLIC_TURNSTILE_SITE_KEY."
      );
    }
    if (options.secretKeyPath) {
      try {
        options.secretKey = fs.readFileSync(
          resolve(nuxt.options.rootDir, options.secretKeyPath),
          "utf-8"
        );
      } catch {
      }
      if (!options.secretKey) {
        logger.warn(`No secret key present in \`${options.secretKeyPath}\`.`);
      }
    }
    const runtimeDir = fileURLToPath(new URL("./runtime", import.meta.url));
    nuxt.options.build.transpile.push(runtimeDir);
    nuxt.options.runtimeConfig = defu(nuxt.options.runtimeConfig, {
      turnstile: {
        secretKey: options.secretKey
      },
      public: {
        turnstile: {
          siteKey
        }
      }
    });
    await installModule("@nuxt/scripts");
    const turnstileReg = {
      import: {
        from: join(runtimeDir, "composables/turnstile"),
        name: "useScriptCloudflareTurnstile"
      }
    };
    addImports(turnstileReg.import);
    nuxt.hook("scripts:registry", (registry) => {
      const cloudflareScriptRegistry = registry.find((r) => r.label === "cloudflareTurnstile");
      if (cloudflareScriptRegistry) {
        Object.assign(cloudflareScriptRegistry, turnstileReg);
      }
    });
    addComponent({
      name: "NuxtTurnstile",
      filePath: join(runtimeDir, "components", "NuxtTurnstile")
    });
    if (options.addValidateEndpoint) {
      addServerHandler({
        route: "/_turnstile/validate",
        handler: join(runtimeDir, "server/validate.post")
      });
    }
    nuxt.hook("nitro:config", (config) => {
      config.externals = defu(config.externals, {
        inline: [runtimeDir]
      });
      config.imports = defu(config.imports, {
        presets: [
          {
            from: join(runtimeDir, "server/utils/verify"),
            imports: ["verifyTurnstileToken"]
          }
        ]
      });
    });
    nuxt.options.typescript.hoist.push("@types/cloudflare-turnstile");
    nuxt.hook("prepare:types", ({ references }) => {
      references.push({ types: "@types/cloudflare-turnstile" });
    });
  }
});

export { module as default };
