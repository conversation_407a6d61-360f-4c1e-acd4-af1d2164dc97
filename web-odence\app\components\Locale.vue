<script lang="ts" setup>
import { LanguageIcon } from '@heroicons/vue/24/solid';
import { ChevronDownIcon } from '@heroicons/vue/24/solid';
const { setLocale, availableLocales } = useI18n();

const props = defineProps({
	type: {
		type: String,
		required: true
	},
	dropdownClass: {
		type: String,
		default: ''
	},
	itemClass: {
		type: String,
		default: ''
	}
});
</script>

<template>
	<div class="dropdown" :class="dropdownClass">
		<div v-if="type === 'button'" tabindex="0" role="button" class="btn btn-outline flex items-center justify-between border-b-2 border-transparent dark:hover:border-gray-400 hover:border-gray-600 duration-300 ease-out transition-all hover:-translate-y-0.5 group">
			<a href="#" class="p-1.5 px-4 bg-transparent text-lg">{{ $t('lang.name') }}</a>
			<ChevronDownIcon class="size-5 transition-transform duration-200 group-focus:rotate-180" />
		</div>
		<div v-else-if="type === 'button-mobile'" tabindex="0" role="" class="p-1.5 px-4 bg-transparent focus:bg-transparent border-b-3 border-transparent hover:border-base-200">
			<p class="text-right">{{ $t('lang.name') }}</p>
		</div>
		<div v-else tabindex="0" role="" class="p-2 px-2.5 flex items-center">
			<a href="#"> <LanguageIcon class="size-5" /> </a>
		</div>
		
		<ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-1 w-52 p-2 shadow-md" :class="itemClass">
			<li v-for="locale in availableLocales" :key="locale" class="text-md" @click.prevent="setLocale(locale)">
				<a href="#">{{ $t(`lang.${locale}`) }}</a>
			</li>
		</ul>
	</div>
</template>
