import type { HTMLAttributes, ImgHTMLAttributes } from 'vue';
import type { ElementScriptTrigger } from '../types.js';
interface VimeoOptions {
    id?: number | undefined;
    url?: string | undefined;
    autopause?: boolean | undefined;
    autoplay?: boolean | undefined;
    background?: boolean | undefined;
    byline?: boolean | undefined;
    color?: string | undefined;
    controls?: boolean | undefined;
    dnt?: boolean | undefined;
    height?: number | undefined;
    interactive_params?: string | undefined;
    keyboard?: boolean | undefined;
    loop?: boolean | undefined;
    maxheight?: number | undefined;
    maxwidth?: number | undefined;
    muted?: boolean | undefined;
    pip?: boolean | undefined;
    playsinline?: boolean | undefined;
    portrait?: boolean | undefined;
    responsive?: boolean | undefined;
    speed?: boolean | undefined;
    quality?: Vimeo.VimeoVideoQuality | undefined;
    texttrack?: string | undefined;
    title?: boolean | undefined;
    transparent?: boolean | undefined;
    width?: number | undefined;
}
type __VLS_Props = {
    trigger?: ElementScriptTrigger;
    placeholderAttrs?: ImgHTMLAttributes;
    rootAttrs?: HTMLAttributes;
    aboveTheFold?: boolean;
    vimeoOptions?: VimeoOptions;
    id?: number | undefined;
    url?: string | undefined;
};
declare var __VLS_1: any, __VLS_3: {}, __VLS_9: {}, __VLS_11: {}, __VLS_13: {};
type __VLS_Slots = {} & {
    placeholder?: (props: typeof __VLS_1) => any;
} & {
    loading?: (props: typeof __VLS_3) => any;
} & {
    awaitingLoad?: (props: typeof __VLS_9) => any;
} & {
    error?: (props: typeof __VLS_11) => any;
} & {
    default?: (props: typeof __VLS_13) => any;
};
declare const __VLS_component: import("vue").DefineComponent<__VLS_Props, {
    play: () => Promise<void> | undefined;
    pause: () => Promise<void> | undefined;
    getDuration: () => Promise<number> | undefined;
    getCurrentTime: () => Promise<number> | undefined;
    setCurrentTime: (time: number) => Promise<number> | undefined;
    getVolume: () => Promise<number> | undefined;
    setVolume: (volume: number) => Promise<number> | undefined;
    getPaused: () => Promise<boolean> | undefined;
    getEnded: () => Promise<boolean> | undefined;
    getLoop: () => Promise<boolean> | undefined;
    setLoop: (loop: boolean) => Promise<boolean> | undefined;
    getPlaybackRate: () => Promise<number> | undefined;
    setPlaybackRate: (rate: number) => Promise<number> | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    loaded: (event: import("@vimeo/player").LoadedEvent, player: Vimeo.Player) => any;
    error: (event: import("@vimeo/player").Error, player: Vimeo.Player) => any;
    cuechange: (event: import("@vimeo/player").CueChangeEvent, player: Vimeo.Player) => any;
    durationchange: (event: import("@vimeo/player").DurationChangeEvent, player: Vimeo.Player) => any;
    ended: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    pause: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    play: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    playing: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    progress: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    resize: (event: import("@vimeo/player").ResizeEvent, player: Vimeo.Player) => any;
    seeked: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    seeking: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    timeupdate: (event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any;
    volumechange: (event: import("@vimeo/player").VolumeChangeEvent, player: Vimeo.Player) => any;
    fullscreenchange: (event: import("@vimeo/player").FullScreenChangeEvent, player: Vimeo.Player) => any;
    texttrackchange: (event: import("@vimeo/player").TextTrackChangeEvent, player: Vimeo.Player) => any;
    chapterchange: (event: import("@vimeo/player").VimeoChapter, player: Vimeo.Player) => any;
    cuepoint: (event: import("@vimeo/player").CuePointEvent, player: Vimeo.Player) => any;
    playbackratechange: (event: import("@vimeo/player").PlaybackRateEvent, player: Vimeo.Player) => any;
    bufferstart: (event: never, player: Vimeo.Player) => any;
    bufferend: (event: never, player: Vimeo.Player) => any;
    qualitychange: (event: import("@vimeo/player").QualityChangeEvent, player: Vimeo.Player) => any;
    camerachange: (event: import("@vimeo/player").VimeoCameraProps, player: Vimeo.Player) => any;
    enterpictureinpicture: (event: never, player: Vimeo.Player) => any;
    leavepictureinpicture: (event: never, player: Vimeo.Player) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    onLoaded?: ((event: import("@vimeo/player").LoadedEvent, player: Vimeo.Player) => any) | undefined;
    onError?: ((event: import("@vimeo/player").Error, player: Vimeo.Player) => any) | undefined;
    onCuechange?: ((event: import("@vimeo/player").CueChangeEvent, player: Vimeo.Player) => any) | undefined;
    onDurationchange?: ((event: import("@vimeo/player").DurationChangeEvent, player: Vimeo.Player) => any) | undefined;
    onEnded?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onPause?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onPlay?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onPlaying?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onProgress?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onResize?: ((event: import("@vimeo/player").ResizeEvent, player: Vimeo.Player) => any) | undefined;
    onSeeked?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onSeeking?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onTimeupdate?: ((event: import("@vimeo/player").TimeEvent, player: Vimeo.Player) => any) | undefined;
    onVolumechange?: ((event: import("@vimeo/player").VolumeChangeEvent, player: Vimeo.Player) => any) | undefined;
    onFullscreenchange?: ((event: import("@vimeo/player").FullScreenChangeEvent, player: Vimeo.Player) => any) | undefined;
    onTexttrackchange?: ((event: import("@vimeo/player").TextTrackChangeEvent, player: Vimeo.Player) => any) | undefined;
    onChapterchange?: ((event: import("@vimeo/player").VimeoChapter, player: Vimeo.Player) => any) | undefined;
    onCuepoint?: ((event: import("@vimeo/player").CuePointEvent, player: Vimeo.Player) => any) | undefined;
    onPlaybackratechange?: ((event: import("@vimeo/player").PlaybackRateEvent, player: Vimeo.Player) => any) | undefined;
    onBufferstart?: ((event: never, player: Vimeo.Player) => any) | undefined;
    onBufferend?: ((event: never, player: Vimeo.Player) => any) | undefined;
    onQualitychange?: ((event: import("@vimeo/player").QualityChangeEvent, player: Vimeo.Player) => any) | undefined;
    onCamerachange?: ((event: import("@vimeo/player").VimeoCameraProps, player: Vimeo.Player) => any) | undefined;
    onEnterpictureinpicture?: ((event: never, player: Vimeo.Player) => any) | undefined;
    onLeavepictureinpicture?: ((event: never, player: Vimeo.Player) => any) | undefined;
}>, {
    trigger: ElementScriptTrigger;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
