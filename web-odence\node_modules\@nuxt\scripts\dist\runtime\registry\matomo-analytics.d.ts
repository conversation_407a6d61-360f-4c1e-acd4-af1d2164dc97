import type { RegistryScriptInput } from '#nuxt-scripts/types';
export declare const MatomoAnalyticsOptions: import("valibot").ObjectSchema<{
    readonly matomoUrl: import("valibot").OptionalSchema<import("valibot").StringSchema<undefined>, undefined>;
    readonly siteId: import("valibot").OptionalSchema<import("valibot").UnionSchema<[import("valibot").StringSchema<undefined>, import("valibot").NumberSchema<undefined>], undefined>, undefined>;
    readonly cloudId: import("valibot").OptionalSchema<import("valibot").StringSchema<undefined>, undefined>;
    readonly trackerUrl: import("valibot").OptionalSchema<import("valibot").StringSchema<undefined>, undefined>;
    readonly trackPageView: import("valibot").OptionalSchema<import("valibot").BooleanSchema<undefined>, undefined>;
    readonly enableLinkTracking: import("valibot").OptionalSchema<import("valibot").BooleanSchema<undefined>, undefined>;
    readonly disableCookies: import("valibot").OptionalSchema<import("valibot").BooleanSchema<undefined>, undefined>;
}, undefined>;
export type MatomoAnalyticsInput = RegistryScriptInput<typeof MatomoAnalyticsOptions, false, false, false>;
interface MatomoAnalyticsApi {
    _paq: unknown[];
}
declare global {
    interface Window extends MatomoAnalyticsApi {
    }
}
export declare function useScriptMatomoAnalytics<T extends MatomoAnalyticsApi>(_options?: MatomoAnalyticsInput): import("#nuxt-scripts/types").UseScriptContext<T>;
export {};
