import * as nuxt_schema from 'nuxt/schema';
import { FetchOptions } from 'ofetch';
import { NuxtConfigScriptRegistry, NuxtUseScriptOptionsSerializable, NuxtUseScriptInput, RegistryScripts } from '../dist/runtime/types.js';

interface ModuleOptions {
    /**
     * The registry of supported third-party scripts. Loads the scripts in globally using the default script options.
     */
    registry?: NuxtConfigScriptRegistry;
    /**
     * Default options for scripts.
     */
    defaultScriptOptions?: NuxtUseScriptOptionsSerializable;
    /**
     * Register scripts that should be loaded globally on all pages.
     */
    globals?: Record<string, NuxtUseScriptInput | [NuxtUseScriptInput, NuxtUseScriptOptionsSerializable]>;
    /** Configure the way scripts assets are exposed */
    assets?: {
        /**
         * The baseURL where scripts files are served.
         * @default '/_scripts/'
         */
        prefix?: string;
        /**
         * Scripts assets are exposed as public assets as part of the build.
         *
         * TODO Make configurable in future.
         */
        strategy?: 'public';
        /**
         * Fallback to src if bundle fails to load.
         * The default behavior is to stop the bundling process if a script fails to be downloaded.
         * @default false
         */
        fallbackOnSrcOnBundleFail?: boolean;
        /**
         * Configure the fetch options used for downloading scripts.
         */
        fetchOptions?: FetchOptions;
    };
    /**
     * Whether the module is enabled.
     *
     * @default true
     */
    enabled: boolean;
    /**
     * Enables debug mode.
     *
     * @false false
     */
    debug: boolean;
}
interface ModuleHooks {
    'scripts:registry': (registry: RegistryScripts) => void | Promise<void>;
}
declare const _default: nuxt_schema.NuxtModule<ModuleOptions, ModuleOptions, false>;

export { _default as default };
export type { ModuleHooks, ModuleOptions };
