{"name": "valibot", "description": "The modular and type safe schema library for validating structural data", "version": "1.1.0", "license": "MIT", "author": "<PERSON>", "homepage": "https://valibot.dev", "repository": {"type": "git", "url": "https://github.com/fabian-hiller/valibot"}, "keywords": ["modular", "typescript", "schema", "validation", "parsing", "bundle-size", "type-safe", "runtime"], "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "devDependencies": {"@eslint/js": "^9.21.0", "@vitest/coverage-v8": "^3.0.7", "eslint": "^9.21.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-redos-detector": "^3.1.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-security": "^3.0.1", "jsdom": "^26.0.0", "tsm": "^2.3.0", "tsup": "^8.4.0", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "vite": "^6.2.0", "vitest": "3.0.7"}, "peerDependencies": {"typescript": ">=5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "scripts": {"play": "tsm ./playground.ts", "test": "vitest --typecheck", "coverage": "vitest run --coverage --isolate", "lint": "eslint \"src/**/*.ts*\" && tsc --noEmit && deno check ./src/index.ts", "lint.fix": "eslint \"src/**/*.ts*\" --fix", "format": "prettier --write ./src", "format.check": "prettier --check ./src", "build": "tsup"}}