var P;function uu(t){P={...P,...t}}function w(t){return{lang:t?.lang??P?.lang,message:t?.message,abortEarly:t?.abortEarly??P?.abortEarly,abortPipeEarly:t?.abortPipeEarly??P?.abortPipeEarly}}function ru(){P=void 0}var q;function au(t,n){q||(q=new Map),q.set(n,t)}function J(t){return q?.get(t)}function iu(t){q?.delete(t)}var _;function pu(t,n){_||(_=new Map),_.set(n,t)}function X(t){return _?.get(t)}function mu(t){_?.delete(t)}var b;function Tu(t,n,e){b||(b=new Map),b.get(t)||b.set(t,new Map),b.get(t).set(e,n)}function H(t,n){return b?.get(t)?.get(n)}function du(t,n){b?.get(t)?.delete(n)}function f(t){let n=typeof t;return n==="string"?`"${t}"`:n==="number"||n==="bigint"||n==="boolean"?`${t}`:n==="object"||n==="function"?(t&&Object.getPrototypeOf(t)?.constructor?.name)??"null":n}function r(t,n,e,s,u){let o=u&&"input"in u?u.input:e.value,a=u?.expected??t.expects??null,I=u?.received??f(o),i={kind:t.kind,type:t.type,input:o,expected:a,received:I,message:`Invalid ${n}: ${a?`Expected ${a} but r`:"R"}eceived ${I}`,requirement:t.requirement,path:u?.path,issues:u?.issues,lang:s.lang,abortEarly:s.abortEarly,abortPipeEarly:s.abortPipeEarly},m=t.kind==="schema",c=u?.message??t.message??H(t.reference,i.lang)??(m?X(i.lang):null)??s.message??J(i.lang);c!==void 0&&(i.message=typeof c=="function"?c(i):c),m&&(e.typed=!1),e.issues?e.issues.push(i):e.issues=[i]}var L;function A(t){return L||(L=new TextEncoder),L.encode(t).length}var K;function E(t){K||(K=new Intl.Segmenter);let n=K.segment(t),e=0;for(let s of n)e++;return e}function W(t,n){if("pipe"in t){let e=[];for(let s=t.pipe.length-1;s>=0;s--){let u=t.pipe[s];if(u.kind==="schema"&&"pipe"in u)e.push(u);else if(u.kind==="metadata"&&u.type===n)return u[n]}for(let s of e){let u=W(s,n);if(u!==void 0)return u}}}function p(t){return{version:1,vendor:"valibot",validate(n){return t["~run"]({value:n},w())}}}var D;function M(t,n){D||(D=new Map),D.get(t)||D.set(t,new Intl.Segmenter(t,{granularity:"word"}));let e=D.get(t).segment(n),s=0;for(let u of e)u.isWordLike&&s++;return s}var ve=/\D/gu;function V(t){let n=t.replace(ve,""),e=n.length,s=1,u=0;for(;e;){let o=+n[--e];s^=1,u+=s?[0,2,4,6,8,1,3,5,7,9][o]:o}return u%10===0}function O(t,n){return Object.hasOwn(t,n)&&n!=="__proto__"&&n!=="prototype"&&n!=="constructor"}function k(t,n){let e=[...new Set(t)];return e.length>1?`(${e.join(` ${n} `)})`:e[0]??"never"}function bu(t,n){let e={};for(let s of t)e[s]=n;return e}function Ru(t){let n={};for(let e of t)Object.assign(n,e.entries);return n}function N(t){if(t.path){let n="";for(let e of t.path)if(typeof e.key=="string"||typeof e.key=="number")n?n+=`.${e.key}`:n+=e.key;else return null;return n}return null}function qu(t,n){return n.kind===t}function Wu(t,n){return n.type===t}function Nu(t){return t instanceof g}var g=class extends Error{constructor(n){super(n[0].message),this.name="ValiError",this.issues=n}};function qe(t){return{kind:"transformation",type:"args",reference:qe,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:u},e);if(o.issues)throw new g(o.issues);return s(...o.value)},n}}}function _e(t){return{kind:"transformation",type:"args",reference:_e,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await t["~run"]({value:u},e);if(o.issues)throw new g(o.issues);return s(...o.value)},n}}}function We(){return{kind:"transformation",type:"await",reference:We,async:!0,async"~run"(t){return t.value=await t.value,t}}}var Z=/^(?:[\da-z+/]{4})*(?:[\da-z+/]{2}==|[\da-z+/]{3}=)?$/iu,Q=/^[A-Z]{6}(?!00)[\dA-Z]{2}(?:[\dA-Z]{3})?$/u,Y=/^[a-z][\da-z]*$/u,ee=/^[+-]?(?:\d*\.)?\d+$/u,ne=/^\d+$/u,te=/^[\w+-]+(?:\.[\w+-]+)*@[\da-z]+(?:[.-][\da-z]+)*\.[a-z]{2,}$/iu,se=/^(?:[\u{1F1E6}-\u{1F1FF}]{2}|\u{1F3F4}[\u{E0061}-\u{E007A}]{2}[\u{E0030}-\u{E0039}\u{E0061}-\u{E007A}]{1,3}\u{E007F}|(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation})(?:\u200D(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation}))*)+$/u,ue=/^(?:0[hx])?[\da-fA-F]+$/u,re=/^#(?:[\da-fA-F]{3,4}|[\da-fA-F]{6}|[\da-fA-F]{8})$/u,oe=/^\d{15}$|^\d{2}-\d{6}-\d{6}-\d$/u,ae=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$/u,ie=/^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,Ie=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$|^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,pe=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])$/u,me=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3]):[0-5]\d$/u,ce=/^(?:0\d|1\d|2[0-3]):[0-5]\d$/u,Te=/^(?:0\d|1\d|2[0-3])(?::[0-5]\d){2}$/u,de=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3])(?::[0-5]\d){2}(?:\.\d{1,9})?(?:Z|[+-](?:0\d|1\d|2[0-3])(?::?[0-5]\d)?)$/u,fe=/^\d{4}-W(?:0[1-9]|[1-4]\d|5[0-3])$/u,ye=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$/iu,le=/^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,he=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$|^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,ke=/^[\w-]+$/u,xe=/^(?:0o)?[0-7]+$/u,ge=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Oe=/^[\da-z]+(?:[-_][\da-z]+)*$/u,we=/^[\da-hjkmnp-tv-zA-HJKMNP-TV-Z]{26}$/u,Se=/^[\da-f]{8}(?:-[\da-f]{4}){3}-[\da-f]{12}$/iu;function De(t){return{kind:"validation",type:"base64",reference:De,async:!1,expects:null,requirement:Z,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Base64",n,e),n}}}function Ve(t){return{kind:"validation",type:"bic",reference:Ve,async:!1,expects:null,requirement:Q,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"BIC",n,e),n}}}function Ne(t){return{kind:"transformation",type:"brand",reference:Ne,async:!1,name:t,"~run"(n){return n}}}function Ce(t,n){return{kind:"validation",type:"bytes",reference:Ce,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u!==this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Le(t,n){return{kind:"validation",type:"check",reference:Le,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement(e.value)&&r(this,"input",e,s),e}}}function Ke(t,n){return{kind:"validation",type:"check",reference:Ke,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){return e.typed&&!await this.requirement(e.value)&&r(this,"input",e,s),e}}}function $e(t,n){return{kind:"validation",type:"check_items",reference:$e,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){if(e.typed)for(let u=0;u<e.value.length;u++){let o=e.value[u];this.requirement(o,u,e.value)||r(this,"item",e,s,{input:o,path:[{type:"array",origin:"value",input:e.value,key:u,value:o}]})}return e}}}function Fe(t,n){return{kind:"validation",type:"check_items",reference:Fe,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){if(e.typed){let u=await Promise.all(e.value.map(this.requirement));for(let o=0;o<e.value.length;o++)if(!u[o]){let a=e.value[o];r(this,"item",e,s,{input:a,path:[{type:"array",origin:"value",input:e.value,key:o,value:a}]})}}return e}}}var ze=/^(?:\d{14,19}|\d{4}(?: \d{3,6}){2,4}|\d{4}(?:-\d{3,6}){2,4})$/u,Ge=/[- ]/gu,Ue=[/^3[47]\d{13}$/u,/^3(?:0[0-5]|[68]\d)\d{11,13}$/u,/^6(?:011|5\d{2})\d{12,15}$/u,/^(?:2131|1800|35\d{3})\d{11}$/u,/^5[1-5]\d{2}|(?:222\d|22[3-9]\d|2[3-6]\d{2}|27[01]\d|2720)\d{12}$/u,/^(?:6[27]\d{14,17}|81\d{14,17})$/u,/^4\d{12}(?:\d{3,6})?$/u];function Je(t){return{kind:"validation",type:"credit_card",reference:Je,async:!1,expects:null,requirement(n){let e;return ze.test(n)&&(e=n.replace(Ge,""))&&Ue.some(s=>s.test(e))&&V(e)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"credit card",n,e),n}}}function Xe(t){return{kind:"validation",type:"cuid2",reference:Xe,async:!1,expects:null,requirement:Y,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Cuid2",n,e),n}}}function He(t){return{kind:"validation",type:"decimal",reference:He,async:!1,expects:null,requirement:ee,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"decimal",n,e),n}}}function Ze(t){return{kind:"metadata",type:"description",reference:Ze,description:t}}function Qe(t){return{kind:"validation",type:"digits",reference:Qe,async:!1,expects:null,requirement:ne,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"digits",n,e),n}}}function Ye(t){return{kind:"validation",type:"email",reference:Ye,expects:null,async:!1,requirement:te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function en(t){return{kind:"validation",type:"emoji",reference:en,async:!1,expects:null,requirement:se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"emoji",n,e),n}}}function nn(t){return{kind:"validation",type:"empty",reference:nn,async:!1,expects:"0",message:t,"~run"(n,e){return n.typed&&n.value.length>0&&r(this,"length",n,e,{received:`${n.value.length}`}),n}}}function tn(t,n){return{kind:"validation",type:"ends_with",reference:tn,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.endsWith(this.requirement)&&r(this,"end",e,s,{received:`"${e.value.slice(-this.requirement.length)}"`}),e}}}function sn(t,n){return{kind:"validation",type:"entries",reference:sn,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u!==this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function un(t,n){return{kind:"validation",type:"every_item",reference:un,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.every(this.requirement)&&r(this,"item",e,s),e}}}function rn(t,n){let e=f(t);return{kind:"validation",type:"excludes",reference:rn,async:!1,expects:`!${e}`,requirement:t,message:n,"~run"(s,u){return s.typed&&s.value.includes(this.requirement)&&r(this,"content",s,u,{received:e}),s}}}function on(t){return{kind:"transformation",type:"filter_items",reference:on,async:!1,operation:t,"~run"(n){return n.value=n.value.filter(this.operation),n}}}function an(t){return{kind:"transformation",type:"find_item",reference:an,async:!1,operation:t,"~run"(n){return n.value=n.value.find(this.operation),n}}}function In(t){return{kind:"validation",type:"finite",reference:In,async:!1,expects:null,requirement:Number.isFinite,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"finite",n,e),n}}}function pn(t){return{kind:"transformation",type:"flavor",reference:pn,async:!1,name:t,"~run"(n){return n}}}function mn(t,n){return{kind:"validation",type:"graphemes",reference:mn,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u!==this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function cn(t,n){return{kind:"validation",type:"gt_value",reference:cn,async:!1,expects:`>${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}var Tn={md4:32,md5:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8,adler32:8};function dn(t,n){return{kind:"validation",type:"hash",reference:dn,expects:null,async:!1,requirement:RegExp(t.map(e=>`^[a-f0-9]{${Tn[e]}}$`).join("|"),"iu"),message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"hash",e,s),e}}}function fn(t){return{kind:"validation",type:"hexadecimal",reference:fn,async:!1,expects:null,requirement:ue,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hexadecimal",n,e),n}}}function yn(t){return{kind:"validation",type:"hex_color",reference:yn,async:!1,expects:null,requirement:re,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hex color",n,e),n}}}function ln(t){return{kind:"validation",type:"imei",reference:ln,async:!1,expects:null,requirement(n){return oe.test(n)&&V(n)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"IMEI",n,e),n}}}function hn(t,n){let e=f(t);return{kind:"validation",type:"includes",reference:hn,async:!1,expects:e,requirement:t,message:n,"~run"(s,u){return s.typed&&!s.value.includes(this.requirement)&&r(this,"content",s,u,{received:`!${e}`}),s}}}function kn(t){return{kind:"validation",type:"integer",reference:kn,async:!1,expects:null,requirement:Number.isInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"integer",n,e),n}}}function xn(t){return{kind:"validation",type:"ip",reference:xn,async:!1,expects:null,requirement:Ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IP",n,e),n}}}function gn(t){return{kind:"validation",type:"ipv4",reference:gn,async:!1,expects:null,requirement:ae,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv4",n,e),n}}}function On(t){return{kind:"validation",type:"ipv6",reference:On,async:!1,expects:null,requirement:ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv6",n,e),n}}}function wn(t){return{kind:"validation",type:"iso_date",reference:wn,async:!1,expects:null,requirement:pe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date",n,e),n}}}function Sn(t){return{kind:"validation",type:"iso_date_time",reference:Sn,async:!1,expects:null,requirement:me,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date-time",n,e),n}}}function Bn(t){return{kind:"validation",type:"iso_time",reference:Bn,async:!1,expects:null,requirement:ce,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time",n,e),n}}}function An(t){return{kind:"validation",type:"iso_time_second",reference:An,async:!1,expects:null,requirement:Te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time-second",n,e),n}}}function En(t){return{kind:"validation",type:"iso_timestamp",reference:En,async:!1,expects:null,requirement:de,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"timestamp",n,e),n}}}function Mn(t){return{kind:"validation",type:"iso_week",reference:Mn,async:!1,expects:null,requirement:fe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"week",n,e),n}}}function bn(t,n){return{kind:"validation",type:"length",reference:bn,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length!==this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Pn(t,n){return{kind:"validation",type:"lt_value",reference:Pn,async:!1,expects:`<${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Rn(t){return{kind:"validation",type:"mac",reference:Rn,async:!1,expects:null,requirement:he,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"MAC",n,e),n}}}function jn(t){return{kind:"validation",type:"mac48",reference:jn,async:!1,expects:null,requirement:ye,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"48-bit MAC",n,e),n}}}function vn(t){return{kind:"validation",type:"mac64",reference:vn,async:!1,expects:null,requirement:le,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"64-bit MAC",n,e),n}}}function qn(t){return{kind:"transformation",type:"map_items",reference:qn,async:!1,operation:t,"~run"(n){return n.value=n.value.map(this.operation),n}}}function _n(t,n){return{kind:"validation",type:"max_bytes",reference:_n,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u>this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Wn(t,n){return{kind:"validation",type:"max_entries",reference:Wn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u>this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function Dn(t,n){return{kind:"validation",type:"max_graphemes",reference:Dn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u>this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Vn(t,n){return{kind:"validation",type:"max_length",reference:Vn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length>this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Nn(t,n){return{kind:"validation",type:"max_size",reference:Nn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size>this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Cn(t,n){return{kind:"validation",type:"max_value",reference:Cn,async:!1,expects:`<=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Ln(t,n,e){return{kind:"validation",type:"max_words",reference:Ln,async:!1,expects:`<=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o>this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Kn(t){return{kind:"metadata",type:"metadata",reference:Kn,metadata:t}}function $n(t,n){return{kind:"validation",type:"mime_type",reference:$n,async:!1,expects:k(t.map(e=>`"${e}"`),"|"),requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.includes(e.value.type)&&r(this,"MIME type",e,s,{received:`"${e.value.type}"`}),e}}}function Fn(t,n){return{kind:"validation",type:"min_bytes",reference:Fn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u<this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function zn(t,n){return{kind:"validation",type:"min_entries",reference:zn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u<this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function Gn(t,n){return{kind:"validation",type:"min_graphemes",reference:Gn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u<this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function Un(t,n){return{kind:"validation",type:"min_length",reference:Un,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length<this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Jn(t,n){return{kind:"validation",type:"min_size",reference:Jn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size<this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Xn(t,n){return{kind:"validation",type:"min_value",reference:Xn,async:!1,expects:`>=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Hn(t,n,e){return{kind:"validation",type:"min_words",reference:Hn,async:!1,expects:`>=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o<this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Zn(t,n){return{kind:"validation",type:"multiple_of",reference:Zn,async:!1,expects:`%${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value%this.requirement!=0&&r(this,"multiple",e,s),e}}}function Qn(t){return{kind:"validation",type:"nanoid",reference:Qn,async:!1,expects:null,requirement:ke,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Nano ID",n,e),n}}}function Yn(t){return{kind:"validation",type:"non_empty",reference:Yn,async:!1,expects:"!0",message:t,"~run"(n,e){return n.typed&&n.value.length===0&&r(this,"length",n,e,{received:"0"}),n}}}function et(t){return{kind:"transformation",type:"normalize",reference:et,async:!1,form:t,"~run"(n){return n.value=n.value.normalize(this.form),n}}}function nt(t,n){return{kind:"validation",type:"not_bytes",reference:nt,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u===this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function tt(t,n){return{kind:"validation",type:"not_entries",reference:tt,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u===this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function st(t,n){return{kind:"validation",type:"not_graphemes",reference:st,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u===this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function ut(t,n){return{kind:"validation",type:"not_length",reference:ut,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length===this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function rt(t,n){return{kind:"validation",type:"not_size",reference:rt,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size===this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function ot(t,n){return{kind:"validation",type:"not_value",reference:ot,async:!1,expects:t instanceof Date?`!${t.toJSON()}`:`!${f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement<=e.value&&this.requirement>=e.value&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function at(t,n){return{kind:"validation",type:"not_values",reference:at,async:!1,expects:`!${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function it(t,n,e){return{kind:"validation",type:"not_words",reference:it,async:!1,expects:`!${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o===this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function It(t){return{kind:"validation",type:"octal",reference:It,async:!1,expects:null,requirement:xe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"octal",n,e),n}}}function pt(t,n){return{kind:"transformation",type:"parse_json",reference:pt,config:t,message:n,async:!1,"~run"(e,s){try{e.value=JSON.parse(e.value,this.config?.reviver)}catch(u){if(u instanceof Error)r(this,"JSON",e,s,{received:`"${u.message}"`}),e.typed=!1;else throw u}return e}}}function C(t,n){if(t.issues)for(let e of n)for(let s of t.issues){let u=!1,o=Math.min(e.length,s.path?.length??0);for(let a=0;a<o;a++)if(e[a]!==s.path[a].key&&(e[a]!=="$"||s.path[a].type!=="array")){u=!0;break}if(!u)return!1}return!0}function mt(t,n,e){return{kind:"validation",type:"partial_check",reference:mt,async:!1,expects:null,paths:t,requirement:n,message:e,"~run"(s,u){return(s.typed||C(s,t))&&!this.requirement(s.value)&&r(this,"input",s,u),s}}}function ct(t,n,e){return{kind:"validation",type:"partial_check",reference:ct,async:!0,expects:null,paths:t,requirement:n,message:e,async"~run"(s,u){return(s.typed||C(s,t))&&!await this.requirement(s.value)&&r(this,"input",s,u),s}}}function Tt(t){return{kind:"validation",type:"raw_check",reference:Tt,async:!1,expects:null,"~run"(n,e){return t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function dt(t){return{kind:"validation",type:"raw_check",reference:dt,async:!0,expects:null,async"~run"(n,e){return await t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function ft(t){return{kind:"transformation",type:"raw_transform",reference:ft,async:!1,"~run"(n,e){let s=t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function yt(t){return{kind:"transformation",type:"raw_transform",reference:yt,async:!0,async"~run"(n,e){let s=await t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function lt(){return{kind:"transformation",type:"readonly",reference:lt,async:!1,"~run"(t){return t}}}function ht(t,n){return{kind:"transformation",type:"reduce_items",reference:ht,async:!1,operation:t,initial:n,"~run"(e){return e.value=e.value.reduce(this.operation,this.initial),e}}}function kt(t,n){return{kind:"validation",type:"regex",reference:kt,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"format",e,s),e}}}function xt(t){return{kind:"transformation",type:"returns",reference:xt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:s(...u)},e);if(o.issues)throw new g(o.issues);return o.value},n}}}function gt(t){return{kind:"transformation",type:"returns",reference:gt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await this.schema["~run"]({value:await s(...u)},e);if(o.issues)throw new g(o.issues);return o.value},n}}}function Ot(t){return{kind:"validation",type:"rfc_email",reference:Ot,expects:null,async:!1,requirement:ge,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function wt(t){return{kind:"validation",type:"safe_integer",reference:wt,async:!1,expects:null,requirement:Number.isSafeInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"safe integer",n,e),n}}}function St(t,n){return{kind:"validation",type:"size",reference:St,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size!==this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Bt(t){return{kind:"validation",type:"slug",reference:Bt,async:!1,expects:null,requirement:Oe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"slug",n,e),n}}}function At(t,n){return{kind:"validation",type:"some_item",reference:At,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.some(this.requirement)&&r(this,"item",e,s),e}}}function Et(t){return{kind:"transformation",type:"sort_items",reference:Et,async:!1,operation:t,"~run"(n){return n.value=n.value.sort(this.operation),n}}}function Mt(t,n){return{kind:"validation",type:"starts_with",reference:Mt,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.startsWith(this.requirement)&&r(this,"start",e,s,{received:`"${e.value.slice(0,this.requirement.length)}"`}),e}}}function bt(t,n){return{kind:"transformation",type:"stringify_json",reference:bt,message:n,config:t,async:!1,"~run"(e,s){try{let u=JSON.stringify(e.value,this.config?.replacer,this.config?.space);u===void 0&&(r(this,"JSON",e,s),e.typed=!1),e.value=u}catch(u){if(u instanceof Error)r(this,"JSON",e,s,{received:`"${u.message}"`}),e.typed=!1;else throw u}return e}}}function Pt(t){return{kind:"metadata",type:"title",reference:Pt,title:t}}function Rt(){return{kind:"transformation",type:"to_lower_case",reference:Rt,async:!1,"~run"(t){return t.value=t.value.toLowerCase(),t}}}function jt(t){return{kind:"transformation",type:"to_max_value",reference:jt,async:!1,requirement:t,"~run"(n){return n.value=n.value>this.requirement?this.requirement:n.value,n}}}function vt(t){return{kind:"transformation",type:"to_min_value",reference:vt,async:!1,requirement:t,"~run"(n){return n.value=n.value<this.requirement?this.requirement:n.value,n}}}function qt(){return{kind:"transformation",type:"to_upper_case",reference:qt,async:!1,"~run"(t){return t.value=t.value.toUpperCase(),t}}}function _t(t){return{kind:"transformation",type:"transform",reference:_t,async:!1,operation:t,"~run"(n){return n.value=this.operation(n.value),n}}}function Wt(t){return{kind:"transformation",type:"transform",reference:Wt,async:!0,operation:t,async"~run"(n){return n.value=await this.operation(n.value),n}}}function Dt(){return{kind:"transformation",type:"trim",reference:Dt,async:!1,"~run"(t){return t.value=t.value.trim(),t}}}function Vt(){return{kind:"transformation",type:"trim_end",reference:Vt,async:!1,"~run"(t){return t.value=t.value.trimEnd(),t}}}function Nt(){return{kind:"transformation",type:"trim_start",reference:Nt,async:!1,"~run"(t){return t.value=t.value.trimStart(),t}}}function Ct(t){return{kind:"validation",type:"ulid",reference:Ct,async:!1,expects:null,requirement:we,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"ULID",n,e),n}}}function Lt(t){return{kind:"validation",type:"url",reference:Lt,async:!1,expects:null,requirement(n){try{return new URL(n),!0}catch{return!1}},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"URL",n,e),n}}}function Kt(t){return{kind:"validation",type:"uuid",reference:Kt,async:!1,expects:null,requirement:Se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"UUID",n,e),n}}}function $t(t,n){return{kind:"validation",type:"value",reference:$t,async:!1,expects:t instanceof Date?t.toJSON():f(t),requirement:t,message:n,"~run"(e,s){return e.typed&&!(this.requirement<=e.value&&this.requirement>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Ft(t,n){return{kind:"validation",type:"values",reference:Ft,async:!1,expects:`${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function zt(t,n,e){return{kind:"validation",type:"words",reference:zt,async:!1,expects:`${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o!==this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function dI(t,n){let e=t["~run"]({value:n},{abortEarly:!0}).issues;if(e)throw new g(e)}function lI(t,n){return{...t,get"~standard"(){return p(this)},"~run"(e,s){return t["~run"](e,{...s,...n})}}}function l(t,n,e){return typeof t.fallback=="function"?t.fallback(n,e):t.fallback}function OI(t,n){return{...t,fallback:n,get"~standard"(){return p(this)},"~run"(e,s){let u=t["~run"](e,s);return u.issues?{typed:!0,value:l(this,u,s)}:u}}}function AI(t,n){return{...t,fallback:n,async:!0,get"~standard"(){return p(this)},async"~run"(e,s){let u=await t["~run"](e,s);return u.issues?{typed:!0,value:await l(this,u,s)}:u}}}function bI(t){let n={};for(let e of t)if(e.path){let s=N(e);s?(n.nested||(n.nested={}),n.nested[s]?n.nested[s].push(e.message):n.nested[s]=[e.message]):n.other?n.other.push(e.message):n.other=[e.message]}else n.root?n.root.push(e.message):n.root=[e.message];return n}function RI(t,n){return{...t,"~run"(e,s){let u=e.issues&&[...e.issues];if(e=t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function vI(t,n){return{...t,async:!0,async"~run"(e,s){let u=e.issues&&[...e.issues];if(e=await t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function y(t,n,e){return typeof t.default=="function"?t.default(n,e):t.default}function Be(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Be(t.entries[e]);return n}return"items"in t?t.items.map(Be):y(t)}async function Ae(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await Ae(e)]))):"items"in t?Promise.all(t.items.map(Ae)):y(t)}function LI(t){return W(t,"description")}function Ee(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Ee(t.entries[e]);return n}return"items"in t?t.items.map(Ee):l(t)}async function Me(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await Me(e)]))):"items"in t?Promise.all(t.items.map(Me)):l(t)}function UI(t){let n={};function e(s){if("pipe"in s)for(let u of s.pipe)u.kind==="schema"&&"pipe"in u?e(u):u.kind==="metadata"&&u.type==="metadata"&&Object.assign(n,u.metadata)}return e(t),n}function HI(t){return W(t,"title")}function QI(t,n){return!t["~run"]({value:n},{abortEarly:!0}).issues}function Gt(){return{kind:"schema",type:"any",reference:Gt,expects:"any",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function Ut(t,n){return{kind:"schema",type:"array",reference:Ut,expects:"Array",async:!1,item:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<u.length;o++){let a=u[o],I=this.item["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Jt(t,n){return{kind:"schema",type:"array",reference:Jt,expects:"Array",async:!0,item:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(u.map(a=>this.item["~run"]({value:a},s)));for(let a=0;a<o.length;a++){let I=o[a];if(I.issues){let i={type:"array",origin:"value",input:u,key:a,value:u[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Xt(t){return{kind:"schema",type:"bigint",reference:Xt,expects:"bigint",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="bigint"?n.typed=!0:r(this,"type",n,e),n}}}function Ht(t){return{kind:"schema",type:"blob",reference:Ht,expects:"Blob",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Blob?n.typed=!0:r(this,"type",n,e),n}}}function Zt(t){return{kind:"schema",type:"boolean",reference:Zt,expects:"boolean",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="boolean"?n.typed=!0:r(this,"type",n,e),n}}}function Qt(t,n){return{kind:"schema",type:"custom",reference:Qt,expects:"unknown",async:!1,check:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Yt(t,n){return{kind:"schema",type:"custom",reference:Yt,expects:"unknown",async:!0,check:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return await this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function es(t){return{kind:"schema",type:"date",reference:es,expects:"Date",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Date?isNaN(n.value)?r(this,"type",n,e,{received:'"Invalid Date"'}):n.typed=!0:r(this,"type",n,e),n}}}function ns(t,n){let e=[];for(let s in t)(`${+s}`!==s||typeof t[s]!="string"||!Object.is(t[t[s]],+s))&&e.push(t[s]);return{kind:"schema",type:"enum",reference:ns,expects:k(e.map(f),"|"),async:!1,enum:t,options:e,message:n,get"~standard"(){return p(this)},"~run"(s,u){return this.options.includes(s.value)?s.typed=!0:r(this,"type",s,u),s}}}function ts(t,n){return{kind:"schema",type:"exact_optional",reference:ts,expects:t.expects,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return this.wrapped["~run"](e,s)}}}function ss(t,n){return{kind:"schema",type:"exact_optional",reference:ss,expects:t.expects,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return this.wrapped["~run"](e,s)}}}function us(t){return{kind:"schema",type:"file",reference:us,expects:"File",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof File?n.typed=!0:r(this,"type",n,e),n}}}function rs(t){return{kind:"schema",type:"function",reference:rs,expects:"Function",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="function"?n.typed=!0:r(this,"type",n,e),n}}}function os(t,n){return{kind:"schema",type:"instance",reference:os,expects:t.name,async:!1,class:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value instanceof this.class?e.typed=!0:r(this,"type",e,s),e}}}function R(t,n){if(typeof t==typeof n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{value:t};if(t&&n&&t.constructor===Object&&n.constructor===Object){for(let e in n)if(e in t){let s=R(t[e],n[e]);if(s.issue)return s;t[e]=s.value}else t[e]=n[e];return{value:t}}if(Array.isArray(t)&&Array.isArray(n)&&t.length===n.length){for(let e=0;e<t.length;e++){let s=R(t[e],n[e]);if(s.issue)return s;t[e]=s.value}return{value:t}}}return{issue:!0}}function as(t,n){return{kind:"schema",type:"intersect",reference:as,expects:k(t.map(e=>e.expects),"&"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;for(let a of this.options){let I=a["~run"]({value:u},s);if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let a=1;a<o.length;a++){let I=R(e.value,o[a]);if(I.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=I.value}}}else r(this,"type",e,s);return e}}}function is(t,n){return{kind:"schema",type:"intersect",reference:is,expects:k(t.map(e=>e.expects),"&"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;let a=await Promise.all(this.options.map(I=>I["~run"]({value:u},s)));for(let I of a){if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let I=1;I<o.length;I++){let i=R(e.value,o[I]);if(i.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=i.value}}}else r(this,"type",e,s);return e}}}function Is(t){return{kind:"schema",type:"lazy",reference:Is,expects:"unknown",async:!1,getter:t,get"~standard"(){return p(this)},"~run"(n,e){return this.getter(n.value)["~run"](n,e)}}}function ps(t){return{kind:"schema",type:"lazy",reference:ps,expects:"unknown",async:!0,getter:t,get"~standard"(){return p(this)},async"~run"(n,e){return(await this.getter(n.value))["~run"](n,e)}}}function ms(t,n){return{kind:"schema",type:"literal",reference:ms,expects:f(t),async:!1,literal:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===this.literal?e.typed=!0:r(this,"type",e,s),e}}}function cs(t,n){return{kind:"schema",type:"loose_object",reference:cs,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly)for(let o in u)O(u,o)&&!(o in this.entries)&&(e.value[o]=u[o])}else r(this,"type",e,s);return e}}}function Ts(t,n){return{kind:"schema",type:"loose_object",reference:Ts,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly)for(let a in u)O(u,a)&&!(a in this.entries)&&(e.value[a]=u[a])}else r(this,"type",e,s);return e}}}function ds(t,n){return{kind:"schema",type:"loose_tuple",reference:ds,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}if(!e.issues||!s.abortEarly)for(let o=this.items.length;o<u.length;o++)e.value.push(u[o])}else r(this,"type",e,s);return e}}}function fs(t,n){return{kind:"schema",type:"loose_tuple",reference:fs,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}if(!e.issues||!s.abortEarly)for(let a=this.items.length;a<u.length;a++)e.value.push(u[a])}else r(this,"type",e,s);return e}}}function ys(t,n,e){return{kind:"schema",type:"map",reference:ys,expects:"Map",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;for(let[a,I]of o){let i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"map",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"map",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),s.value.set(i.value,m.value)}}else r(this,"type",s,u);return s}}}function ls(t,n,e){return{kind:"schema",type:"map",reference:ls,expects:"Map",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;let a=await Promise.all([...o].map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"map",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"map",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),s.value.set(m.value,c.value)}}else r(this,"type",s,u);return s}}}function hs(t){return{kind:"schema",type:"nan",reference:hs,expects:"NaN",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return Number.isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function ks(t){return{kind:"schema",type:"never",reference:ks,expects:"never",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return r(this,"type",n,e),n}}}function xs(t,n){return{kind:"schema",type:"non_nullable",reference:xs,expects:"!null",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==null&&(e=this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function gs(t,n){return{kind:"schema",type:"non_nullable",reference:gs,expects:"!null",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==null&&(e=await this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function Os(t,n){return{kind:"schema",type:"non_nullish",reference:Os,expects:"(!null & !undefined)",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null||e.value===void 0||(e=this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function ws(t,n){return{kind:"schema",type:"non_nullish",reference:ws,expects:"(!null & !undefined)",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null||e.value===void 0||(e=await this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function $(t,n){return{kind:"schema",type:"non_optional",reference:$,expects:"!undefined",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==void 0&&(e=this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function F(t,n){return{kind:"schema",type:"non_optional",reference:F,expects:"!undefined",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==void 0&&(e=await this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function Ss(t){return{kind:"schema",type:"null",reference:Ss,expects:"null",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===null?n.typed=!0:r(this,"type",n,e),n}}}function Bs(t,n){return{kind:"schema",type:"nullable",reference:Bs,expects:`(${t.expects} | null)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function As(t,n){return{kind:"schema",type:"nullable",reference:As,expects:`(${t.expects} | null)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Es(t,n){return{kind:"schema",type:"nullish",reference:Es,expects:`(${t.expects} | null | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Ms(t,n){return{kind:"schema",type:"nullish",reference:Ms,expects:`(${t.expects} | null | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function bs(t){return{kind:"schema",type:"number",reference:bs,expects:"number",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="number"&&!isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function Ps(t,n){return{kind:"schema",type:"object",reference:Ps,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}}else r(this,"type",e,s);return e}}}function Rs(t,n){return{kind:"schema",type:"object",reference:Rs,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break}else r(this,"type",e,s);return e}}}function js(t,n,e){return{kind:"schema",type:"object_with_rest",reference:js,expects:"Object",async:!1,entries:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in this.entries){let I=this.entries[a];if(a in o||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in o?o[a]:y(I),m=I["~run"]({value:i},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:i};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}m.typed||(s.typed=!1),s.value[a]=m.value}else if(I.fallback!==void 0)s.value[a]=l(I);else if(I.type!=="exact_optional"&&I.type!=="optional"&&I.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:o,key:a,value:o[a]}]}),u.abortEarly))break}if(!s.issues||!u.abortEarly){for(let a in o)if(O(o,a)&&!(a in this.entries)){let I=this.rest["~run"]({value:o[a]},u);if(I.issues){let i={type:"object",origin:"value",input:o,key:a,value:o[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],s.issues?.push(m);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value[a]=I.value}}}else r(this,"type",s,u);return s}}}function vs(t,n,e){return{kind:"schema",type:"object_with_rest",reference:vs,expects:"Object",async:!0,entries:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let[a,I]=await Promise.all([Promise.all(Object.entries(this.entries).map(async([i,m])=>{if(i in o||(m.type==="exact_optional"||m.type==="optional"||m.type==="nullish")&&m.default!==void 0){let c=i in o?o[i]:await y(m);return[i,c,m,await m["~run"]({value:c},u)]}return[i,o[i],m,null]})),Promise.all(Object.entries(o).filter(([i])=>O(o,i)&&!(i in this.entries)).map(async([i,m])=>[i,m,await this.rest["~run"]({value:m},u)]))]);for(let[i,m,c,T]of a)if(T){if(T.issues){let d={type:"object",origin:"value",input:o,key:i,value:m};for(let x of T.issues)x.path?x.path.unshift(d):x.path=[d],s.issues?.push(x);if(s.issues||(s.issues=T.issues),u.abortEarly){s.typed=!1;break}}T.typed||(s.typed=!1),s.value[i]=T.value}else if(c.fallback!==void 0)s.value[i]=await l(c);else if(c.type!=="exact_optional"&&c.type!=="optional"&&c.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${i}"`,path:[{type:"object",origin:"key",input:o,key:i,value:m}]}),u.abortEarly))break;if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"object",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value[i]=c.value}}else r(this,"type",s,u);return s}}}function z(t,n){return{kind:"schema",type:"optional",reference:z,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function G(t,n){return{kind:"schema",type:"optional",reference:G,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function U(t,n){return{kind:"schema",type:"picklist",reference:U,expects:k(t.map(f),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.options.includes(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function qs(t){return{kind:"schema",type:"promise",reference:qs,expects:"Promise",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Promise?n.typed=!0:r(this,"type",n,e),n}}}function _s(t,n,e){return{kind:"schema",type:"record",reference:_s,expects:"Object",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in o)if(O(o,a)){let I=o[a],i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"object",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),i.typed&&(s.value[i.value]=m.value)}}else r(this,"type",s,u);return s}}}function Ws(t,n,e){return{kind:"schema",type:"record",reference:Ws,expects:"Object",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let a=await Promise.all(Object.entries(o).filter(([I])=>O(o,I)).map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"object",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"object",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),m.typed&&(s.value[m.value]=c.value)}}else r(this,"type",s,u);return s}}}function Ds(t,n){return{kind:"schema",type:"set",reference:Ds,expects:"Set",async:!1,value:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;for(let o of u){let a=this.value["~run"]({value:o},s);if(a.issues){let I={type:"set",origin:"value",input:u,key:null,value:o};for(let i of a.issues)i.path?i.path.unshift(I):i.path=[I],e.issues?.push(i);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.add(a.value)}}else r(this,"type",e,s);return e}}}function Vs(t,n){return{kind:"schema",type:"set",reference:Vs,expects:"Set",async:!0,value:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;let o=await Promise.all([...u].map(async a=>[a,await this.value["~run"]({value:a},s)]));for(let[a,I]of o){if(I.issues){let i={type:"set",origin:"value",input:u,key:null,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.add(I.value)}}else r(this,"type",e,s);return e}}}function Ns(t,n){return{kind:"schema",type:"strict_object",reference:Ns,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly){for(let o in u)if(!(o in this.entries)){r(this,"key",e,s,{input:o,expected:"never",path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]});break}}}else r(this,"type",e,s);return e}}}function Cs(t,n){return{kind:"schema",type:"strict_object",reference:Cs,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly){for(let a in u)if(!(a in this.entries)){r(this,"key",e,s,{input:a,expected:"never",path:[{type:"object",origin:"key",input:u,key:a,value:u[a]}]});break}}}else r(this,"type",e,s);return e}}}function Ls(t,n){return{kind:"schema",type:"strict_tuple",reference:Ls,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function Ks(t,n){return{kind:"schema",type:"strict_tuple",reference:Ks,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function $s(t){return{kind:"schema",type:"string",reference:$s,expects:"string",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="string"?n.typed=!0:r(this,"type",n,e),n}}}function Fs(t){return{kind:"schema",type:"symbol",reference:Fs,expects:"symbol",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="symbol"?n.typed=!0:r(this,"type",n,e),n}}}function zs(t,n){return{kind:"schema",type:"tuple",reference:zs,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Gs(t,n){return{kind:"schema",type:"tuple",reference:Gs,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}}else r(this,"type",e,s);return e}}}function Us(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Us,expects:"Array",async:!1,items:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];for(let a=0;a<this.items.length;a++){let I=o[a],i=this.items[a]["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}if(!s.issues||!u.abortEarly)for(let a=this.items.length;a<o.length;a++){let I=o[a],i=this.rest["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}}else r(this,"type",s,u);return s}}}function Js(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Js,expects:"Array",async:!0,items:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];let[a,I]=await Promise.all([Promise.all(this.items.map(async(i,m)=>{let c=o[m];return[m,c,await i["~run"]({value:c},u)]})),Promise.all(o.slice(this.items.length).map(async(i,m)=>[m+this.items.length,i,await this.rest["~run"]({value:i},u)]))]);for(let[i,m,c]of a){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}}else r(this,"type",s,u);return s}}}function Xs(t){return{kind:"schema",type:"undefined",reference:Xs,expects:"undefined",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function Hs(t,n){return{kind:"schema",type:"undefinedable",reference:Hs,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Zs(t,n){return{kind:"schema",type:"undefinedable",reference:Zs,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function j(t){let n;if(t)for(let e of t)n?n.push(...e.issues):n=e.issues;return n}function Qs(t,n){return{kind:"schema",type:"union",reference:Qs,expects:k(t.map(e=>e.expects),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u,o,a;for(let I of this.options){let i=I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:j(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:j(a)})}return e}}}function Ys(t,n){return{kind:"schema",type:"union",reference:Ys,expects:k(t.map(e=>e.expects),"|"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u,o,a;for(let I of this.options){let i=await I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:j(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:j(a)})}return e}}}function eu(){return{kind:"schema",type:"unknown",reference:eu,expects:"unknown",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function nu(t,n,e){return{kind:"schema",type:"variant",reference:nu,expects:"Object",async:!1,key:t,options:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=(T,d)=>{for(let x of T.options){if(x.type==="variant")c(x,new Set(d).add(x.key));else{let v=!0,S=0;for(let h of d){let B=x.entries[h];if(h in o?B["~run"]({typed:!1,value:o[h]},{abortEarly:!0}).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){v=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(x.entries[h].expects);break}S++}if(v){let h=x["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function tu(t,n,e){return{kind:"schema",type:"variant",reference:tu,expects:"Object",async:!0,key:t,options:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=async(T,d)=>{for(let x of T.options){if(x.type==="variant")await c(x,new Set(d).add(x.key));else{let v=!0,S=0;for(let h of d){let B=x.entries[h];if(h in o?(await B["~run"]({typed:!1,value:o[h]},{abortEarly:!0})).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){v=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(x.entries[h].expects);break}S++}if(v){let h=await x["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(await c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function su(t){return{kind:"schema",type:"void",reference:su,expects:"void",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function sT(t,n){return U(Object.keys(t.entries),n)}function oT(t,n){return{...t,get"~standard"(){return p(this)},"~run"(e,s){return t["~run"](e,{...s,message:n})}}}function IT(t,n){let e={...t.entries};for(let s of n)delete e[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function be(t,n,e){let s=t["~run"]({value:n},w(e));if(s.issues)throw new g(s.issues);return s.value}async function Pe(t,n,e){let s=await t["~run"]({value:n},w(e));if(s.issues)throw new g(s.issues);return s.value}function hT(t,n){let e=s=>be(t,s,n);return e.schema=t,e.config=n,e}function gT(t,n){let e=s=>Pe(t,s,n);return e.schema=t,e.config=n,e}function BT(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?z(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function bT(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?G(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function jT(t,n){let e={};for(let s of n)e[s]=t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function _T(...t){return{...t[0],pipe:t,get"~standard"(){return p(this)},"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=s["~run"](n,e))}return n}}}function VT(...t){return{...t[0],pipe:t,async:!0,get"~standard"(){return p(this)},async"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=await s["~run"](n,e))}return n}}}function KT(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?$(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function GT(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?F(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function Re(t,n,e){let s=t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}async function je(t,n,e){let s=await t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}function YT(t,n){let e=s=>Re(t,s,n);return e.schema=t,e.config=n,e}function td(t,n){let e=s=>je(t,s,n);return e.schema=t,e.config=n,e}function rd(t){let n="";for(let e of t){n&&(n+=`
`),n+=`\xD7 ${e.message}`;let s=N(e);s&&(n+=`
  \u2192 at ${s}`)}return n}function ad(t){return t.wrapped}export{Z as BASE64_REGEX,Q as BIC_REGEX,Y as CUID2_REGEX,ee as DECIMAL_REGEX,ne as DIGITS_REGEX,te as EMAIL_REGEX,se as EMOJI_REGEX,ue as HEXADECIMAL_REGEX,re as HEX_COLOR_REGEX,oe as IMEI_REGEX,ae as IPV4_REGEX,ie as IPV6_REGEX,Ie as IP_REGEX,pe as ISO_DATE_REGEX,me as ISO_DATE_TIME_REGEX,de as ISO_TIMESTAMP_REGEX,ce as ISO_TIME_REGEX,Te as ISO_TIME_SECOND_REGEX,fe as ISO_WEEK_REGEX,ye as MAC48_REGEX,le as MAC64_REGEX,he as MAC_REGEX,ke as NANO_ID_REGEX,xe as OCTAL_REGEX,ge as RFC_EMAIL_REGEX,Oe as SLUG_REGEX,we as ULID_REGEX,Se as UUID_REGEX,g as ValiError,r as _addIssue,A as _getByteCount,E as _getGraphemeCount,W as _getLastMetadata,p as _getStandardProps,M as _getWordCount,V as _isLuhnAlgo,O as _isValidObjectKey,k as _joinExpects,f as _stringify,Gt as any,qe as args,_e as argsAsync,Ut as array,Jt as arrayAsync,dI as assert,We as awaitAsync,De as base64,Ve as bic,Xt as bigint,Ht as blob,Zt as boolean,Ne as brand,Ce as bytes,Le as check,Ke as checkAsync,$e as checkItems,Fe as checkItemsAsync,lI as config,Je as creditCard,Xe as cuid2,Qt as custom,Yt as customAsync,es as date,He as decimal,ru as deleteGlobalConfig,iu as deleteGlobalMessage,mu as deleteSchemaMessage,du as deleteSpecificMessage,Ze as description,Qe as digits,Ye as email,en as emoji,nn as empty,tn as endsWith,sn as entries,bu as entriesFromList,Ru as entriesFromObjects,ns as enum,ns as enum_,un as everyItem,ts as exactOptional,ss as exactOptionalAsync,rn as excludes,OI as fallback,AI as fallbackAsync,us as file,on as filterItems,an as findItem,In as finite,bI as flatten,pn as flavor,RI as forward,vI as forwardAsync,rs as function,rs as function_,y as getDefault,Be as getDefaults,Ae as getDefaultsAsync,LI as getDescription,N as getDotPath,l as getFallback,Ee as getFallbacks,Me as getFallbacksAsync,w as getGlobalConfig,J as getGlobalMessage,UI as getMetadata,X as getSchemaMessage,H as getSpecificMessage,HI as getTitle,mn as graphemes,cn as gtValue,dn as hash,yn as hexColor,fn as hexadecimal,ln as imei,hn as includes,os as instance,kn as integer,as as intersect,is as intersectAsync,xn as ip,gn as ipv4,On as ipv6,QI as is,qu as isOfKind,Wu as isOfType,Nu as isValiError,wn as isoDate,Sn as isoDateTime,Bn as isoTime,An as isoTimeSecond,En as isoTimestamp,Mn as isoWeek,sT as keyof,Is as lazy,ps as lazyAsync,bn as length,ms as literal,cs as looseObject,Ts as looseObjectAsync,ds as looseTuple,fs as looseTupleAsync,Pn as ltValue,Rn as mac,jn as mac48,vn as mac64,ys as map,ls as mapAsync,qn as mapItems,_n as maxBytes,Wn as maxEntries,Dn as maxGraphemes,Vn as maxLength,Nn as maxSize,Cn as maxValue,Ln as maxWords,oT as message,Kn as metadata,$n as mimeType,Fn as minBytes,zn as minEntries,Gn as minGraphemes,Un as minLength,Jn as minSize,Xn as minValue,Hn as minWords,Zn as multipleOf,hs as nan,Qn as nanoid,ks as never,Yn as nonEmpty,xs as nonNullable,gs as nonNullableAsync,Os as nonNullish,ws as nonNullishAsync,$ as nonOptional,F as nonOptionalAsync,et as normalize,nt as notBytes,tt as notEntries,st as notGraphemes,ut as notLength,rt as notSize,ot as notValue,at as notValues,it as notWords,Ss as null,Ss as null_,Bs as nullable,As as nullableAsync,Es as nullish,Ms as nullishAsync,bs as number,Ps as object,Rs as objectAsync,js as objectWithRest,vs as objectWithRestAsync,It as octal,IT as omit,z as optional,G as optionalAsync,be as parse,Pe as parseAsync,pt as parseJson,hT as parser,gT as parserAsync,BT as partial,bT as partialAsync,mt as partialCheck,ct as partialCheckAsync,jT as pick,U as picklist,_T as pipe,VT as pipeAsync,qs as promise,Tt as rawCheck,dt as rawCheckAsync,ft as rawTransform,yt as rawTransformAsync,lt as readonly,_s as record,Ws as recordAsync,ht as reduceItems,kt as regex,KT as required,GT as requiredAsync,xt as returns,gt as returnsAsync,Ot as rfcEmail,wt as safeInteger,Re as safeParse,je as safeParseAsync,YT as safeParser,td as safeParserAsync,Ds as set,Vs as setAsync,uu as setGlobalConfig,au as setGlobalMessage,pu as setSchemaMessage,Tu as setSpecificMessage,St as size,Bt as slug,At as someItem,Et as sortItems,Mt as startsWith,Ns as strictObject,Cs as strictObjectAsync,Ls as strictTuple,Ks as strictTupleAsync,$s as string,bt as stringifyJson,rd as summarize,Fs as symbol,Pt as title,Rt as toLowerCase,jt as toMaxValue,vt as toMinValue,qt as toUpperCase,_t as transform,Wt as transformAsync,Dt as trim,Vt as trimEnd,Nt as trimStart,zs as tuple,Gs as tupleAsync,Us as tupleWithRest,Js as tupleWithRestAsync,Ct as ulid,Xs as undefined,Xs as undefined_,Hs as undefinedable,Zs as undefinedableAsync,Qs as union,Ys as unionAsync,eu as unknown,ad as unwrap,Lt as url,Kt as uuid,$t as value,Ft as values,nu as variant,tu as variantAsync,su as void,su as void_,zt as words};
