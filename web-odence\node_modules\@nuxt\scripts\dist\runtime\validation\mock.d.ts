export declare const parse: (() => void) & {
    __mock__: boolean;
};
export declare const object: (() => void) & {
    __mock__: boolean;
};
export declare const array: (() => void) & {
    __mock__: boolean;
};
export declare const string: (() => void) & {
    __mock__: boolean;
};
export declare const number: (() => void) & {
    __mock__: boolean;
};
export declare const boolean: (() => void) & {
    __mock__: boolean;
};
export declare const optional: (() => void) & {
    __mock__: boolean;
};
export declare const literal: (() => void) & {
    __mock__: boolean;
};
export declare const union: (() => void) & {
    __mock__: boolean;
};
export declare const record: (() => void) & {
    __mock__: boolean;
};
export declare const any: (() => void) & {
    __mock__: boolean;
};
export declare const minLength: (() => void) & {
    __mock__: boolean;
};
export declare const pipe: (() => void) & {
    __mock__: boolean;
};
export declare const custom: (() => void) & {
    __mock__: boolean;
};
