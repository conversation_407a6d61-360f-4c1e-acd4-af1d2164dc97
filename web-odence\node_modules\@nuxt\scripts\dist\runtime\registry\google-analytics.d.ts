import type { RegistryScriptInput } from '#nuxt-scripts/types';
export type GtagCustomParams = Record<string, any>;
export type ConsentStatus = 'granted' | 'denied';
export interface ConsentOptions {
    ad_user_data?: ConsentStatus;
    ad_personalization?: ConsentStatus;
    ad_storage?: ConsentStatus;
    analytics_storage?: ConsentStatus;
    functionality_storage?: ConsentStatus;
    personalization_storage?: ConsentStatus;
    security_storage?: ConsentStatus;
    wait_for_update?: number;
    region?: string[];
}
export interface ConfigParams extends GtagCustomParams {
    send_page_view?: boolean;
    transport_url?: string;
    cookie_domain?: string;
    cookie_prefix?: string;
    cookie_expires?: number;
    cookie_update?: boolean;
    cookie_flags?: string;
    user_id?: string;
}
export interface EventParameters extends GtagCustomParams {
    value?: number;
    currency?: string;
    transaction_id?: string;
    items?: Array<{
        item_id?: string;
        item_name?: string;
        item_category?: string;
        item_variant?: string;
        price?: number;
        quantity?: number;
        [key: string]: any;
    }>;
    [key: string]: any;
}
export type DefaultEventName = 'add_payment_info' | 'add_shipping_info' | 'add_to_cart' | 'add_to_wishlist' | 'begin_checkout' | 'purchase' | 'refund' | 'remove_from_cart' | 'select_item' | 'select_promotion' | 'view_cart' | 'view_item' | 'view_item_list' | 'view_promotion' | 'login' | 'sign_up' | 'search' | 'page_view' | 'screen_view' | string;
export interface GTag {
    (command: 'js', value: Date): void;
    (command: 'config', targetId: string, configParams?: ConfigParams): void;
    (command: 'get', targetId: string, fieldName: string, callback?: (field: any) => void): void;
    (command: 'event', eventName: DefaultEventName, eventParams?: EventParameters): void;
    (command: 'set', params: GtagCustomParams): void;
    (command: 'consent', consentArg: 'default' | 'update', consentParams: ConsentOptions): void;
}
export interface DataLayerObject {
    event?: string;
    [key: string]: any;
}
export type DataLayer = Array<DataLayerObject>;
export interface GoogleAnalyticsApi {
    gtag: GTag;
    dataLayer: DataLayer;
}
export declare const GoogleAnalyticsOptions: import("valibot").ObjectSchema<{
    readonly id: import("valibot").StringSchema<undefined>;
    readonly l: import("valibot").OptionalSchema<import("valibot").StringSchema<undefined>, undefined>;
}, undefined>;
export type GoogleAnalyticsInput = RegistryScriptInput<typeof GoogleAnalyticsOptions>;
export declare function useScriptGoogleAnalytics<T extends GoogleAnalyticsApi>(_options?: GoogleAnalyticsInput & {
    onBeforeGtagStart?: (gtag: GTag) => void;
}): import("#nuxt-scripts/types").UseScriptContext<T>;
