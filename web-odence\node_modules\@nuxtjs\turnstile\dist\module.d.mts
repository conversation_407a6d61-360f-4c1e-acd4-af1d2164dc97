import * as _nuxt_schema from '@nuxt/schema';

interface ModuleOptions {
    /** It is recommended you set the secret key via `runtimeConfig.turnstile.secretKey` or NUXT_TURNSTILE_SECRET_KEY */
    secretKey?: string;
    /** Path to a file containing the secret key. */
    secretKeyPath?: string;
    /** Your Turnstile site key */
    siteKey?: string;
    /**
     * Add a nitro endpoint at `/_turnstile/validate` to validate tokens.
     * @default {false}
     */
    addValidateEndpoint?: boolean;
}
declare const _default: _nuxt_schema.NuxtModule<ModuleOptions, ModuleOptions, false>;

export { _default as default };
export type { ModuleOptions };
