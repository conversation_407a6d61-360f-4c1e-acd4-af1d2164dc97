import type { ElementScriptTrigger } from '../types.js';
type __VLS_Props = {
    trigger?: ElementScriptTrigger;
};
declare var __VLS_1: {};
type __VLS_Slots = {} & {
    default?: (props: typeof __VLS_1) => any;
};
declare const __VLS_component: import("vue").DefineComponent<__VLS_Props, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    ready: (args_0: import("../types.js").UseScriptContext<import("../registry/lemon-squeezy.js").LemonSqueezyApi>) => any;
    lemonSqueezyEvent: (args_0: never) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    onReady?: ((args_0: import("../types.js").UseScriptContext<import("../registry/lemon-squeezy.js").LemonSqueezyApi>) => any) | undefined;
    onLemonSqueezyEvent?: ((args_0: never) => any) | undefined;
}>, {
    trigger: ElementScriptTrigger;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
