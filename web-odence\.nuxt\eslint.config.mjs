// ESLint config generated by Nuxt
/// <reference path="./eslint-typegen.d.ts" />
/* eslint-disable */
// @ts-nocheck

import typegen from '../node_modules/eslint-typegen/dist/index.mjs';
import { createConfigForNuxt, defineFlatConfigs, resolveOptions } from '../node_modules/@nuxt/eslint-config/dist/flat.mjs';
import { fileURLToPath } from 'node:url';

const r = (...args) => fileURLToPath(new URL(...args, import.meta.url))

export { defineFlatConfigs }

export const options = resolveOptions({
  features: {
  "standalone": true
},
  dirs: {
    pages: ["app/pages"],
    composables: ["app/composables", "app/utils"],
    components: ["app/components"],
    componentsPrefixed: [],
    layouts: ["app/layouts"],
    plugins: ["app/plugins"],
    middleware: ["app/middleware"],
    modules: ["modules"],
    servers: [],
    root: [],
    src: ["app"],
}
})

export const configs = createConfigForNuxt(options)

configs.append(
// Set globals from imports registry
{
  name: 'nuxt/import-globals',
  languageOptions: {
    globals: Object.fromEntries(["useTranslation","toTypedSchema","cancelIdleCallback","requestIdleCallback","setInterval","defineNuxtLink","clearNuxtData","refreshNuxtData","useAsyncData","useLazyAsyncData","useNuxtData","reloadNuxtApp","defineNuxtComponent","refreshCookie","useCookie","clearError","createError","isNuxtError","showError","useError","useFetch","useLazyFetch","injectHead","useHead","useHeadSafe","useSeoMeta","useServerHead","useServerHeadSafe","useServerSeoMeta","useHydration","defineLazyHydrationComponent","useLoadingIndicator","getAppManifest","getRouteRules","callOnce","definePayloadReducer","definePayloadReviver","isPrerendered","loadPayload","preloadPayload","prefetchComponents","preloadComponents","preloadRouteComponents","usePreviewMode","onNuxtReady","useRouteAnnouncer","abortNavigation","addRouteMiddleware","defineNuxtRouteMiddleware","navigateTo","setPageLayout","useRoute","useRouter","useRuntimeHook","onPrehydrate","prerenderRoutes","setResponseStatus","useRequestEvent","useRequestFetch","useRequestHeader","useRequestHeaders","useResponseHeader","clearNuxtState","useState","useRequestURL","updateAppConfig","useAppConfig","defineAppConfig","defineNuxtPlugin","definePayloadPlugin","tryUseNuxtApp","useNuxtApp","useRuntimeConfig","useScroll","getCookieLocale","getHeaderLanguage","getHeaderLanguages","getHeaderLocale","getHeaderLocales","getPathLocale","getQueryLocale","setCookieLocale","tryCookieLocale","tryHeaderLocale","tryHeaderLocales","tryPathLocale","tryQueryLocale","useImage","useImage","useScript","useScriptEventPage","useScriptTriggerConsent","useScriptTriggerElement","useScriptClarity","useScriptCloudflareWebAnalytics","useScriptCrisp","useScriptFathomAnalytics","useScriptGoogleAdsense","useScriptGoogleAnalytics","useScriptGoogleMaps","useScriptGoogleTagManager","useScriptHotjar","useScriptIntercom","useScriptLemonSqueezy","useScriptMatomoAnalytics","useScriptMetaPixel","useScriptNpm","useScriptPlausibleAnalytics","useScriptRybbitAnalytics","useScriptSegment","useScriptSnapchatPixel","useScriptStripe","useScriptUmamiAnalytics","useScriptVimeoPlayer","useScriptXPixel","useScriptYouTubePlayer","useColorMode","defineI18nConfig","defineI18nLocale","defineI18nRoute","useBrowserLocale","useCookieLocale","useI18nPreloadKeys","useLocaleHead","useLocalePath","useLocaleRoute","useRouteBaseName","useSetI18nParams","useSwitchLocalePath","defineI18nLocaleDetector","defineI18nConfig","defineI18nLocale","useScriptCloudflareTurnstile","verifyTurnstileToken","defineAppConfig","__buildAssetsURL","__publicAssetsURL","definePageMeta","useI18n","appendCorsHeaders","appendCorsPreflightHeaders","appendHeader","appendHeaders","appendResponseHeader","appendResponseHeaders","assertMethod","callNodeListener","clearResponseHeaders","clearSession","createApp","createAppEventHandler","createError","createEvent","createEventStream","createRouter","defaultContentType","defineEventHandler","defineLazyEventHandler","defineNodeListener","defineNodeMiddleware","defineRequestMiddleware","defineResponseMiddleware","defineWebSocket","defineWebSocketHandler","deleteCookie","dynamicEventHandler","eventHandler","fetchWithEvent","fromNodeMiddleware","fromPlainHandler","fromWebHandler","getCookie","getHeader","getHeaders","getMethod","getProxyRequestHeaders","getQuery","getRequestFingerprint","getRequestHeader","getRequestHeaders","getRequestHost","getRequestIP","getRequestPath","getRequestProtocol","getRequestURL","getRequestWebStream","getResponseHeader","getResponseHeaders","getResponseStatus","getResponseStatusText","getRouterParam","getRouterParams","getSession","getValidatedQuery","getValidatedRouterParams","handleCacheHeaders","handleCors","isCorsOriginAllowed","isError","isEvent","isEventHandler","isMethod","isPreflightRequest","isStream","isWebResponse","lazyEventHandler","parseCookies","promisifyNodeListener","proxyRequest","readBody","readFormData","readMultipartFormData","readRawBody","readValidatedBody","removeResponseHeader","sanitizeStatusCode","sanitizeStatusMessage","sealSession","send","sendError","sendIterable","sendNoContent","sendProxy","sendRedirect","sendStream","sendWebResponse","serveStatic","setCookie","setHeader","setHeaders","setResponseHeader","setResponseHeaders","setResponseStatus","splitCookiesString","toEventHandler","toNodeListener","toPlainHandler","toWebHandler","toWebRequest","unsealSession","updateSession","useBase","useSession","writeEarlyHints","useNitroApp","cachedEventHandler","cachedFunction","defineCachedEventHandler","defineCachedFunction","useAppConfig","useRuntimeConfig","useEvent","defineNitroErrorHandler","defineRouteMeta","defineNitroPlugin","nitroPlugin","defineRenderHandler","getRouteRules","useStorage","defineTask","runTask","useField","useFieldArray","useFieldError","useFieldValue","useForm","useFormContext","useFormErrors","useFormValues","useIsFieldDirty","useIsFieldTouched","useIsFieldValid","useIsFormDirty","useIsFormTouched","useIsFormValid","useIsSubmitting","useIsValidating","useResetForm","useSubmitCount","useSubmitForm","useValidateField","useValidateForm","Component","ComponentPublicInstance","computed","ComputedRef","customRef","defineAsyncComponent","defineComponent","DirectiveBinding","effect","effectScope","ExtractDefaultPropTypes","ExtractPropTypes","ExtractPublicPropTypes","getCurrentInstance","getCurrentScope","h","hasInjectionContext","inject","InjectionKey","isProxy","isReactive","isReadonly","isRef","isShallow","markRaw","MaybeRef","MaybeRefOrGetter","mergeModels","nextTick","onActivated","onBeforeMount","onBeforeUnmount","onBeforeUpdate","onDeactivated","onErrorCaptured","onMounted","onRenderTracked","onRenderTriggered","onScopeDispose","onServerPrefetch","onUnmounted","onUpdated","onWatcherCleanup","PropType","provide","proxyRefs","reactive","readonly","ref","Ref","resolveComponent","shallowReactive","shallowReadonly","shallowRef","toRaw","toRef","toRefs","toValue","triggerRef","unref","useAttrs","useCssModule","useCssVars","useId","useModel","useShadowRoot","useSlots","useTemplateRef","useTransitionState","VNode","watch","watchEffect","watchPostEffect","watchSyncEffect","withCtx","withDirectives","withKeys","withMemo","withModifiers","withScopeId","WritableComputedRef","isVue2","isVue3","onBeforeRouteLeave","onBeforeRouteUpdate","useLink"].map(i => [i, 'readonly'])),
  },
}
)

export function withNuxt(...customs) {
  return configs
    .clone()
    .append(...customs)
    .onResolved(configs => typegen(configs, { dtsPath: r("./eslint-typegen.d.ts"), augmentFlatConfigUtils: true }))
}

export default withNuxt