export const useScroll = () => {
	const scrollToElement = (elementId: string, offset = -36) => {
		if (import.meta.client) {
			const element = document.getElementById(elementId);
			if (element) {
				const elementPosition = element.getBoundingClientRect().top;
				const offsetPosition = elementPosition + window.pageYOffset + offset;

				window.scrollTo({
					top: offsetPosition,
					behavior: 'smooth'
				});
			}
		}
	};

	return {
		scrollToElement
	};
};
