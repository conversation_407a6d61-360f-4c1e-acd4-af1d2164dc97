import {
  AST_NODE_PROPS_KEYS,
  CORE_ERROR_CODES_EXTEND_POINT,
  CORE_WARN_CODES_EXTEND_POINT,
  CoreErrorCodes,
  CoreWarnCodes,
  DATETIME_FORMAT_OPTIONS_KEYS,
  DEFAULT_LOCALE,
  DEFAULT_MESSAGE_DATA_TYPE,
  MISSING_RESOLVE_VALUE,
  NOT_REOSLVED,
  NUMBER_FORMAT_OPTIONS_KEYS,
  VERSION,
  clearCompileCache,
  clearDateTimeFormat,
  clearNumberFormat,
  compile,
  createCoreContext,
  createCoreError,
  createMessageContext,
  datetime,
  fallbackWithLocaleChain,
  fallbackWithSimple,
  getAdditionalMeta,
  getDevToolsHook,
  getFallbackContext,
  getLocale,
  getWarnMessage,
  handleMissing,
  initI18nDevTools,
  isAlmostSameLocale,
  isImplicit<PERSON><PERSON>back,
  isMessageAST,
  isMessageFunction,
  isTranslateFallbackWarn,
  isTranslateMissingWarn,
  number,
  parse,
  parseDateTimeArgs,
  parseNumberArgs,
  parseTranslateArgs,
  registerLocaleFallbacker,
  registerMessageCompiler,
  registerMessageResolver,
  resolveLocale,
  resolveValue,
  resolveWithKeyValue,
  setAdditionalMeta,
  setDevToolsHook,
  setFallbackContext,
  translate,
  translateDevTools,
  updateFallbackLocale
} from "./chunk-54NDRSNM.js";
import {
  CompileErrorCodes,
  createCompileError
} from "./chunk-ZB33R6PD.js";
import {
  getGlobalThis
} from "./chunk-55TB3ZU2.js";

// node_modules/@intlify/core/dist/core.node.mjs
function initFeatureFlags() {
  if (typeof __INTLIFY_PROD_DEVTOOLS__ !== "boolean") {
    getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;
  }
  if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== "boolean") {
    getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;
  }
}
{
  initFeatureFlags();
}
registerMessageCompiler(compile);
registerMessageResolver(resolveValue);
registerLocaleFallbacker(fallbackWithLocaleChain);
export {
  AST_NODE_PROPS_KEYS,
  CORE_ERROR_CODES_EXTEND_POINT,
  CORE_WARN_CODES_EXTEND_POINT,
  CompileErrorCodes,
  CoreErrorCodes,
  CoreWarnCodes,
  DATETIME_FORMAT_OPTIONS_KEYS,
  DEFAULT_LOCALE,
  DEFAULT_MESSAGE_DATA_TYPE,
  MISSING_RESOLVE_VALUE,
  NOT_REOSLVED,
  NUMBER_FORMAT_OPTIONS_KEYS,
  VERSION,
  clearCompileCache,
  clearDateTimeFormat,
  clearNumberFormat,
  compile,
  createCompileError,
  createCoreContext,
  createCoreError,
  createMessageContext,
  datetime,
  fallbackWithLocaleChain,
  fallbackWithSimple,
  getAdditionalMeta,
  getDevToolsHook,
  getFallbackContext,
  getLocale,
  getWarnMessage,
  handleMissing,
  initI18nDevTools,
  isAlmostSameLocale,
  isImplicitFallback,
  isMessageAST,
  isMessageFunction,
  isTranslateFallbackWarn,
  isTranslateMissingWarn,
  number,
  parse,
  parseDateTimeArgs,
  parseNumberArgs,
  parseTranslateArgs,
  registerLocaleFallbacker,
  registerMessageCompiler,
  registerMessageResolver,
  resolveLocale,
  resolveValue,
  resolveWithKeyValue,
  setAdditionalMeta,
  setDevToolsHook,
  setFallbackContext,
  translate,
  translateDevTools,
  updateFallbackLocale
};
/*! Bundled license information:

@intlify/core/dist/core.node.mjs:
  (*!
    * core v11.1.12
    * (c) 2025 kazuya kawaguchi
    * Released under the MIT License.
    *)
*/
//# sourceMappingURL=@intlify_core.js.map
