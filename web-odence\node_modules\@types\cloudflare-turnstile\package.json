{"name": "@types/cloudflare-turnstile", "version": "0.2.2", "description": "TypeScript definitions for cloudflare-turnstile", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cloudflare-turnstile", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "lb<PERSON><PERSON>", "url": "https://github.com/lbarthon"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "worenga", "url": "https://github.com/worenga"}, {"name": "<PERSON>", "githubUsername": "olip<PERSON>", "url": "https://github.com/olipayne"}, {"name": "<PERSON><PERSON>", "githubUsername": "punkeel", "url": "https://github.com/punkeel"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/migue<PERSON><PERSON>a"}, {"name": "Aleksandar <PERSON>", "githubUsername": "AleksandarHr", "url": "https://github.com/AleksandarHr"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cloudflare-turnstile"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "72d2c58b2182d5d135f3b23eb31fc88e5f6fb6f8458e1d2054b503188c7c2dee", "typeScriptVersion": "4.8", "nonNpm": true}