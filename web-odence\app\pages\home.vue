<script lang="ts" setup>
import { 
	WrenchScrewdriverIcon,
	ShoppingBagIcon,
	ArrowTopRightOnSquareIcon,
	ClipboardDocumentCheckIcon,
	BellAlertIcon
 } from '@heroicons/vue/24/outline';

 function computedT(name: string) {
	return computed(() => $t(name));
}

const colorMode = useColorMode();
const { scrollToElement } = useScroll();

const splash = computed(() => {
	return colorMode.value === 'dark' ? 'Game-1-Splash-1-Dark.png?updatedAt=1759417919861' : 'Game-1-Splash-1-Light.png?updatedAt=1759418373875';
})

const cards = [
	{
		icon: ShoppingBagIcon,
		title: computedT('home.card_1.title').value,
		desc: computedT('home.card_1.desc').value,
		btnText: computedT('home.card_1.btnText').value,
		btnIcon: ArrowTopRightOnSquareIcon,
		gradCol: 'blue-cyan'
	},
	{
		icon: ClipboardDocumentCheckIcon,
		title: computedT('home.card_2.title').value,
		desc: computedT('home.card_2.desc').value,
		btnText: computedT('home.card_2.btnText').value,
		gradCol: 'cyan-purple'
	},
	{
		icon: BellAlertIcon,
		title: computedT('home.card_3.title').value,
		desc: computedT('home.card_3.desc').value,
		btnText: computedT('home.card_3.btnText').value,
		gridSpan: 2,
		isRegister: true
	}
];
</script>

<template>
	<main class="scroll-smooth inset-5">
		<!-- Hero -->
		<section id="hero" class="relative min-h-svh flex items-center justify-center bg-base-200 p-7 md:p-15">
			<div class="relative">
				<div class="flex justify-center mb-7">
					<ClientOnly>
						<NuxtPicture provider="imagekit" :src="splash" alt="Splash" :imgAttrs="{ class: 'max-h-25 md:max-h-30 lg:max-h-35' }" />
					</ClientOnly>
				</div>
				<div class="flex-row items-center justify-center my-7">
					<div class="flex justify-center mb-4">
						<NuxtLink to="" @click.prevent="scrollToElement('details')" class="flex items-center bg-orange-700/10 dark:bg-orange-200/10 outline-2 outline-orange-500/50 dark:outline-orange-200/50 rounded-lg p-3 px-5 hover:bg-orange-200/20 hover:outline-orange-200 translate-y-0 hover:-translate-y-0.5 ease-out duration-300 transition-all">
							<WrenchScrewdriverIcon class="size-6 me-3 translate-y-0.5 animate-pulse" />
							<h1 class="text-left md:text-center lg:text-xl font-bold animate-pulse">{{ $t('home.desc') }}</h1>
						</NuxtLink>
					</div>
					<div class="flex justify-center text-center">
						<p class="text-gray-500 max-lg:text-sm max-md:max-w-64 md:max-w-sm">{{ $t('home.subtle') }}</p>
					</div>
				</div>
			</div>
		</section>

		<!-- Details -->
		<section id="details" class="relative min-h-svh flex items-center justify-center bg-base-300 p-7 md:p-15">
			<div class="grid grid-cols-1 md:grid-cols-2 justify-center items-stretch w-full max-w-3xl gap-7 lg:gap-10">
				<HomeCard v-for="card in cards" :key="card.title" 
					:class="(card.gridSpan === 2) ? 'md:col-span-2' : ''"
					:icon="card.icon" 
					:title="card.title" 
					:desc="card.desc" 
					:btnText="card.btnText" 
					:btnIcon="card.btnIcon"
					:gridSpan="card.gridSpan"
					:gradCol="card.gradCol"
					:isRegister="card.isRegister"
				/>
			</div>
		</section>
		
	</main>
</template>
