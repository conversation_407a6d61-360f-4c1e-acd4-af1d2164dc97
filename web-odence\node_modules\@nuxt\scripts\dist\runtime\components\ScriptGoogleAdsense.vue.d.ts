import type { ElementScriptTrigger } from '#nuxt-scripts/types';
type __VLS_Props = {
    dataAdClient: string;
    dataAdSlot: string;
    dataAdFormat?: 'auto' | 'rectangle' | 'vertical' | 'horizontal' | 'fluid' | 'autorelaxed';
    dataAdLayout?: 'in-article' | 'in-feed' | 'fixed';
    dataFullWidthResponsive?: boolean;
    /**
     * Defines the trigger event to load the script.
     */
    trigger?: ElementScriptTrigger;
};
declare var __VLS_1: {}, __VLS_3: {}, __VLS_5: {};
type __VLS_Slots = {} & {
    awaitingLoad?: (props: typeof __VLS_1) => any;
} & {
    loading?: (props: typeof __VLS_3) => any;
} & {
    error?: (props: typeof __VLS_5) => any;
};
declare const __VLS_component: import("vue").DefineComponent<__VLS_Props, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    error: () => any;
    ready: (e: import("#nuxt-scripts/types").UseScriptContext<import("../registry/google-adsense.js").GoogleAdsenseApi>) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    onError?: (() => any) | undefined;
    onReady?: ((e: import("#nuxt-scripts/types").UseScriptContext<import("../registry/google-adsense.js").GoogleAdsenseApi>) => any) | undefined;
}>, {
    dataAdFormat: "auto" | "rectangle" | "vertical" | "horizontal" | "fluid" | "autorelaxed";
    dataFullWidthResponsive: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
