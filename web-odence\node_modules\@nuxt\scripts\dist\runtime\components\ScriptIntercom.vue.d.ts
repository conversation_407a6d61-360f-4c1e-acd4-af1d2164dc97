import type { ElementScriptTrigger } from '#nuxt-scripts/types';
type __VLS_Props = {
    appId: string;
    apiBase?: string;
    name?: string;
    email?: string;
    userId?: string;
    alignment?: 'left' | 'right';
    horizontalPadding?: number;
    verticalPadding?: number;
    /**
     * Defines the trigger event to load the script.
     */
    trigger?: ElementScriptTrigger;
};
declare var __VLS_1: {
    ready: any;
}, __VLS_3: {}, __VLS_5: {}, __VLS_7: {};
type __VLS_Slots = {} & {
    default?: (props: typeof __VLS_1) => any;
} & {
    awaitingLoad?: (props: typeof __VLS_3) => any;
} & {
    loading?: (props: typeof __VLS_5) => any;
} & {
    error?: (props: typeof __VLS_7) => any;
};
declare const __VLS_component: import("vue").DefineComponent<__VLS_Props, {
    intercom: import("#nuxt-scripts/types").UseScriptContext<import("../registry/intercom.js").IntercomApi>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    error: () => any;
    ready: (e: import("#nuxt-scripts/types").UseScriptContext<import("../registry/intercom.js").IntercomApi>) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    onError?: (() => any) | undefined;
    onReady?: ((e: import("#nuxt-scripts/types").UseScriptContext<import("../registry/intercom.js").IntercomApi>) => any) | undefined;
}>, {
    trigger: ElementScriptTrigger;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
