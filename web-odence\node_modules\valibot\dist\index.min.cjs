"use strict";var G=Object.defineProperty;var eu=Object.getOwnPropertyDescriptor;var nu=Object.getOwnPropertyNames;var tu=Object.prototype.hasOwnProperty;var su=(t,n)=>{for(var e in n)G(t,e,{get:n[e],enumerable:!0})},uu=(t,n,e,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let u of nu(n))!tu.call(t,u)&&u!==e&&G(t,u,{get:()=>n[u],enumerable:!(s=eu(n,u))||s.enumerable});return t};var ru=t=>uu(G({},"__esModule",{value:!0}),t);var Zu={};su(Zu,{BASE64_REGEX:()=>Q,BIC_REGEX:()=>Y,CUID2_REGEX:()=>ee,DECIMAL_REGEX:()=>ne,DIGITS_REGEX:()=>te,EMAIL_REGEX:()=>se,EMOJI_REGEX:()=>ue,HEXADECIMAL_REGEX:()=>re,HEX_COLOR_REGEX:()=>oe,IMEI_REGEX:()=>ae,IPV4_REGEX:()=>ie,IPV6_REGEX:()=>Ie,IP_REGEX:()=>pe,ISO_DATE_REGEX:()=>me,ISO_DATE_TIME_REGEX:()=>ce,ISO_TIMESTAMP_REGEX:()=>fe,ISO_TIME_REGEX:()=>Te,ISO_TIME_SECOND_REGEX:()=>de,ISO_WEEK_REGEX:()=>ye,MAC48_REGEX:()=>le,MAC64_REGEX:()=>he,MAC_REGEX:()=>ke,NANO_ID_REGEX:()=>xe,OCTAL_REGEX:()=>ge,RFC_EMAIL_REGEX:()=>Oe,SLUG_REGEX:()=>we,ULID_REGEX:()=>Se,UUID_REGEX:()=>Be,ValiError:()=>x,_addIssue:()=>r,_getByteCount:()=>A,_getGraphemeCount:()=>E,_getLastMetadata:()=>R,_getStandardProps:()=>p,_getWordCount:()=>M,_isLuhnAlgo:()=>V,_isValidObjectKey:()=>O,_joinExpects:()=>k,_stringify:()=>f,any:()=>Kt,args:()=>qe,argsAsync:()=>_e,array:()=>$t,arrayAsync:()=>Ft,assert:()=>Su,awaitAsync:()=>We,base64:()=>De,bic:()=>Ve,bigint:()=>zt,blob:()=>Gt,boolean:()=>Ut,brand:()=>Ne,bytes:()=>Ce,check:()=>Le,checkAsync:()=>Ke,checkItems:()=>$e,checkItemsAsync:()=>Fe,config:()=>Bu,creditCard:()=>ze,cuid2:()=>Ge,custom:()=>Jt,customAsync:()=>Xt,date:()=>Ht,decimal:()=>Ue,deleteGlobalConfig:()=>au,deleteGlobalMessage:()=>Iu,deleteSchemaMessage:()=>mu,deleteSpecificMessage:()=>Tu,description:()=>Je,digits:()=>Xe,email:()=>He,emoji:()=>Ze,empty:()=>Qe,endsWith:()=>Ye,entries:()=>en,entriesFromList:()=>fu,entriesFromObjects:()=>yu,enum:()=>Zt,enum_:()=>Zt,everyItem:()=>nn,exactOptional:()=>Qt,exactOptionalAsync:()=>Yt,excludes:()=>tn,fallback:()=>Au,fallbackAsync:()=>Eu,file:()=>es,filterItems:()=>sn,findItem:()=>un,finite:()=>rn,flatten:()=>Mu,flavor:()=>on,forward:()=>bu,forwardAsync:()=>Pu,function:()=>ns,function_:()=>ns,getDefault:()=>y,getDefaults:()=>Ae,getDefaultsAsync:()=>Ee,getDescription:()=>Ru,getDotPath:()=>N,getFallback:()=>l,getFallbacks:()=>Me,getFallbacksAsync:()=>be,getGlobalConfig:()=>w,getGlobalMessage:()=>U,getMetadata:()=>ju,getSchemaMessage:()=>J,getSpecificMessage:()=>X,getTitle:()=>vu,graphemes:()=>an,gtValue:()=>In,hash:()=>pn,hexColor:()=>cn,hexadecimal:()=>mn,imei:()=>Tn,includes:()=>dn,instance:()=>ts,integer:()=>fn,intersect:()=>ss,intersectAsync:()=>us,ip:()=>yn,ipv4:()=>ln,ipv6:()=>hn,is:()=>qu,isOfKind:()=>lu,isOfType:()=>hu,isValiError:()=>ku,isoDate:()=>kn,isoDateTime:()=>xn,isoTime:()=>gn,isoTimeSecond:()=>On,isoTimestamp:()=>wn,isoWeek:()=>Sn,keyof:()=>_u,lazy:()=>rs,lazyAsync:()=>os,length:()=>Bn,literal:()=>as,looseObject:()=>is,looseObjectAsync:()=>Is,looseTuple:()=>ps,looseTupleAsync:()=>ms,ltValue:()=>An,mac:()=>En,mac48:()=>Mn,mac64:()=>bn,map:()=>cs,mapAsync:()=>Ts,mapItems:()=>Pn,maxBytes:()=>Rn,maxEntries:()=>jn,maxGraphemes:()=>vn,maxLength:()=>qn,maxSize:()=>_n,maxValue:()=>Wn,maxWords:()=>Dn,message:()=>Wu,metadata:()=>Vn,mimeType:()=>Nn,minBytes:()=>Cn,minEntries:()=>Ln,minGraphemes:()=>Kn,minLength:()=>$n,minSize:()=>Fn,minValue:()=>zn,minWords:()=>Gn,multipleOf:()=>Un,nan:()=>ds,nanoid:()=>Jn,never:()=>fs,nonEmpty:()=>Xn,nonNullable:()=>ys,nonNullableAsync:()=>ls,nonNullish:()=>hs,nonNullishAsync:()=>ks,nonOptional:()=>L,nonOptionalAsync:()=>K,normalize:()=>Hn,notBytes:()=>Zn,notEntries:()=>Qn,notGraphemes:()=>Yn,notLength:()=>et,notSize:()=>nt,notValue:()=>tt,notValues:()=>st,notWords:()=>ut,null:()=>xs,null_:()=>xs,nullable:()=>gs,nullableAsync:()=>Os,nullish:()=>ws,nullishAsync:()=>Ss,number:()=>Bs,object:()=>As,objectAsync:()=>Es,objectWithRest:()=>Ms,objectWithRestAsync:()=>bs,octal:()=>rt,omit:()=>Du,optional:()=>$,optionalAsync:()=>F,parse:()=>Pe,parseAsync:()=>Re,parseJson:()=>ot,parser:()=>Vu,parserAsync:()=>Nu,partial:()=>Cu,partialAsync:()=>Lu,partialCheck:()=>at,partialCheckAsync:()=>it,pick:()=>Ku,picklist:()=>z,pipe:()=>$u,pipeAsync:()=>Fu,promise:()=>Ps,rawCheck:()=>It,rawCheckAsync:()=>pt,rawTransform:()=>mt,rawTransformAsync:()=>ct,readonly:()=>Tt,record:()=>Rs,recordAsync:()=>js,reduceItems:()=>dt,regex:()=>ft,required:()=>zu,requiredAsync:()=>Gu,returns:()=>yt,returnsAsync:()=>lt,rfcEmail:()=>ht,safeInteger:()=>kt,safeParse:()=>je,safeParseAsync:()=>ve,safeParser:()=>Uu,safeParserAsync:()=>Ju,set:()=>vs,setAsync:()=>qs,setGlobalConfig:()=>ou,setGlobalMessage:()=>iu,setSchemaMessage:()=>pu,setSpecificMessage:()=>cu,size:()=>xt,slug:()=>gt,someItem:()=>Ot,sortItems:()=>wt,startsWith:()=>St,strictObject:()=>_s,strictObjectAsync:()=>Ws,strictTuple:()=>Ds,strictTupleAsync:()=>Vs,string:()=>Ns,stringifyJson:()=>Bt,summarize:()=>Xu,symbol:()=>Cs,title:()=>At,toLowerCase:()=>Et,toMaxValue:()=>Mt,toMinValue:()=>bt,toUpperCase:()=>Pt,transform:()=>Rt,transformAsync:()=>jt,trim:()=>vt,trimEnd:()=>qt,trimStart:()=>_t,tuple:()=>Ls,tupleAsync:()=>Ks,tupleWithRest:()=>$s,tupleWithRestAsync:()=>Fs,ulid:()=>Wt,undefined:()=>zs,undefined_:()=>zs,undefinedable:()=>Gs,undefinedableAsync:()=>Us,union:()=>Js,unionAsync:()=>Xs,unknown:()=>Hs,unwrap:()=>Hu,url:()=>Dt,uuid:()=>Vt,value:()=>Nt,values:()=>Ct,variant:()=>Zs,variantAsync:()=>Qs,void:()=>Ys,void_:()=>Ys,words:()=>Lt});module.exports=ru(Zu);var P;function ou(t){P={...P,...t}}function w(t){return{lang:t?.lang??P?.lang,message:t?.message,abortEarly:t?.abortEarly??P?.abortEarly,abortPipeEarly:t?.abortPipeEarly??P?.abortPipeEarly}}function au(){P=void 0}var _;function iu(t,n){_||(_=new Map),_.set(n,t)}function U(t){return _?.get(t)}function Iu(t){_?.delete(t)}var W;function pu(t,n){W||(W=new Map),W.set(n,t)}function J(t){return W?.get(t)}function mu(t){W?.delete(t)}var b;function cu(t,n,e){b||(b=new Map),b.get(t)||b.set(t,new Map),b.get(t).set(e,n)}function X(t,n){return b?.get(t)?.get(n)}function Tu(t,n){b?.get(t)?.delete(n)}function f(t){let n=typeof t;return n==="string"?`"${t}"`:n==="number"||n==="bigint"||n==="boolean"?`${t}`:n==="object"||n==="function"?(t&&Object.getPrototypeOf(t)?.constructor?.name)??"null":n}function r(t,n,e,s,u){let o=u&&"input"in u?u.input:e.value,a=u?.expected??t.expects??null,I=u?.received??f(o),i={kind:t.kind,type:t.type,input:o,expected:a,received:I,message:`Invalid ${n}: ${a?`Expected ${a} but r`:"R"}eceived ${I}`,requirement:t.requirement,path:u?.path,issues:u?.issues,lang:s.lang,abortEarly:s.abortEarly,abortPipeEarly:s.abortPipeEarly},m=t.kind==="schema",c=u?.message??t.message??X(t.reference,i.lang)??(m?J(i.lang):null)??s.message??U(i.lang);c!==void 0&&(i.message=typeof c=="function"?c(i):c),m&&(e.typed=!1),e.issues?e.issues.push(i):e.issues=[i]}var H;function A(t){return H||(H=new TextEncoder),H.encode(t).length}var Z;function E(t){Z||(Z=new Intl.Segmenter);let n=Z.segment(t),e=0;for(let s of n)e++;return e}function R(t,n){if("pipe"in t){let e=[];for(let s=t.pipe.length-1;s>=0;s--){let u=t.pipe[s];if(u.kind==="schema"&&"pipe"in u)e.push(u);else if(u.kind==="metadata"&&u.type===n)return u[n]}for(let s of e){let u=R(s,n);if(u!==void 0)return u}}}function p(t){return{version:1,vendor:"valibot",validate(n){return t["~run"]({value:n},w())}}}var D;function M(t,n){D||(D=new Map),D.get(t)||D.set(t,new Intl.Segmenter(t,{granularity:"word"}));let e=D.get(t).segment(n),s=0;for(let u of e)u.isWordLike&&s++;return s}var du=/\D/gu;function V(t){let n=t.replace(du,""),e=n.length,s=1,u=0;for(;e;){let o=+n[--e];s^=1,u+=s?[0,2,4,6,8,1,3,5,7,9][o]:o}return u%10===0}function O(t,n){return Object.hasOwn(t,n)&&n!=="__proto__"&&n!=="prototype"&&n!=="constructor"}function k(t,n){let e=[...new Set(t)];return e.length>1?`(${e.join(` ${n} `)})`:e[0]??"never"}function fu(t,n){let e={};for(let s of t)e[s]=n;return e}function yu(t){let n={};for(let e of t)Object.assign(n,e.entries);return n}function N(t){if(t.path){let n="";for(let e of t.path)if(typeof e.key=="string"||typeof e.key=="number")n?n+=`.${e.key}`:n+=e.key;else return null;return n}return null}function lu(t,n){return n.kind===t}function hu(t,n){return n.type===t}function ku(t){return t instanceof x}var x=class extends Error{constructor(n){super(n[0].message),this.name="ValiError",this.issues=n}};function qe(t){return{kind:"transformation",type:"args",reference:qe,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:u},e);if(o.issues)throw new x(o.issues);return s(...o.value)},n}}}function _e(t){return{kind:"transformation",type:"args",reference:_e,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await t["~run"]({value:u},e);if(o.issues)throw new x(o.issues);return s(...o.value)},n}}}function We(){return{kind:"transformation",type:"await",reference:We,async:!0,async"~run"(t){return t.value=await t.value,t}}}var Q=/^(?:[\da-z+/]{4})*(?:[\da-z+/]{2}==|[\da-z+/]{3}=)?$/iu,Y=/^[A-Z]{6}(?!00)[\dA-Z]{2}(?:[\dA-Z]{3})?$/u,ee=/^[a-z][\da-z]*$/u,ne=/^[+-]?(?:\d*\.)?\d+$/u,te=/^\d+$/u,se=/^[\w+-]+(?:\.[\w+-]+)*@[\da-z]+(?:[.-][\da-z]+)*\.[a-z]{2,}$/iu,ue=/^(?:[\u{1F1E6}-\u{1F1FF}]{2}|\u{1F3F4}[\u{E0061}-\u{E007A}]{2}[\u{E0030}-\u{E0039}\u{E0061}-\u{E007A}]{1,3}\u{E007F}|(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation})(?:\u200D(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation}))*)+$/u,re=/^(?:0[hx])?[\da-fA-F]+$/u,oe=/^#(?:[\da-fA-F]{3,4}|[\da-fA-F]{6}|[\da-fA-F]{8})$/u,ae=/^\d{15}$|^\d{2}-\d{6}-\d{6}-\d$/u,ie=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$/u,Ie=/^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,pe=/^(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])(?:\.(?:(?:[1-9]|1\d|2[0-4])?\d|25[0-5])){3}$|^(?:(?:[\da-f]{1,4}:){7}[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,7}:|(?:[\da-f]{1,4}:){1,6}:[\da-f]{1,4}|(?:[\da-f]{1,4}:){1,5}(?::[\da-f]{1,4}){1,2}|(?:[\da-f]{1,4}:){1,4}(?::[\da-f]{1,4}){1,3}|(?:[\da-f]{1,4}:){1,3}(?::[\da-f]{1,4}){1,4}|(?:[\da-f]{1,4}:){1,2}(?::[\da-f]{1,4}){1,5}|[\da-f]{1,4}:(?::[\da-f]{1,4}){1,6}|:(?:(?::[\da-f]{1,4}){1,7}|:)|fe80:(?::[\da-f]{0,4}){0,4}%[\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/iu,me=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])$/u,ce=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3]):[0-5]\d$/u,Te=/^(?:0\d|1\d|2[0-3]):[0-5]\d$/u,de=/^(?:0\d|1\d|2[0-3])(?::[0-5]\d){2}$/u,fe=/^\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\d|0[1-9]|3[01])[T ](?:0\d|1\d|2[0-3])(?::[0-5]\d){2}(?:\.\d{1,9})?(?:Z|[+-](?:0\d|1\d|2[0-3])(?::?[0-5]\d)?)$/u,ye=/^\d{4}-W(?:0[1-9]|[1-4]\d|5[0-3])$/u,le=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$/iu,he=/^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,ke=/^(?:[\da-f]{2}:){5}[\da-f]{2}$|^(?:[\da-f]{2}-){5}[\da-f]{2}$|^(?:[\da-f]{4}\.){2}[\da-f]{4}$|^(?:[\da-f]{2}:){7}[\da-f]{2}$|^(?:[\da-f]{2}-){7}[\da-f]{2}$|^(?:[\da-f]{4}\.){3}[\da-f]{4}$|^(?:[\da-f]{4}:){3}[\da-f]{4}$/iu,xe=/^[\w-]+$/u,ge=/^(?:0o)?[0-7]+$/u,Oe=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,we=/^[\da-z]+(?:[-_][\da-z]+)*$/u,Se=/^[\da-hjkmnp-tv-zA-HJKMNP-TV-Z]{26}$/u,Be=/^[\da-f]{8}(?:-[\da-f]{4}){3}-[\da-f]{12}$/iu;function De(t){return{kind:"validation",type:"base64",reference:De,async:!1,expects:null,requirement:Q,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Base64",n,e),n}}}function Ve(t){return{kind:"validation",type:"bic",reference:Ve,async:!1,expects:null,requirement:Y,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"BIC",n,e),n}}}function Ne(t){return{kind:"transformation",type:"brand",reference:Ne,async:!1,name:t,"~run"(n){return n}}}function Ce(t,n){return{kind:"validation",type:"bytes",reference:Ce,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u!==this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Le(t,n){return{kind:"validation",type:"check",reference:Le,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement(e.value)&&r(this,"input",e,s),e}}}function Ke(t,n){return{kind:"validation",type:"check",reference:Ke,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){return e.typed&&!await this.requirement(e.value)&&r(this,"input",e,s),e}}}function $e(t,n){return{kind:"validation",type:"check_items",reference:$e,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){if(e.typed)for(let u=0;u<e.value.length;u++){let o=e.value[u];this.requirement(o,u,e.value)||r(this,"item",e,s,{input:o,path:[{type:"array",origin:"value",input:e.value,key:u,value:o}]})}return e}}}function Fe(t,n){return{kind:"validation",type:"check_items",reference:Fe,async:!0,expects:null,requirement:t,message:n,async"~run"(e,s){if(e.typed){let u=await Promise.all(e.value.map(this.requirement));for(let o=0;o<e.value.length;o++)if(!u[o]){let a=e.value[o];r(this,"item",e,s,{input:a,path:[{type:"array",origin:"value",input:e.value,key:o,value:a}]})}}return e}}}var xu=/^(?:\d{14,19}|\d{4}(?: \d{3,6}){2,4}|\d{4}(?:-\d{3,6}){2,4})$/u,gu=/[- ]/gu,Ou=[/^3[47]\d{13}$/u,/^3(?:0[0-5]|[68]\d)\d{11,13}$/u,/^6(?:011|5\d{2})\d{12,15}$/u,/^(?:2131|1800|35\d{3})\d{11}$/u,/^5[1-5]\d{2}|(?:222\d|22[3-9]\d|2[3-6]\d{2}|27[01]\d|2720)\d{12}$/u,/^(?:6[27]\d{14,17}|81\d{14,17})$/u,/^4\d{12}(?:\d{3,6})?$/u];function ze(t){return{kind:"validation",type:"credit_card",reference:ze,async:!1,expects:null,requirement(n){let e;return xu.test(n)&&(e=n.replace(gu,""))&&Ou.some(s=>s.test(e))&&V(e)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"credit card",n,e),n}}}function Ge(t){return{kind:"validation",type:"cuid2",reference:Ge,async:!1,expects:null,requirement:ee,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Cuid2",n,e),n}}}function Ue(t){return{kind:"validation",type:"decimal",reference:Ue,async:!1,expects:null,requirement:ne,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"decimal",n,e),n}}}function Je(t){return{kind:"metadata",type:"description",reference:Je,description:t}}function Xe(t){return{kind:"validation",type:"digits",reference:Xe,async:!1,expects:null,requirement:te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"digits",n,e),n}}}function He(t){return{kind:"validation",type:"email",reference:He,expects:null,async:!1,requirement:se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function Ze(t){return{kind:"validation",type:"emoji",reference:Ze,async:!1,expects:null,requirement:ue,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"emoji",n,e),n}}}function Qe(t){return{kind:"validation",type:"empty",reference:Qe,async:!1,expects:"0",message:t,"~run"(n,e){return n.typed&&n.value.length>0&&r(this,"length",n,e,{received:`${n.value.length}`}),n}}}function Ye(t,n){return{kind:"validation",type:"ends_with",reference:Ye,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.endsWith(this.requirement)&&r(this,"end",e,s,{received:`"${e.value.slice(-this.requirement.length)}"`}),e}}}function en(t,n){return{kind:"validation",type:"entries",reference:en,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u!==this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function nn(t,n){return{kind:"validation",type:"every_item",reference:nn,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.every(this.requirement)&&r(this,"item",e,s),e}}}function tn(t,n){let e=f(t);return{kind:"validation",type:"excludes",reference:tn,async:!1,expects:`!${e}`,requirement:t,message:n,"~run"(s,u){return s.typed&&s.value.includes(this.requirement)&&r(this,"content",s,u,{received:e}),s}}}function sn(t){return{kind:"transformation",type:"filter_items",reference:sn,async:!1,operation:t,"~run"(n){return n.value=n.value.filter(this.operation),n}}}function un(t){return{kind:"transformation",type:"find_item",reference:un,async:!1,operation:t,"~run"(n){return n.value=n.value.find(this.operation),n}}}function rn(t){return{kind:"validation",type:"finite",reference:rn,async:!1,expects:null,requirement:Number.isFinite,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"finite",n,e),n}}}function on(t){return{kind:"transformation",type:"flavor",reference:on,async:!1,name:t,"~run"(n){return n}}}function an(t,n){return{kind:"validation",type:"graphemes",reference:an,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u!==this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function In(t,n){return{kind:"validation",type:"gt_value",reference:In,async:!1,expects:`>${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}var wu={md4:32,md5:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8,adler32:8};function pn(t,n){return{kind:"validation",type:"hash",reference:pn,expects:null,async:!1,requirement:RegExp(t.map(e=>`^[a-f0-9]{${wu[e]}}$`).join("|"),"iu"),message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"hash",e,s),e}}}function mn(t){return{kind:"validation",type:"hexadecimal",reference:mn,async:!1,expects:null,requirement:re,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hexadecimal",n,e),n}}}function cn(t){return{kind:"validation",type:"hex_color",reference:cn,async:!1,expects:null,requirement:oe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"hex color",n,e),n}}}function Tn(t){return{kind:"validation",type:"imei",reference:Tn,async:!1,expects:null,requirement(n){return ae.test(n)&&V(n)},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"IMEI",n,e),n}}}function dn(t,n){let e=f(t);return{kind:"validation",type:"includes",reference:dn,async:!1,expects:e,requirement:t,message:n,"~run"(s,u){return s.typed&&!s.value.includes(this.requirement)&&r(this,"content",s,u,{received:`!${e}`}),s}}}function fn(t){return{kind:"validation",type:"integer",reference:fn,async:!1,expects:null,requirement:Number.isInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"integer",n,e),n}}}function yn(t){return{kind:"validation",type:"ip",reference:yn,async:!1,expects:null,requirement:pe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IP",n,e),n}}}function ln(t){return{kind:"validation",type:"ipv4",reference:ln,async:!1,expects:null,requirement:ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv4",n,e),n}}}function hn(t){return{kind:"validation",type:"ipv6",reference:hn,async:!1,expects:null,requirement:Ie,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"IPv6",n,e),n}}}function kn(t){return{kind:"validation",type:"iso_date",reference:kn,async:!1,expects:null,requirement:me,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date",n,e),n}}}function xn(t){return{kind:"validation",type:"iso_date_time",reference:xn,async:!1,expects:null,requirement:ce,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"date-time",n,e),n}}}function gn(t){return{kind:"validation",type:"iso_time",reference:gn,async:!1,expects:null,requirement:Te,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time",n,e),n}}}function On(t){return{kind:"validation",type:"iso_time_second",reference:On,async:!1,expects:null,requirement:de,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"time-second",n,e),n}}}function wn(t){return{kind:"validation",type:"iso_timestamp",reference:wn,async:!1,expects:null,requirement:fe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"timestamp",n,e),n}}}function Sn(t){return{kind:"validation",type:"iso_week",reference:Sn,async:!1,expects:null,requirement:ye,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"week",n,e),n}}}function Bn(t,n){return{kind:"validation",type:"length",reference:Bn,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length!==this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function An(t,n){return{kind:"validation",type:"lt_value",reference:An,async:!1,expects:`<${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function En(t){return{kind:"validation",type:"mac",reference:En,async:!1,expects:null,requirement:ke,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"MAC",n,e),n}}}function Mn(t){return{kind:"validation",type:"mac48",reference:Mn,async:!1,expects:null,requirement:le,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"48-bit MAC",n,e),n}}}function bn(t){return{kind:"validation",type:"mac64",reference:bn,async:!1,expects:null,requirement:he,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"64-bit MAC",n,e),n}}}function Pn(t){return{kind:"transformation",type:"map_items",reference:Pn,async:!1,operation:t,"~run"(n){return n.value=n.value.map(this.operation),n}}}function Rn(t,n){return{kind:"validation",type:"max_bytes",reference:Rn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u>this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function jn(t,n){return{kind:"validation",type:"max_entries",reference:jn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u>this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function vn(t,n){return{kind:"validation",type:"max_graphemes",reference:vn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u>this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function qn(t,n){return{kind:"validation",type:"max_length",reference:qn,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length>this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function _n(t,n){return{kind:"validation",type:"max_size",reference:_n,async:!1,expects:`<=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size>this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function Wn(t,n){return{kind:"validation",type:"max_value",reference:Wn,async:!1,expects:`<=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value<=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Dn(t,n,e){return{kind:"validation",type:"max_words",reference:Dn,async:!1,expects:`<=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o>this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Vn(t){return{kind:"metadata",type:"metadata",reference:Vn,metadata:t}}function Nn(t,n){return{kind:"validation",type:"mime_type",reference:Nn,async:!1,expects:k(t.map(e=>`"${e}"`),"|"),requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.includes(e.value.type)&&r(this,"MIME type",e,s,{received:`"${e.value.type}"`}),e}}}function Cn(t,n){return{kind:"validation",type:"min_bytes",reference:Cn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u<this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Ln(t,n){return{kind:"validation",type:"min_entries",reference:Ln,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u<this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function Kn(t,n){return{kind:"validation",type:"min_graphemes",reference:Kn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u<this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function $n(t,n){return{kind:"validation",type:"min_length",reference:$n,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length<this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function Fn(t,n){return{kind:"validation",type:"min_size",reference:Fn,async:!1,expects:`>=${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size<this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function zn(t,n){return{kind:"validation",type:"min_value",reference:zn,async:!1,expects:`>=${t instanceof Date?t.toJSON():f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!(e.value>=this.requirement)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Gn(t,n,e){return{kind:"validation",type:"min_words",reference:Gn,async:!1,expects:`>=${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o<this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Un(t,n){return{kind:"validation",type:"multiple_of",reference:Un,async:!1,expects:`%${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value%this.requirement!=0&&r(this,"multiple",e,s),e}}}function Jn(t){return{kind:"validation",type:"nanoid",reference:Jn,async:!1,expects:null,requirement:xe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"Nano ID",n,e),n}}}function Xn(t){return{kind:"validation",type:"non_empty",reference:Xn,async:!1,expects:"!0",message:t,"~run"(n,e){return n.typed&&n.value.length===0&&r(this,"length",n,e,{received:"0"}),n}}}function Hn(t){return{kind:"transformation",type:"normalize",reference:Hn,async:!1,form:t,"~run"(n){return n.value=n.value.normalize(this.form),n}}}function Zn(t,n){return{kind:"validation",type:"not_bytes",reference:Zn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=A(e.value);u===this.requirement&&r(this,"bytes",e,s,{received:`${u}`})}return e}}}function Qn(t,n){return{kind:"validation",type:"not_entries",reference:Qn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(!e.typed)return e;let u=Object.keys(e.value).length;return e.typed&&u===this.requirement&&r(this,"entries",e,s,{received:`${u}`}),e}}}function Yn(t,n){return{kind:"validation",type:"not_graphemes",reference:Yn,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){if(e.typed){let u=E(e.value);u===this.requirement&&r(this,"graphemes",e,s,{received:`${u}`})}return e}}}function et(t,n){return{kind:"validation",type:"not_length",reference:et,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.length===this.requirement&&r(this,"length",e,s,{received:`${e.value.length}`}),e}}}function nt(t,n){return{kind:"validation",type:"not_size",reference:nt,async:!1,expects:`!${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size===this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function tt(t,n){return{kind:"validation",type:"not_value",reference:tt,async:!1,expects:t instanceof Date?`!${t.toJSON()}`:`!${f(t)}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement<=e.value&&this.requirement>=e.value&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function st(t,n){return{kind:"validation",type:"not_values",reference:st,async:!1,expects:`!${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function ut(t,n,e){return{kind:"validation",type:"not_words",reference:ut,async:!1,expects:`!${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o===this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function rt(t){return{kind:"validation",type:"octal",reference:rt,async:!1,expects:null,requirement:ge,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"octal",n,e),n}}}function ot(t,n){return{kind:"transformation",type:"parse_json",reference:ot,config:t,message:n,async:!1,"~run"(e,s){try{e.value=JSON.parse(e.value,this.config?.reviver)}catch(u){if(u instanceof Error)r(this,"JSON",e,s,{received:`"${u.message}"`}),e.typed=!1;else throw u}return e}}}function C(t,n){if(t.issues)for(let e of n)for(let s of t.issues){let u=!1,o=Math.min(e.length,s.path?.length??0);for(let a=0;a<o;a++)if(e[a]!==s.path[a].key&&(e[a]!=="$"||s.path[a].type!=="array")){u=!0;break}if(!u)return!1}return!0}function at(t,n,e){return{kind:"validation",type:"partial_check",reference:at,async:!1,expects:null,paths:t,requirement:n,message:e,"~run"(s,u){return(s.typed||C(s,t))&&!this.requirement(s.value)&&r(this,"input",s,u),s}}}function it(t,n,e){return{kind:"validation",type:"partial_check",reference:it,async:!0,expects:null,paths:t,requirement:n,message:e,async"~run"(s,u){return(s.typed||C(s,t))&&!await this.requirement(s.value)&&r(this,"input",s,u),s}}}function It(t){return{kind:"validation",type:"raw_check",reference:It,async:!1,expects:null,"~run"(n,e){return t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function pt(t){return{kind:"validation",type:"raw_check",reference:pt,async:!0,expects:null,async"~run"(n,e){return await t({dataset:n,config:e,addIssue:s=>r(this,s?.label??"input",n,e,s)}),n}}}function mt(t){return{kind:"transformation",type:"raw_transform",reference:mt,async:!1,"~run"(n,e){let s=t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function ct(t){return{kind:"transformation",type:"raw_transform",reference:ct,async:!0,async"~run"(n,e){let s=await t({dataset:n,config:e,addIssue:u=>r(this,u?.label??"input",n,e,u),NEVER:null});return n.issues?n.typed=!1:n.value=s,n}}}function Tt(){return{kind:"transformation",type:"readonly",reference:Tt,async:!1,"~run"(t){return t}}}function dt(t,n){return{kind:"transformation",type:"reduce_items",reference:dt,async:!1,operation:t,initial:n,"~run"(e){return e.value=e.value.reduce(this.operation,this.initial),e}}}function ft(t,n){return{kind:"validation",type:"regex",reference:ft,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.test(e.value)&&r(this,"format",e,s),e}}}function yt(t){return{kind:"transformation",type:"returns",reference:yt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=(...u)=>{let o=this.schema["~run"]({value:s(...u)},e);if(o.issues)throw new x(o.issues);return o.value},n}}}function lt(t){return{kind:"transformation",type:"returns",reference:lt,async:!1,schema:t,"~run"(n,e){let s=n.value;return n.value=async(...u)=>{let o=await this.schema["~run"]({value:await s(...u)},e);if(o.issues)throw new x(o.issues);return o.value},n}}}function ht(t){return{kind:"validation",type:"rfc_email",reference:ht,expects:null,async:!1,requirement:Oe,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"email",n,e),n}}}function kt(t){return{kind:"validation",type:"safe_integer",reference:kt,async:!1,expects:null,requirement:Number.isSafeInteger,message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"safe integer",n,e),n}}}function xt(t,n){return{kind:"validation",type:"size",reference:xt,async:!1,expects:`${t}`,requirement:t,message:n,"~run"(e,s){return e.typed&&e.value.size!==this.requirement&&r(this,"size",e,s,{received:`${e.value.size}`}),e}}}function gt(t){return{kind:"validation",type:"slug",reference:gt,async:!1,expects:null,requirement:we,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"slug",n,e),n}}}function Ot(t,n){return{kind:"validation",type:"some_item",reference:Ot,async:!1,expects:null,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.some(this.requirement)&&r(this,"item",e,s),e}}}function wt(t){return{kind:"transformation",type:"sort_items",reference:wt,async:!1,operation:t,"~run"(n){return n.value=n.value.sort(this.operation),n}}}function St(t,n){return{kind:"validation",type:"starts_with",reference:St,async:!1,expects:`"${t}"`,requirement:t,message:n,"~run"(e,s){return e.typed&&!e.value.startsWith(this.requirement)&&r(this,"start",e,s,{received:`"${e.value.slice(0,this.requirement.length)}"`}),e}}}function Bt(t,n){return{kind:"transformation",type:"stringify_json",reference:Bt,message:n,config:t,async:!1,"~run"(e,s){try{let u=JSON.stringify(e.value,this.config?.replacer,this.config?.space);u===void 0&&(r(this,"JSON",e,s),e.typed=!1),e.value=u}catch(u){if(u instanceof Error)r(this,"JSON",e,s,{received:`"${u.message}"`}),e.typed=!1;else throw u}return e}}}function At(t){return{kind:"metadata",type:"title",reference:At,title:t}}function Et(){return{kind:"transformation",type:"to_lower_case",reference:Et,async:!1,"~run"(t){return t.value=t.value.toLowerCase(),t}}}function Mt(t){return{kind:"transformation",type:"to_max_value",reference:Mt,async:!1,requirement:t,"~run"(n){return n.value=n.value>this.requirement?this.requirement:n.value,n}}}function bt(t){return{kind:"transformation",type:"to_min_value",reference:bt,async:!1,requirement:t,"~run"(n){return n.value=n.value<this.requirement?this.requirement:n.value,n}}}function Pt(){return{kind:"transformation",type:"to_upper_case",reference:Pt,async:!1,"~run"(t){return t.value=t.value.toUpperCase(),t}}}function Rt(t){return{kind:"transformation",type:"transform",reference:Rt,async:!1,operation:t,"~run"(n){return n.value=this.operation(n.value),n}}}function jt(t){return{kind:"transformation",type:"transform",reference:jt,async:!0,operation:t,async"~run"(n){return n.value=await this.operation(n.value),n}}}function vt(){return{kind:"transformation",type:"trim",reference:vt,async:!1,"~run"(t){return t.value=t.value.trim(),t}}}function qt(){return{kind:"transformation",type:"trim_end",reference:qt,async:!1,"~run"(t){return t.value=t.value.trimEnd(),t}}}function _t(){return{kind:"transformation",type:"trim_start",reference:_t,async:!1,"~run"(t){return t.value=t.value.trimStart(),t}}}function Wt(t){return{kind:"validation",type:"ulid",reference:Wt,async:!1,expects:null,requirement:Se,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"ULID",n,e),n}}}function Dt(t){return{kind:"validation",type:"url",reference:Dt,async:!1,expects:null,requirement(n){try{return new URL(n),!0}catch{return!1}},message:t,"~run"(n,e){return n.typed&&!this.requirement(n.value)&&r(this,"URL",n,e),n}}}function Vt(t){return{kind:"validation",type:"uuid",reference:Vt,async:!1,expects:null,requirement:Be,message:t,"~run"(n,e){return n.typed&&!this.requirement.test(n.value)&&r(this,"UUID",n,e),n}}}function Nt(t,n){return{kind:"validation",type:"value",reference:Nt,async:!1,expects:t instanceof Date?t.toJSON():f(t),requirement:t,message:n,"~run"(e,s){return e.typed&&!(this.requirement<=e.value&&this.requirement>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Ct(t,n){return{kind:"validation",type:"values",reference:Ct,async:!1,expects:`${k(t.map(e=>e instanceof Date?e.toJSON():f(e)),"|")}`,requirement:t,message:n,"~run"(e,s){return e.typed&&!this.requirement.some(u=>u<=e.value&&u>=e.value)&&r(this,"value",e,s,{received:e.value instanceof Date?e.value.toJSON():f(e.value)}),e}}}function Lt(t,n,e){return{kind:"validation",type:"words",reference:Lt,async:!1,expects:`${n}`,locales:t,requirement:n,message:e,"~run"(s,u){if(s.typed){let o=M(this.locales,s.value);o!==this.requirement&&r(this,"words",s,u,{received:`${o}`})}return s}}}function Su(t,n){let e=t["~run"]({value:n},{abortEarly:!0}).issues;if(e)throw new x(e)}function Bu(t,n){return{...t,get"~standard"(){return p(this)},"~run"(e,s){return t["~run"](e,{...s,...n})}}}function l(t,n,e){return typeof t.fallback=="function"?t.fallback(n,e):t.fallback}function Au(t,n){return{...t,fallback:n,get"~standard"(){return p(this)},"~run"(e,s){let u=t["~run"](e,s);return u.issues?{typed:!0,value:l(this,u,s)}:u}}}function Eu(t,n){return{...t,fallback:n,async:!0,get"~standard"(){return p(this)},async"~run"(e,s){let u=await t["~run"](e,s);return u.issues?{typed:!0,value:await l(this,u,s)}:u}}}function Mu(t){let n={};for(let e of t)if(e.path){let s=N(e);s?(n.nested||(n.nested={}),n.nested[s]?n.nested[s].push(e.message):n.nested[s]=[e.message]):n.other?n.other.push(e.message):n.other=[e.message]}else n.root?n.root.push(e.message):n.root=[e.message];return n}function bu(t,n){return{...t,"~run"(e,s){let u=e.issues&&[...e.issues];if(e=t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function Pu(t,n){return{...t,async:!0,async"~run"(e,s){let u=e.issues&&[...e.issues];if(e=await t["~run"](e,s),e.issues){for(let o of e.issues)if(!u?.includes(o)){let a=e.value;for(let I of n){let i=a[I],m={type:"unknown",origin:"value",input:a,key:I,value:i};if(o.path?o.path.push(m):o.path=[m],!i)break;a=i}}}return e}}}function y(t,n,e){return typeof t.default=="function"?t.default(n,e):t.default}function Ae(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Ae(t.entries[e]);return n}return"items"in t?t.items.map(Ae):y(t)}async function Ee(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await Ee(e)]))):"items"in t?Promise.all(t.items.map(Ee)):y(t)}function Ru(t){return R(t,"description")}function Me(t){if("entries"in t){let n={};for(let e in t.entries)n[e]=Me(t.entries[e]);return n}return"items"in t?t.items.map(Me):l(t)}async function be(t){return"entries"in t?Object.fromEntries(await Promise.all(Object.entries(t.entries).map(async([n,e])=>[n,await be(e)]))):"items"in t?Promise.all(t.items.map(be)):l(t)}function ju(t){let n={};function e(s){if("pipe"in s)for(let u of s.pipe)u.kind==="schema"&&"pipe"in u?e(u):u.kind==="metadata"&&u.type==="metadata"&&Object.assign(n,u.metadata)}return e(t),n}function vu(t){return R(t,"title")}function qu(t,n){return!t["~run"]({value:n},{abortEarly:!0}).issues}function Kt(){return{kind:"schema",type:"any",reference:Kt,expects:"any",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function $t(t,n){return{kind:"schema",type:"array",reference:$t,expects:"Array",async:!1,item:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<u.length;o++){let a=u[o],I=this.item["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Ft(t,n){return{kind:"schema",type:"array",reference:Ft,expects:"Array",async:!0,item:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(u.map(a=>this.item["~run"]({value:a},s)));for(let a=0;a<o.length;a++){let I=o[a];if(I.issues){let i={type:"array",origin:"value",input:u,key:a,value:u[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function zt(t){return{kind:"schema",type:"bigint",reference:zt,expects:"bigint",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="bigint"?n.typed=!0:r(this,"type",n,e),n}}}function Gt(t){return{kind:"schema",type:"blob",reference:Gt,expects:"Blob",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Blob?n.typed=!0:r(this,"type",n,e),n}}}function Ut(t){return{kind:"schema",type:"boolean",reference:Ut,expects:"boolean",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="boolean"?n.typed=!0:r(this,"type",n,e),n}}}function Jt(t,n){return{kind:"schema",type:"custom",reference:Jt,expects:"unknown",async:!1,check:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Xt(t,n){return{kind:"schema",type:"custom",reference:Xt,expects:"unknown",async:!0,check:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return await this.check(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Ht(t){return{kind:"schema",type:"date",reference:Ht,expects:"Date",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Date?isNaN(n.value)?r(this,"type",n,e,{received:'"Invalid Date"'}):n.typed=!0:r(this,"type",n,e),n}}}function Zt(t,n){let e=[];for(let s in t)(`${+s}`!==s||typeof t[s]!="string"||!Object.is(t[t[s]],+s))&&e.push(t[s]);return{kind:"schema",type:"enum",reference:Zt,expects:k(e.map(f),"|"),async:!1,enum:t,options:e,message:n,get"~standard"(){return p(this)},"~run"(s,u){return this.options.includes(s.value)?s.typed=!0:r(this,"type",s,u),s}}}function Qt(t,n){return{kind:"schema",type:"exact_optional",reference:Qt,expects:t.expects,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return this.wrapped["~run"](e,s)}}}function Yt(t,n){return{kind:"schema",type:"exact_optional",reference:Yt,expects:t.expects,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return this.wrapped["~run"](e,s)}}}function es(t){return{kind:"schema",type:"file",reference:es,expects:"File",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof File?n.typed=!0:r(this,"type",n,e),n}}}function ns(t){return{kind:"schema",type:"function",reference:ns,expects:"Function",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="function"?n.typed=!0:r(this,"type",n,e),n}}}function ts(t,n){return{kind:"schema",type:"instance",reference:ts,expects:t.name,async:!1,class:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value instanceof this.class?e.typed=!0:r(this,"type",e,s),e}}}function j(t,n){if(typeof t==typeof n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{value:t};if(t&&n&&t.constructor===Object&&n.constructor===Object){for(let e in n)if(e in t){let s=j(t[e],n[e]);if(s.issue)return s;t[e]=s.value}else t[e]=n[e];return{value:t}}if(Array.isArray(t)&&Array.isArray(n)&&t.length===n.length){for(let e=0;e<t.length;e++){let s=j(t[e],n[e]);if(s.issue)return s;t[e]=s.value}return{value:t}}}return{issue:!0}}function ss(t,n){return{kind:"schema",type:"intersect",reference:ss,expects:k(t.map(e=>e.expects),"&"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;for(let a of this.options){let I=a["~run"]({value:u},s);if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let a=1;a<o.length;a++){let I=j(e.value,o[a]);if(I.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=I.value}}}else r(this,"type",e,s);return e}}}function us(t,n){return{kind:"schema",type:"intersect",reference:us,expects:k(t.map(e=>e.expects),"&"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){if(this.options.length){let u=e.value,o;e.typed=!0;let a=await Promise.all(this.options.map(I=>I["~run"]({value:u},s)));for(let I of a){if(I.issues&&(e.issues?e.issues.push(...I.issues):e.issues=I.issues,s.abortEarly)){e.typed=!1;break}I.typed||(e.typed=!1),e.typed&&(o?o.push(I.value):o=[I.value])}if(e.typed){e.value=o[0];for(let I=1;I<o.length;I++){let i=j(e.value,o[I]);if(i.issue){r(this,"type",e,s,{received:"unknown"});break}e.value=i.value}}}else r(this,"type",e,s);return e}}}function rs(t){return{kind:"schema",type:"lazy",reference:rs,expects:"unknown",async:!1,getter:t,get"~standard"(){return p(this)},"~run"(n,e){return this.getter(n.value)["~run"](n,e)}}}function os(t){return{kind:"schema",type:"lazy",reference:os,expects:"unknown",async:!0,getter:t,get"~standard"(){return p(this)},async"~run"(n,e){return(await this.getter(n.value))["~run"](n,e)}}}function as(t,n){return{kind:"schema",type:"literal",reference:as,expects:f(t),async:!1,literal:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===this.literal?e.typed=!0:r(this,"type",e,s),e}}}function is(t,n){return{kind:"schema",type:"loose_object",reference:is,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly)for(let o in u)O(u,o)&&!(o in this.entries)&&(e.value[o]=u[o])}else r(this,"type",e,s);return e}}}function Is(t,n){return{kind:"schema",type:"loose_object",reference:Is,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly)for(let a in u)O(u,a)&&!(a in this.entries)&&(e.value[a]=u[a])}else r(this,"type",e,s);return e}}}function ps(t,n){return{kind:"schema",type:"loose_tuple",reference:ps,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}if(!e.issues||!s.abortEarly)for(let o=this.items.length;o<u.length;o++)e.value.push(u[o])}else r(this,"type",e,s);return e}}}function ms(t,n){return{kind:"schema",type:"loose_tuple",reference:ms,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}if(!e.issues||!s.abortEarly)for(let a=this.items.length;a<u.length;a++)e.value.push(u[a])}else r(this,"type",e,s);return e}}}function cs(t,n,e){return{kind:"schema",type:"map",reference:cs,expects:"Map",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;for(let[a,I]of o){let i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"map",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"map",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),s.value.set(i.value,m.value)}}else r(this,"type",s,u);return s}}}function Ts(t,n,e){return{kind:"schema",type:"map",reference:Ts,expects:"Map",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o instanceof Map){s.typed=!0,s.value=new Map;let a=await Promise.all([...o].map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"map",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"map",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),s.value.set(m.value,c.value)}}else r(this,"type",s,u);return s}}}function ds(t){return{kind:"schema",type:"nan",reference:ds,expects:"NaN",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return Number.isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function fs(t){return{kind:"schema",type:"never",reference:fs,expects:"never",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return r(this,"type",n,e),n}}}function ys(t,n){return{kind:"schema",type:"non_nullable",reference:ys,expects:"!null",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==null&&(e=this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function ls(t,n){return{kind:"schema",type:"non_nullable",reference:ls,expects:"!null",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==null&&(e=await this.wrapped["~run"](e,s)),e.value===null&&r(this,"type",e,s),e}}}function hs(t,n){return{kind:"schema",type:"non_nullish",reference:hs,expects:"(!null & !undefined)",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null||e.value===void 0||(e=this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function ks(t,n){return{kind:"schema",type:"non_nullish",reference:ks,expects:"(!null & !undefined)",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null||e.value===void 0||(e=await this.wrapped["~run"](e,s)),(e.value===null||e.value===void 0)&&r(this,"type",e,s),e}}}function L(t,n){return{kind:"schema",type:"non_optional",reference:L,expects:"!undefined",async:!1,wrapped:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value!==void 0&&(e=this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function K(t,n){return{kind:"schema",type:"non_optional",reference:K,expects:"!undefined",async:!0,wrapped:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value!==void 0&&(e=await this.wrapped["~run"](e,s)),e.value===void 0&&r(this,"type",e,s),e}}}function xs(t){return{kind:"schema",type:"null",reference:xs,expects:"null",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===null?n.typed=!0:r(this,"type",n,e),n}}}function gs(t,n){return{kind:"schema",type:"nullable",reference:gs,expects:`(${t.expects} | null)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Os(t,n){return{kind:"schema",type:"nullable",reference:Os,expects:`(${t.expects} | null)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===null&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function ws(t,n){return{kind:"schema",type:"nullish",reference:ws,expects:`(${t.expects} | null | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Ss(t,n){return{kind:"schema",type:"nullish",reference:Ss,expects:`(${t.expects} | null | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return(e.value===null||e.value===void 0)&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===null||e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Bs(t){return{kind:"schema",type:"number",reference:Bs,expects:"number",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="number"&&!isNaN(n.value)?n.typed=!0:r(this,"type",n,e),n}}}function As(t,n){return{kind:"schema",type:"object",reference:As,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}}else r(this,"type",e,s);return e}}}function Es(t,n){return{kind:"schema",type:"object",reference:Es,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break}else r(this,"type",e,s);return e}}}function Ms(t,n,e){return{kind:"schema",type:"object_with_rest",reference:Ms,expects:"Object",async:!1,entries:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in this.entries){let I=this.entries[a];if(a in o||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in o?o[a]:y(I),m=I["~run"]({value:i},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:i};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}m.typed||(s.typed=!1),s.value[a]=m.value}else if(I.fallback!==void 0)s.value[a]=l(I);else if(I.type!=="exact_optional"&&I.type!=="optional"&&I.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:o,key:a,value:o[a]}]}),u.abortEarly))break}if(!s.issues||!u.abortEarly){for(let a in o)if(O(o,a)&&!(a in this.entries)){let I=this.rest["~run"]({value:o[a]},u);if(I.issues){let i={type:"object",origin:"value",input:o,key:a,value:o[a]};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],s.issues?.push(m);if(s.issues||(s.issues=I.issues),u.abortEarly){s.typed=!1;break}}I.typed||(s.typed=!1),s.value[a]=I.value}}}else r(this,"type",s,u);return s}}}function bs(t,n,e){return{kind:"schema",type:"object_with_rest",reference:bs,expects:"Object",async:!0,entries:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let[a,I]=await Promise.all([Promise.all(Object.entries(this.entries).map(async([i,m])=>{if(i in o||(m.type==="exact_optional"||m.type==="optional"||m.type==="nullish")&&m.default!==void 0){let c=i in o?o[i]:await y(m);return[i,c,m,await m["~run"]({value:c},u)]}return[i,o[i],m,null]})),Promise.all(Object.entries(o).filter(([i])=>O(o,i)&&!(i in this.entries)).map(async([i,m])=>[i,m,await this.rest["~run"]({value:m},u)]))]);for(let[i,m,c,T]of a)if(T){if(T.issues){let d={type:"object",origin:"value",input:o,key:i,value:m};for(let g of T.issues)g.path?g.path.unshift(d):g.path=[d],s.issues?.push(g);if(s.issues||(s.issues=T.issues),u.abortEarly){s.typed=!1;break}}T.typed||(s.typed=!1),s.value[i]=T.value}else if(c.fallback!==void 0)s.value[i]=await l(c);else if(c.type!=="exact_optional"&&c.type!=="optional"&&c.type!=="nullish"&&(r(this,"key",s,u,{input:void 0,expected:`"${i}"`,path:[{type:"object",origin:"key",input:o,key:i,value:m}]}),u.abortEarly))break;if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"object",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value[i]=c.value}}else r(this,"type",s,u);return s}}}function $(t,n){return{kind:"schema",type:"optional",reference:$,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function F(t,n){return{kind:"schema",type:"optional",reference:F,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function z(t,n){return{kind:"schema",type:"picklist",reference:z,expects:k(t.map(f),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){return this.options.includes(e.value)?e.typed=!0:r(this,"type",e,s),e}}}function Ps(t){return{kind:"schema",type:"promise",reference:Ps,expects:"Promise",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value instanceof Promise?n.typed=!0:r(this,"type",n,e),n}}}function Rs(t,n,e){return{kind:"schema",type:"record",reference:Rs,expects:"Object",async:!1,key:t,value:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};for(let a in o)if(O(o,a)){let I=o[a],i=this.key["~run"]({value:a},u);if(i.issues){let c={type:"object",origin:"key",input:o,key:a,value:I};for(let T of i.issues)T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}let m=this.value["~run"]({value:I},u);if(m.issues){let c={type:"object",origin:"value",input:o,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],s.issues?.push(T);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}(!i.typed||!m.typed)&&(s.typed=!1),i.typed&&(s.value[i.value]=m.value)}}else r(this,"type",s,u);return s}}}function js(t,n,e){return{kind:"schema",type:"record",reference:js,expects:"Object",async:!0,key:t,value:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){s.typed=!0,s.value={};let a=await Promise.all(Object.entries(o).filter(([I])=>O(o,I)).map(([I,i])=>Promise.all([I,i,this.key["~run"]({value:I},u),this.value["~run"]({value:i},u)])));for(let[I,i,m,c]of a){if(m.issues){let T={type:"object",origin:"key",input:o,key:I,value:i};for(let d of m.issues)d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=m.issues),u.abortEarly){s.typed=!1;break}}if(c.issues){let T={type:"object",origin:"value",input:o,key:I,value:i};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}(!m.typed||!c.typed)&&(s.typed=!1),m.typed&&(s.value[m.value]=c.value)}}else r(this,"type",s,u);return s}}}function vs(t,n){return{kind:"schema",type:"set",reference:vs,expects:"Set",async:!1,value:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;for(let o of u){let a=this.value["~run"]({value:o},s);if(a.issues){let I={type:"set",origin:"value",input:u,key:null,value:o};for(let i of a.issues)i.path?i.path.unshift(I):i.path=[I],e.issues?.push(i);if(e.issues||(e.issues=a.issues),s.abortEarly){e.typed=!1;break}}a.typed||(e.typed=!1),e.value.add(a.value)}}else r(this,"type",e,s);return e}}}function qs(t,n){return{kind:"schema",type:"set",reference:qs,expects:"Set",async:!0,value:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u instanceof Set){e.typed=!0,e.value=new Set;let o=await Promise.all([...u].map(async a=>[a,await this.value["~run"]({value:a},s)]));for(let[a,I]of o){if(I.issues){let i={type:"set",origin:"value",input:u,key:null,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.add(I.value)}}else r(this,"type",e,s);return e}}}function _s(t,n){return{kind:"schema",type:"strict_object",reference:_s,expects:"Object",async:!1,entries:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};for(let o in this.entries){let a=this.entries[o];if(o in u||(a.type==="exact_optional"||a.type==="optional"||a.type==="nullish")&&a.default!==void 0){let I=o in u?u[o]:y(a),i=a["~run"]({value:I},s);if(i.issues){let m={type:"object",origin:"value",input:u,key:o,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value[o]=i.value}else if(a.fallback!==void 0)e.value[o]=l(a);else if(a.type!=="exact_optional"&&a.type!=="optional"&&a.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${o}"`,path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]}),s.abortEarly))break}if(!e.issues||!s.abortEarly){for(let o in u)if(!(o in this.entries)){r(this,"key",e,s,{input:o,expected:"never",path:[{type:"object",origin:"key",input:u,key:o,value:u[o]}]});break}}}else r(this,"type",e,s);return e}}}function Ws(t,n){return{kind:"schema",type:"strict_object",reference:Ws,expects:"Object",async:!0,entries:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(u&&typeof u=="object"){e.typed=!0,e.value={};let o=await Promise.all(Object.entries(this.entries).map(async([a,I])=>{if(a in u||(I.type==="exact_optional"||I.type==="optional"||I.type==="nullish")&&I.default!==void 0){let i=a in u?u[a]:await y(I);return[a,i,I,await I["~run"]({value:i},s)]}return[a,u[a],I,null]}));for(let[a,I,i,m]of o)if(m){if(m.issues){let c={type:"object",origin:"value",input:u,key:a,value:I};for(let T of m.issues)T.path?T.path.unshift(c):T.path=[c],e.issues?.push(T);if(e.issues||(e.issues=m.issues),s.abortEarly){e.typed=!1;break}}m.typed||(e.typed=!1),e.value[a]=m.value}else if(i.fallback!==void 0)e.value[a]=await l(i);else if(i.type!=="exact_optional"&&i.type!=="optional"&&i.type!=="nullish"&&(r(this,"key",e,s,{input:void 0,expected:`"${a}"`,path:[{type:"object",origin:"key",input:u,key:a,value:I}]}),s.abortEarly))break;if(!e.issues||!s.abortEarly){for(let a in u)if(!(a in this.entries)){r(this,"key",e,s,{input:a,expected:"never",path:[{type:"object",origin:"key",input:u,key:a,value:u[a]}]});break}}}else r(this,"type",e,s);return e}}}function Ds(t,n){return{kind:"schema",type:"strict_tuple",reference:Ds,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function Vs(t,n){return{kind:"schema",type:"strict_tuple",reference:Vs,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}!(e.issues&&s.abortEarly)&&this.items.length<u.length&&r(this,"type",e,s,{input:u[this.items.length],expected:"never",path:[{type:"array",origin:"value",input:u,key:this.items.length,value:u[this.items.length]}]})}else r(this,"type",e,s);return e}}}function Ns(t){return{kind:"schema",type:"string",reference:Ns,expects:"string",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="string"?n.typed=!0:r(this,"type",n,e),n}}}function Cs(t){return{kind:"schema",type:"symbol",reference:Cs,expects:"symbol",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return typeof n.value=="symbol"?n.typed=!0:r(this,"type",n,e),n}}}function Ls(t,n){return{kind:"schema",type:"tuple",reference:Ls,expects:"Array",async:!1,items:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];for(let o=0;o<this.items.length;o++){let a=u[o],I=this.items[o]["~run"]({value:a},s);if(I.issues){let i={type:"array",origin:"value",input:u,key:o,value:a};for(let m of I.issues)m.path?m.path.unshift(i):m.path=[i],e.issues?.push(m);if(e.issues||(e.issues=I.issues),s.abortEarly){e.typed=!1;break}}I.typed||(e.typed=!1),e.value.push(I.value)}}else r(this,"type",e,s);return e}}}function Ks(t,n){return{kind:"schema",type:"tuple",reference:Ks,expects:"Array",async:!0,items:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u=e.value;if(Array.isArray(u)){e.typed=!0,e.value=[];let o=await Promise.all(this.items.map(async(a,I)=>{let i=u[I];return[I,i,await a["~run"]({value:i},s)]}));for(let[a,I,i]of o){if(i.issues){let m={type:"array",origin:"value",input:u,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],e.issues?.push(c);if(e.issues||(e.issues=i.issues),s.abortEarly){e.typed=!1;break}}i.typed||(e.typed=!1),e.value.push(i.value)}}else r(this,"type",e,s);return e}}}function $s(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:$s,expects:"Array",async:!1,items:t,rest:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];for(let a=0;a<this.items.length;a++){let I=o[a],i=this.items[a]["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}if(!s.issues||!u.abortEarly)for(let a=this.items.length;a<o.length;a++){let I=o[a],i=this.rest["~run"]({value:I},u);if(i.issues){let m={type:"array",origin:"value",input:o,key:a,value:I};for(let c of i.issues)c.path?c.path.unshift(m):c.path=[m],s.issues?.push(c);if(s.issues||(s.issues=i.issues),u.abortEarly){s.typed=!1;break}}i.typed||(s.typed=!1),s.value.push(i.value)}}else r(this,"type",s,u);return s}}}function Fs(t,n,e){return{kind:"schema",type:"tuple_with_rest",reference:Fs,expects:"Array",async:!0,items:t,rest:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(Array.isArray(o)){s.typed=!0,s.value=[];let[a,I]=await Promise.all([Promise.all(this.items.map(async(i,m)=>{let c=o[m];return[m,c,await i["~run"]({value:c},u)]})),Promise.all(o.slice(this.items.length).map(async(i,m)=>[m+this.items.length,i,await this.rest["~run"]({value:i},u)]))]);for(let[i,m,c]of a){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}if(!s.issues||!u.abortEarly)for(let[i,m,c]of I){if(c.issues){let T={type:"array",origin:"value",input:o,key:i,value:m};for(let d of c.issues)d.path?d.path.unshift(T):d.path=[T],s.issues?.push(d);if(s.issues||(s.issues=c.issues),u.abortEarly){s.typed=!1;break}}c.typed||(s.typed=!1),s.value.push(c.value)}}else r(this,"type",s,u);return s}}}function zs(t){return{kind:"schema",type:"undefined",reference:zs,expects:"undefined",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function Gs(t,n){return{kind:"schema",type:"undefinedable",reference:Gs,expects:`(${t.expects} | undefined)`,async:!1,wrapped:t,default:n,get"~standard"(){return p(this)},"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function Us(t,n){return{kind:"schema",type:"undefinedable",reference:Us,expects:`(${t.expects} | undefined)`,async:!0,wrapped:t,default:n,get"~standard"(){return p(this)},async"~run"(e,s){return e.value===void 0&&(this.default!==void 0&&(e.value=await y(this,e,s)),e.value===void 0)?(e.typed=!0,e):this.wrapped["~run"](e,s)}}}function v(t){let n;if(t)for(let e of t)n?n.push(...e.issues):n=e.issues;return n}function Js(t,n){return{kind:"schema",type:"union",reference:Js,expects:k(t.map(e=>e.expects),"|"),async:!1,options:t,message:n,get"~standard"(){return p(this)},"~run"(e,s){let u,o,a;for(let I of this.options){let i=I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:v(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:v(a)})}return e}}}function Xs(t,n){return{kind:"schema",type:"union",reference:Xs,expects:k(t.map(e=>e.expects),"|"),async:!0,options:t,message:n,get"~standard"(){return p(this)},async"~run"(e,s){let u,o,a;for(let I of this.options){let i=await I["~run"]({value:e.value},s);if(i.typed)if(i.issues)o?o.push(i):o=[i];else{u=i;break}else a?a.push(i):a=[i]}if(u)return u;if(o){if(o.length===1)return o[0];r(this,"type",e,s,{issues:v(o)}),e.typed=!0}else{if(a?.length===1)return a[0];r(this,"type",e,s,{issues:v(a)})}return e}}}function Hs(){return{kind:"schema",type:"unknown",reference:Hs,expects:"unknown",async:!1,get"~standard"(){return p(this)},"~run"(t){return t.typed=!0,t}}}function Zs(t,n,e){return{kind:"schema",type:"variant",reference:Zs,expects:"Object",async:!1,key:t,options:n,message:e,get"~standard"(){return p(this)},"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=(T,d)=>{for(let g of T.options){if(g.type==="variant")c(g,new Set(d).add(g.key));else{let q=!0,S=0;for(let h of d){let B=g.entries[h];if(h in o?B["~run"]({typed:!1,value:o[h]},{abortEarly:!0}).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){q=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(g.entries[h].expects);break}S++}if(q){let h=g["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function Qs(t,n,e){return{kind:"schema",type:"variant",reference:Qs,expects:"Object",async:!0,key:t,options:n,message:e,get"~standard"(){return p(this)},async"~run"(s,u){let o=s.value;if(o&&typeof o=="object"){let a,I=0,i=this.key,m=[],c=async(T,d)=>{for(let g of T.options){if(g.type==="variant")await c(g,new Set(d).add(g.key));else{let q=!0,S=0;for(let h of d){let B=g.entries[h];if(h in o?(await B["~run"]({typed:!1,value:o[h]},{abortEarly:!0})).issues:B.type!=="exact_optional"&&B.type!=="optional"&&B.type!=="nullish"){q=!1,i!==h&&(I<S||I===S&&h in o&&!(i in o))&&(I=S,i=h,m=[]),i===h&&m.push(g.entries[h].expects);break}S++}if(q){let h=await g["~run"]({value:o},u);(!a||!a.typed&&h.typed)&&(a=h)}}if(a&&!a.issues)break}};if(await c(this,new Set([this.key])),a)return a;r(this,"type",s,u,{input:o[i],expected:k(m,"|"),path:[{type:"object",origin:"value",input:o,key:i,value:o[i]}]})}else r(this,"type",s,u);return s}}}function Ys(t){return{kind:"schema",type:"void",reference:Ys,expects:"void",async:!1,message:t,get"~standard"(){return p(this)},"~run"(n,e){return n.value===void 0?n.typed=!0:r(this,"type",n,e),n}}}function _u(t,n){return z(Object.keys(t.entries),n)}function Wu(t,n){return{...t,get"~standard"(){return p(this)},"~run"(e,s){return t["~run"](e,{...s,message:n})}}}function Du(t,n){let e={...t.entries};for(let s of n)delete e[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Pe(t,n,e){let s=t["~run"]({value:n},w(e));if(s.issues)throw new x(s.issues);return s.value}async function Re(t,n,e){let s=await t["~run"]({value:n},w(e));if(s.issues)throw new x(s.issues);return s.value}function Vu(t,n){let e=s=>Pe(t,s,n);return e.schema=t,e.config=n,e}function Nu(t,n){let e=s=>Re(t,s,n);return e.schema=t,e.config=n,e}function Cu(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?$(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Lu(t,n){let e={};for(let s in t.entries)e[s]=!n||n.includes(s)?F(t.entries[s]):t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function Ku(t,n){let e={};for(let s of n)e[s]=t.entries[s];return{...t,entries:e,get"~standard"(){return p(this)}}}function $u(...t){return{...t[0],pipe:t,get"~standard"(){return p(this)},"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=s["~run"](n,e))}return n}}}function Fu(...t){return{...t[0],pipe:t,async:!0,get"~standard"(){return p(this)},async"~run"(n,e){for(let s of t)if(s.kind!=="metadata"){if(n.issues&&(s.kind==="schema"||s.kind==="transformation")){n.typed=!1;break}(!n.issues||!e.abortEarly&&!e.abortPipeEarly)&&(n=await s["~run"](n,e))}return n}}}function zu(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?L(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function Gu(t,n,e){let s=Array.isArray(n)?n:void 0,u=Array.isArray(n)?e:n,o={};for(let a in t.entries)o[a]=!s||s.includes(a)?K(t.entries[a],u):t.entries[a];return{...t,entries:o,get"~standard"(){return p(this)}}}function je(t,n,e){let s=t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}async function ve(t,n,e){let s=await t["~run"]({value:n},w(e));return{typed:s.typed,success:!s.issues,output:s.value,issues:s.issues}}function Uu(t,n){let e=s=>je(t,s,n);return e.schema=t,e.config=n,e}function Ju(t,n){let e=s=>ve(t,s,n);return e.schema=t,e.config=n,e}function Xu(t){let n="";for(let e of t){n&&(n+=`
`),n+=`\xD7 ${e.message}`;let s=N(e);s&&(n+=`
  \u2192 at ${s}`)}return n}function Hu(t){return t.wrapped}0&&(module.exports={BASE64_REGEX,BIC_REGEX,CUID2_REGEX,DECIMAL_REGEX,DIGITS_REGEX,EMAIL_REGEX,EMOJI_REGEX,HEXADECIMAL_REGEX,HEX_COLOR_REGEX,IMEI_REGEX,IPV4_REGEX,IPV6_REGEX,IP_REGEX,ISO_DATE_REGEX,ISO_DATE_TIME_REGEX,ISO_TIMESTAMP_REGEX,ISO_TIME_REGEX,ISO_TIME_SECOND_REGEX,ISO_WEEK_REGEX,MAC48_REGEX,MAC64_REGEX,MAC_REGEX,NANO_ID_REGEX,OCTAL_REGEX,RFC_EMAIL_REGEX,SLUG_REGEX,ULID_REGEX,UUID_REGEX,ValiError,_addIssue,_getByteCount,_getGraphemeCount,_getLastMetadata,_getStandardProps,_getWordCount,_isLuhnAlgo,_isValidObjectKey,_joinExpects,_stringify,any,args,argsAsync,array,arrayAsync,assert,awaitAsync,base64,bic,bigint,blob,boolean,brand,bytes,check,checkAsync,checkItems,checkItemsAsync,config,creditCard,cuid2,custom,customAsync,date,decimal,deleteGlobalConfig,deleteGlobalMessage,deleteSchemaMessage,deleteSpecificMessage,description,digits,email,emoji,empty,endsWith,entries,entriesFromList,entriesFromObjects,enum:null,enum_,everyItem,exactOptional,exactOptionalAsync,excludes,fallback,fallbackAsync,file,filterItems,findItem,finite,flatten,flavor,forward,forwardAsync,function:null,function_,getDefault,getDefaults,getDefaultsAsync,getDescription,getDotPath,getFallback,getFallbacks,getFallbacksAsync,getGlobalConfig,getGlobalMessage,getMetadata,getSchemaMessage,getSpecificMessage,getTitle,graphemes,gtValue,hash,hexColor,hexadecimal,imei,includes,instance,integer,intersect,intersectAsync,ip,ipv4,ipv6,is,isOfKind,isOfType,isValiError,isoDate,isoDateTime,isoTime,isoTimeSecond,isoTimestamp,isoWeek,keyof,lazy,lazyAsync,length,literal,looseObject,looseObjectAsync,looseTuple,looseTupleAsync,ltValue,mac,mac48,mac64,map,mapAsync,mapItems,maxBytes,maxEntries,maxGraphemes,maxLength,maxSize,maxValue,maxWords,message,metadata,mimeType,minBytes,minEntries,minGraphemes,minLength,minSize,minValue,minWords,multipleOf,nan,nanoid,never,nonEmpty,nonNullable,nonNullableAsync,nonNullish,nonNullishAsync,nonOptional,nonOptionalAsync,normalize,notBytes,notEntries,notGraphemes,notLength,notSize,notValue,notValues,notWords,null:null,null_,nullable,nullableAsync,nullish,nullishAsync,number,object,objectAsync,objectWithRest,objectWithRestAsync,octal,omit,optional,optionalAsync,parse,parseAsync,parseJson,parser,parserAsync,partial,partialAsync,partialCheck,partialCheckAsync,pick,picklist,pipe,pipeAsync,promise,rawCheck,rawCheckAsync,rawTransform,rawTransformAsync,readonly,record,recordAsync,reduceItems,regex,required,requiredAsync,returns,returnsAsync,rfcEmail,safeInteger,safeParse,safeParseAsync,safeParser,safeParserAsync,set,setAsync,setGlobalConfig,setGlobalMessage,setSchemaMessage,setSpecificMessage,size,slug,someItem,sortItems,startsWith,strictObject,strictObjectAsync,strictTuple,strictTupleAsync,string,stringifyJson,summarize,symbol,title,toLowerCase,toMaxValue,toMinValue,toUpperCase,transform,transformAsync,trim,trimEnd,trimStart,tuple,tupleAsync,tupleWithRest,tupleWithRestAsync,ulid,undefined,undefined_,undefinedable,undefinedableAsync,union,unionAsync,unknown,unwrap,url,uuid,value,values,variant,variantAsync,void:null,void_,words});
