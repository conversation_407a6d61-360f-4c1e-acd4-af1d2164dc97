<script lang="ts" setup>
const props = defineProps({
	icon: {
		type: [Object, Function],
		default: null
	},
	title: {
		type: String,
		default: ''
	},
	desc: {
		type: String,
		default: ''
	},
	btnText: {
		type: String,
		default: ''
	},
	btnLink: {
		type: String,
		default: ''
	},
	btnIcon: {
		type: [Object, Function],
		default: null
	},
	gradCol: {
		type: String,
		default: 'red-orange'
	},
	isRegister: {
		type: Boolean,
		default: false
	}
});

import { toTypedSchema } from '@vee-validate/yup';
import * as yup from 'yup';

const schema = toTypedSchema(
	yup.object({
		email: yup.string().email().required(),
		cbt: yup.boolean().default(false)
	})
);

const { handleSubmit, errors: regErrors, resetForm } = useForm({
	validationSchema: schema
});

const { value: regEmail } = useField('email');
const { value: regCbt } = useField('cbt');

const showModal = ref(false);
const openModal = () => showModal.value = true;
//const closeModal = () => showModal.value = false;

const token = ref('');
const turnstile = ref(null);

const onSubmitValidate = handleSubmit(() => {
	/*console.log('Form submitted:', {
		email: regEmail.value,
		cbt: regCbt.value,
		date: new Date()
	});
	
	openModal();
	resetForm();*/
	
	onSubmit();
});

async function onSubmit() {
	if (!token.value) {
		alert('Please verify (Turnstile)')
		return
	}
	
	const res = await $fetch('/api/contact', {
		method: 'POST',
		body: { name: name.value, token: token.value }
	})
	console.log(res)
}
</script>

<template>
	<div class="relative inline-block p-[2px] rounded-xl translate-y-0 hover:-translate-y-0.5 transition-transform ease-out duration-300 group">
		<div v-if="props.gradCol === 'red-orange'" class="absolute inset-0 rounded-xl bg-gradient-to-r 
			from-red-400/20 to-orange-400/20 dark:from-red-400/30 dark:to-orange-400/30 
			group-hover:from-orange-500/70 group-hover:to-red-400/70 group-hover:dark:from-orange-500/70 group-hover:dark:to-red-400/70 
			[mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] 
			[mask-composite:exclude] transition-colors ease-out duration-300">
		</div>
		<div v-else-if="props.gradCol === 'blue-cyan'" class="absolute inset-0 rounded-xl bg-gradient-to-r 
			from-blue-400/20 to-cyan-400/20 dark:from-blue-400/30 dark:to-cyan-400/30 
			group-hover:from-cyan-400/70 group-hover:to-blue-400/70  
			[mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] 
			[mask-composite:exclude] transition-colors ease-out duration-300">
		</div>
		<div v-else-if="props.gradCol === 'cyan-purple'" class="absolute inset-0 rounded-xl bg-gradient-to-r 
			from-cyan-400/20 to-purple-400/20 dark:from-cyan-400/30 dark:to-purple-400/30 
			group-hover:from-purple-400/70 group-hover:to-cyan-400/70 
			[mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] 
			[mask-composite:exclude] transition-colors ease-out duration-300">
		</div>

		<div class="relative h-full card bg-base-200 card-md lg:card-lg rounded-[10px] shadow-md group-hover:shadow-lg">
			<div class="card-body gap-y-3">
				<div class="flex items-center">
					<component v-if="props.icon" :is="props.icon" class="size-6 me-3" />
					<h2 class="card-title">{{ props.title }}</h2>
				</div>
				
				<p class="mt-1 mb-3">{{ props.desc }}</p>
				<div class="justify-end card-actions">
					<NuxtLink v-if="!props.isRegister" :to="props.btnLink">
						<button v-if="props.gradCol === 'red-orange'" class="btn rounded-lg bg-gradient-to-r 
							from-red-300/50 to-orange-300/50 hover:from-red-300 hover:to-orange-300 
							dark:from-red-500/30 dark:to-orange-500/30 dark:hover:from-red-500/70 dark:hover:to-orange-500/70 
							px-5 transition-colors ease-out duration-300">
							{{ props.btnText }}
							<component v-if="props.btnIcon" :is="props.btnIcon" class="size-4 ms-1" />
						</button>
						<button v-else-if="props.gradCol === 'blue-cyan'" class="btn rounded-lg bg-gradient-to-r 
							from-blue-300/50 to-cyan-300/50 hover:from-blue-300 hover:to-cyan-300 
							dark:from-blue-500/30 dark:to-cyan-500/30 dark:hover:from-blue-500/70 dark:hover:to-cyan-500/70 
							px-5 transition-colors ease-out duration-300">
							{{ props.btnText }}
							<component v-if="props.btnIcon" :is="props.btnIcon" class="size-4 ms-1" />
						</button>
						<button v-else-if="props.gradCol === 'cyan-purple'" class="btn rounded-lg bg-gradient-to-r 
							from-cyan-300/50 to-purple-300/50 hover:from-cyan-300 hover:to-purple-300 
							dark:from-cyan-500/30 dark:to-purple-500/30 dark:hover:from-cyan-500/70 dark:hover:to-purple-500/50 
							px-5 transition-colors ease-out duration-300">
							{{ props.btnText }}
							<component v-if="props.btnIcon" :is="props.btnIcon" class="size-4 ms-1" />
						</button>
					</NuxtLink>
					<div v-else>
						<form @submit.prevent="onSubmitValidate" class="flex flex-col md:flex-row gap-4">
							<div>
								<NuxtTurnstile v-model="token" ref="turnstile" />
							</div>
							<div>
								<label class="floating-label">
									<span>{{ $t('action.registerLabel') }}</span>
									<input name="email" autocomplete="email" v-model="regEmail" type="email" placeholder="<EMAIL>" class="input validator min-w-53 rounded-lg border-1 focus:border-0 border-orange-300" />
								</label>

								<label class="label flex mt-4 md:mt-2">
									<input name="cbt" type="checkbox" v-model="regCbt" class="checkbox bg-transparent border-purple-300 checked:border-orange-300 checked:bg-orange-200/70 text-orange-800" />
									<span class="text-sm">{{ $t('action.checkCbt') }}</span>
								</label>
							
							</div>
							
							<button class="btn rounded-lg bg-gradient-to-r 
								from-red-300/50 to-orange-300/50 hover:from-red-300 hover:to-orange-300 
								dark:from-red-500/30 dark:to-orange-500/30 dark:hover:from-red-500/70 dark:hover:to-orange-500/70 
								px-5 transition-colors ease-out duration-300">
								{{ $t('action.register') }}
							</button>
						</form>
					</div>
				</div>
			</div>
		</div>

		<!-- Modal -->
		<Teleport v-if="props.isRegister" to="body">
			<dialog id="register-modal" class="modal z-50" :open="showModal" >
				<div class="modal-box">
					<h3 class="text-lg font-bold">Hello!</h3>
					<p class="py-4">Press ESC key or click the button below to close</p>
					<div class="modal-action">
						<form method="dialog">
							<button class="btn" @click="showModal = false">Close</button>
						</form>
					</div>
				</div>
			</dialog>
		</Teleport>
	</div>
</template>
