export default defineI18nLocale(async locale => {
	return {
		lang: {
			name: 'Indonesia',
			en: 'Inggris',
			id: 'Indonesia'
		},
		menu: {
			overview: {
				name: '<PERSON><PERSON><PERSON><PERSON>',
				features: '<PERSON>tur'
			},
			dev: {
				name: '<PERSON><PERSON><PERSON>nga<PERSON>',
				progress: '<PERSON><PERSON><PERSON><PERSON>',
				bugReport: '<PERSON><PERSON><PERSON> Bug',
				featureReq: '<PERSON><PERSON><PERSON><PERSON>tur',
				patchNote: 'Catatan Patch',
				blog: 'Blog'
			},
			download: 'Unduh',
			support: 'Bantuan'
		},
		footer: {
			slogan: 'Banyak Jalan untuk Berkarya. Banyak Jalan untuk Bermain.',
			account: '<PERSON><PERSON><PERSON>',
			contact1: 'Butuh Sesuatu?',
			contact2: '<PERSON><PERSON>ng<PERSON>'
		},
		home: {
			title: '<PERSON>rand<PERSON>',
			desc: '<PERSON><PERSON> beranda'
		}
	}
});
