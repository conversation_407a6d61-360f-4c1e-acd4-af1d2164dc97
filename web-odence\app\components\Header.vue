<script lang="ts" setup>
import { ChevronDownIcon } from '@heroicons/vue/24/solid';
import { ChevronDoubleDownIcon } from '@heroicons/vue/24/solid';

const { scrollToElement } = useScroll();		// based on id name
let colorMode = {
	value: 'dark'
}

const logo = computed(() => {
	return colorMode.value === 'dark' ? 'Game-1-Logo-1-Dark.png?updatedAt=1759417763418' : 'Game-1-Logo-1-Light.png?updatedAt=1759417792592'
});

function computedT(name: string) {
	return computed(() => $t(name));
}

const menus = [
	{ name: computedT('menu.dev.name'), link: '/progress', items: [
		{ name: computedT('menu.dev.progress'), link: '/progress' },
		{ name: computedT('menu.dev.blog'), link: '/blog' }
	] }
];

// later for complete version
/*const menus = [
	{ name: computedMenu('menu.overview.name'), link: '/home', items: [
		{ name: computedMenu('menu.overview.features'), link: '/home#features' }
	] },
	{ name: computedMenu('menu.dev.name'), link: '/progress', items: [
		{ name: computedMenu('menu.dev.progress'), link: '/progress' },
		{ name: computedMenu('menu.dev.bugReport'), link: '/bug-report' },
		{ name: computedMenu('menu.dev.featureReq'), link: '/feature-request' },
		{ name: computedMenu('menu.dev.patchNote'), link: '/patch-notes' },
		{ name: computedMenu('menu.dev.blog'), link: '/blog' }
	] },
	{ name: computedMenu('menu.support'), link: '/support', items: [] },
	{ name: computedMenu('menu.download'), link: '/download', items: [] }
];*/

onMounted(() => {
	colorMode = useColorMode();

	if (!document.documentElement.hasAttribute('data-theme')) {
		document.documentElement.setAttribute('data-theme', colorMode.value);
		document.documentElement.classList.toggle('dark', colorMode.value === 'dark');
	}
});

</script>

<template>
	<nav class="navbar inset-x-0 bg-base-300/90 fixed top-0 z-20 px-6 sm:px-9 md:px-12 lg:px-15 py-2 backdrop-blur-sm" :class="(colorMode.value === 'dark') ? 'shadow-lg' : 'shadow-md'">
		<div class="flex items-center justify-center md:justify-between w-full">
			<!-- Left-Center -->
			<div class="flex items-center sm:ms-3">
				<!-- Menu (Md-) -->
				<div class="flex lg:hidden dropdown">
					<div tabindex="0" aria-label="Menu" role="button" class="swap swap-active me-2 flex justify-center focus:-rotate-180 lg:hidden duration-200 ease-out transition-all hover:-translate-y-0.5 hover:text-orange-700 hover:dark:text-orange-200">
						<ChevronDoubleDownIcon aria-hidden="true" aria-label="Menu Icon" class="swap-on size-4 sm:size-6" />
					</div>
					<ul tabindex="0" aria-label="Menu List" role="menu" class="menu dropdown-content bg-base-100 rounded-box z-1 w-52 mt-12 shadow">
						<li v-for="(menu, menuIdx) in menus" :key="menuIdx">
							<div v-if="menu.items.length > 0" class="dropdown dropdown-hover w-fit" role="tab">
								<NuxtLink :to="menu.link" @click.prevent="scrollToElement('')" class="font-medium text-orange-700 dark:text-orange-200">{{ menu.name }}</NuxtLink>
								<ul class="p-2 max-h-24 overflow-y-auto">
									<li v-for="(item, itemIdx) in menu.items" :key="itemIdx"><NuxtLink :to="item.link" @click.prevent="scrollToElement('')" class="text-sm">{{ item.name }}</NuxtLink></li>
								</ul>
							</div>
							<div v-else>
								<NuxtLink :to="menu.link" @click.prevent="scrollToElement('')" class="font-medium text-orange-700 dark:text-orange-200">{{ menu.name }}</NuxtLink>
							</div>
						</li>

						<li class="xs:hidden">
							<div class="border-t-3 dark:border-base-200 border-base-300 mt-2 flex items-center ">
								<Theme :classes="'flex-none'" />
								<Locale :type="'button-mobile'" :dropdownClass="'grow justify-end'" :itemClass="'-ms-11 mt-15'" />
							</div>
						</li>
					</ul>
				</div>

				<!-- Logo -->
				<ClientOnly>
					<NuxtLink to="/home" @click.prevent="scrollToElement('')">
						<NuxtPicture provider="imagekit" :src="logo" alt="Logo" :imgAttrs="{ class: 'max-h-9' }" />
					</NuxtLink>
				</ClientOnly>

				<!-- Menu (Lg+) -->
				<div class="hidden lg:grid grid-cols-[auto_auto_auto_auto] tabs tabs-lg items-center justify-center gap-2 xl:gap-x-3 ms-5 xl:ms-15 w-auto">
					<div v-for="(menu, menuIdx) in menus" :key="menuIdx">
						<div v-if="menu.items.length > 0" class="dropdown dropdown-center dropdown-hover w-fit" role="tab">
							<a href="#" tabindex="0" role="button" class="tab nav-menu gap-1 xl:gap-2 group max-xl:px-2">
								<p class="text-sm xl:text-lg">{{ menu.name }}</p>
								<ChevronDownIcon class="size-3 xl:size-5 transition-transform duration-200 group-hover:rotate-180" />
							</a>
							<ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-1 w-52 p-2 shadow-md">
								<li v-for="(item, itemIdx) in menu.items" :key="itemIdx"><NuxtLink :to="item.link" @click.prevent="scrollToElement('')">{{ item.name }}</NuxtLink></li>
							</ul> 
						</div>
						<div v-else>
							<NuxtLink :to="menu.link" @click.prevent="scrollToElement('')" class="tab nav-menu max-xl:px-2">
								<p class="text-sm xl:text-lg" :class="(menuIdx === 3) ? 'font-bold text-orange-700 dark:text-orange-200' : ''">{{ menu.name }}</p>
							</NuxtLink>
						</div>
					</div>
				</div>
			</div>
			
			<!-- Right-Center -->			
			<div class="hidden md:flex items-center">
				<Locale class="block xl:hidden me-5" :type="'button-icon'" :dropdownClass="'dropdown-bottom dropdown-end'" :itemClass="'mt-5'" />
				<Locale class="hidden xl:block me-5" :type="'button'" :dropdownClass="'dropdown-bottom dropdown-end'" :itemClass="'mt-5'" />
				<Theme />
			</div>
		</div>
	</nav>
</template>
