<script lang="ts" setup>
import { SunIcon } from '@heroicons/vue/24/solid';
import { MoonIcon } from '@heroicons/vue/24/solid';

const props = defineProps({
	classes: {
		type: String,
		default: ''
	}
});

const colorMode = useColorMode();

const toggleTheme = () => {
	const newTheme = colorMode.value === 'dark' ? 'light' : 'dark';

	colorMode.preference = newTheme;
	document.documentElement.setAttribute('data-theme', newTheme);
	document.documentElement.classList.toggle('dark', newTheme === 'dark');
};

</script>

<template>
	<ClientOnly>
		<label :class="classes" class="swap swap-rotate" aria-label="Theme">
			<input type="checkbox" @change="toggleTheme()" :checked="colorMode.value === 'dark'" />
			<MoonIcon class="swap-on size-4 md:size-5" />
			<SunIcon class="swap-off size-4 md:size-5" />
		</label>
	</ClientOnly>
</template>
