/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/image" />
/// <reference types="@nuxt/fonts" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@nuxtjs/turnstile" />
/// <reference types="@vee-validate/nuxt" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference path="types/modules.d.ts" />
/// <reference path="types/runtime-config.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference types="nuxt" />
/// <reference path="eslint-typegen.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />

export {}
