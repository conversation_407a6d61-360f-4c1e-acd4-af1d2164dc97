'use strict';

const fs = require('node:fs');
const node_url = require('node:url');
const kit = require('@nuxt/kit');
const pathe = require('pathe');
const defu = require('defu');

var _documentCurrentScript = typeof document !== 'undefined' ? document.currentScript : null;
function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const fs__default = /*#__PURE__*/_interopDefaultCompat(fs);

const module$1 = kit.defineNuxtModule({
  meta: {
    configKey: "turnstile",
    name: "@nuxtjs/turnstile",
    compatibility: {
      bridge: false,
      nuxt: ">=3"
    }
  },
  defaults: (nuxt) => ({
    secretKey: nuxt.options.dev ? "1x0000000000000000000000000000000AA" : void 0,
    siteKey: nuxt.options.dev ? "1x00000000000000000000AA" : void 0,
    addValidateEndpoint: false
  }),
  async setup(options, nuxt) {
    const logger = kit.useLogger("turnstile");
    const siteKey = options.siteKey || nuxt.options.runtimeConfig.public?.turnstile?.siteKey;
    if (!siteKey) {
      logger.warn(
        "No site key was provided. Make sure you pass one at runtime by setting NUXT_PUBLIC_TURNSTILE_SITE_KEY."
      );
    }
    if (options.secretKeyPath) {
      try {
        options.secretKey = fs__default.readFileSync(
          pathe.resolve(nuxt.options.rootDir, options.secretKeyPath),
          "utf-8"
        );
      } catch {
      }
      if (!options.secretKey) {
        logger.warn(`No secret key present in \`${options.secretKeyPath}\`.`);
      }
    }
    const runtimeDir = node_url.fileURLToPath(new URL("./runtime", (typeof document === 'undefined' ? require('u' + 'rl').pathToFileURL(__filename).href : (_documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === 'SCRIPT' && _documentCurrentScript.src || new URL('module.cjs', document.baseURI).href))));
    nuxt.options.build.transpile.push(runtimeDir);
    nuxt.options.runtimeConfig = defu.defu(nuxt.options.runtimeConfig, {
      turnstile: {
        secretKey: options.secretKey
      },
      public: {
        turnstile: {
          siteKey
        }
      }
    });
    await kit.installModule("@nuxt/scripts");
    const turnstileReg = {
      import: {
        from: pathe.join(runtimeDir, "composables/turnstile"),
        name: "useScriptCloudflareTurnstile"
      }
    };
    kit.addImports(turnstileReg.import);
    nuxt.hook("scripts:registry", (registry) => {
      const cloudflareScriptRegistry = registry.find((r) => r.label === "cloudflareTurnstile");
      if (cloudflareScriptRegistry) {
        Object.assign(cloudflareScriptRegistry, turnstileReg);
      }
    });
    kit.addComponent({
      name: "NuxtTurnstile",
      filePath: pathe.join(runtimeDir, "components", "NuxtTurnstile")
    });
    if (options.addValidateEndpoint) {
      kit.addServerHandler({
        route: "/_turnstile/validate",
        handler: pathe.join(runtimeDir, "server/validate.post")
      });
    }
    nuxt.hook("nitro:config", (config) => {
      config.externals = defu.defu(config.externals, {
        inline: [runtimeDir]
      });
      config.imports = defu.defu(config.imports, {
        presets: [
          {
            from: pathe.join(runtimeDir, "server/utils/verify"),
            imports: ["verifyTurnstileToken"]
          }
        ]
      });
    });
    nuxt.options.typescript.hoist.push("@types/cloudflare-turnstile");
    nuxt.hook("prepare:types", ({ references }) => {
      references.push({ types: "@types/cloudflare-turnstile" });
    });
  }
});

module.exports = module$1;
