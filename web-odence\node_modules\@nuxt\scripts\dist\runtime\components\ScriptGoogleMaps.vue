<script setup>
import { computed, onBeforeUnmount, onMounted, ref, watch, toRaw } from "vue";
import { withQuery } from "ufo";
import { defu } from "defu";
import { hash } from "ohash";
import { useHead } from "nuxt/app";
import { scriptRuntimeConfig } from "../utils";
import { useScriptTriggerElement } from "../composables/useScriptTriggerElement";
import { useScriptGoogleMaps } from "../registry/google-maps";
import ScriptAriaLoadingIndicator from "./ScriptAriaLoadingIndicator.vue";
const props = defineProps({
  trigger: { type: [String, Array, Boolean], required: false, default: ["mouseenter", "mouseover", "mousedown"] },
  aboveTheFold: { type: Boolean, required: false },
  apiKey: { type: String, required: false },
  center: { type: null, required: false },
  centerMarker: { type: Boolean, required: false, default: true },
  mapOptions: { type: null, required: false },
  region: { type: String, required: false },
  language: { type: String, required: false },
  version: { type: String, required: false },
  width: { type: [Number, String], required: false, default: 640 },
  height: { type: [Number, String], required: false, default: 400 },
  placeholderOptions: { type: Object, required: false },
  placeholderAttrs: { type: Object, required: false },
  rootAttrs: { type: Object, required: false },
  markers: { type: Array, required: false }
});
const emits = defineEmits(["ready", "error"]);
const apiKey = props.apiKey || scriptRuntimeConfig("googleMaps")?.apiKey;
const mapsApi = ref();
if (import.meta.dev && !apiKey)
  throw new Error("GoogleMaps requires an API key. Please provide `apiKey` on the <ScriptGoogleMaps> or globally via `runtimeConfig.public.scripts.googleMaps.apiKey`.");
const rootEl = ref();
const mapEl = ref();
const centerOverride = ref();
const trigger = useScriptTriggerElement({ trigger: props.trigger, el: rootEl });
const { load, status, onLoaded } = useScriptGoogleMaps({
  apiKey: props.apiKey,
  scriptOptions: {
    trigger
  },
  region: props.region,
  language: props.language,
  v: props.version
});
const options = computed(() => {
  return defu({ center: centerOverride.value }, props.mapOptions, {
    center: props.center,
    zoom: 15,
    mapId: props.mapOptions?.styles ? void 0 : "map"
  });
});
const ready = ref(false);
const map = ref();
const mapMarkers = ref(/* @__PURE__ */ new Map());
function isLocationQuery(s) {
  return typeof s === "string" && (s.split(",").length > 2 || s.includes("+"));
}
function resetMapMarkerMap(_marker) {
  return new Promise(async (resolve) => {
    const marker = _marker instanceof Promise ? await _marker : _marker;
    if (marker) {
      marker.setMap(null);
    }
    resolve();
  });
}
function normalizeAdvancedMapMarkerOptions(_options) {
  const opts = typeof _options === "string" ? {
    position: {
      lat: Number.parseFloat(_options.split(",")[0] || "0"),
      lng: Number.parseFloat(_options.split(",")[1] || "0")
    }
  } : _options;
  if (!opts.position) {
    opts.position = {
      lat: 0,
      lng: 0
    };
  }
  return opts;
}
async function createAdvancedMapMarker(_options) {
  if (!_options)
    return;
  const normalizedOptions = normalizeAdvancedMapMarkerOptions(_options);
  const key = hash({ position: normalizedOptions.position });
  if (mapMarkers.value.has(key))
    return mapMarkers.value.get(key);
  const p = new Promise(async (resolve) => {
    const lib = await importLibrary("marker");
    const mapMarkerOptions = defu(toRaw(normalizedOptions), {
      map: toRaw(map.value),
      // @ts-expect-error unified API for maps and markers
      position: normalizedOptions.location
    });
    resolve(new lib.AdvancedMarkerElement(mapMarkerOptions));
  });
  mapMarkers.value.set(key, p);
  return p;
}
const queryToLatLngCache = /* @__PURE__ */ new Map();
async function resolveQueryToLatLang(query) {
  if (query && typeof query === "object")
    return Promise.resolve(query);
  if (queryToLatLngCache.has(query)) {
    return Promise.resolve(queryToLatLngCache.get(query));
  }
  return new Promise(async (resolve, reject) => {
    if (!mapsApi.value) {
      await load();
      await new Promise((resolve2) => {
        const _ = watch(mapsApi, () => {
          _();
          resolve2();
        });
      });
    }
    const placesService = new mapsApi.value.places.PlacesService(map.value);
    placesService.findPlaceFromQuery({
      query,
      fields: ["name", "geometry"]
    }, (results, status2) => {
      if (status2 === "OK" && results?.[0]?.geometry?.location)
        return resolve(results[0].geometry.location);
      return reject(new Error(`No location found for ${query}`));
    });
  }).then((res) => {
    queryToLatLngCache.set(query, res);
    return res;
  });
}
const libraries = /* @__PURE__ */ new Map();
function importLibrary(key) {
  if (libraries.has(key))
    return libraries.get(key);
  const p = mapsApi.value?.importLibrary(key) || new Promise((resolve) => {
    const stop = watch(mapsApi, (api) => {
      if (api) {
        const p2 = api.importLibrary(key);
        resolve(p2);
        stop();
      }
    }, { immediate: true });
  });
  libraries.set(key, p);
  return p;
}
const googleMaps = {
  googleMaps: mapsApi,
  map,
  createAdvancedMapMarker,
  resolveQueryToLatLang,
  importLibrary
};
defineExpose(googleMaps);
onMounted(() => {
  watch(ready, (v) => {
    if (v) {
      emits("ready", googleMaps);
    }
  });
  watch(status, (v) => {
    if (v === "error") {
      emits("error");
    }
  });
  watch(options, () => {
    map.value?.setOptions(options.value);
  });
  watch([() => props.markers, map], async () => {
    if (!map.value) {
      return;
    }
    const nextMap = new Map((props.markers || []).map((m) => [hash({ position: normalizeAdvancedMapMarkerOptions(m).position }), m]));
    const toRemove = new Set([
      ...mapMarkers.value.keys()
    ].filter((k) => !nextMap.has(k)));
    const toAdd = new Set([...nextMap.keys()].filter((k) => !mapMarkers.value.has(k)));
    const centerHash = hash({ position: options.value.center });
    for (const key of toRemove) {
      if (props.centerMarker && key === centerHash) {
        continue;
      }
      const marker = await mapMarkers.value.get(key);
      if (marker) {
        resetMapMarkerMap(marker).then(() => {
          mapMarkers.value.delete(key);
        });
      }
    }
    for (const k of toAdd) {
      createAdvancedMapMarker(nextMap.get(k));
    }
  }, {
    immediate: true,
    deep: true
  });
  watch([() => options.value.center, ready, map], async (next, prev) => {
    if (!map.value) {
      return;
    }
    let center = toRaw(next[0]);
    if (center) {
      if (isLocationQuery(center) && ready.value) {
        center = await resolveQueryToLatLang(center);
      }
      map.value.setCenter(center);
      if (props.centerMarker) {
        if (options.value.mapId) {
          return;
        }
        if (prev[0]) {
          const prevCenterHash = hash({ position: prev[0] });
          if (mapMarkers.value.has(prevCenterHash)) {
            resetMapMarkerMap(mapMarkers.value.get(prevCenterHash)).then(() => {
              mapMarkers.value.delete(prevCenterHash);
            });
          }
        }
        createAdvancedMapMarker({ position: center });
      }
    }
  }, {
    immediate: true
  });
  onLoaded(async (instance) => {
    mapsApi.value = await instance.maps;
    const center = options.value.center;
    const _options = {
      ...options.value,
      // @ts-expect-error broken
      center: !center || isLocationQuery(center) ? void 0 : center
    };
    map.value = new mapsApi.value.Map(mapEl.value, _options);
    if (center && isLocationQuery(center)) {
      centerOverride.value = await resolveQueryToLatLang(center);
      map.value?.setCenter(centerOverride.value);
    }
    ready.value = true;
  });
});
if (import.meta.server) {
  useHead({
    link: [
      {
        rel: props.aboveTheFold ? "preconnect" : "dns-prefetch",
        href: "https://maps.googleapis.com"
      }
    ]
  });
}
function transformMapStyles(styles) {
  return styles.map((style) => {
    const feature = style.featureType ? `feature:${style.featureType}` : "";
    const element = style.elementType ? `element:${style.elementType}` : "";
    const rules = (style.stylers || []).map((styler) => {
      return Object.entries(styler).map(([key, value]) => {
        if (key === "color" && typeof value === "string") {
          value = value.replace("#", "0x");
        }
        return `${key}:${value}`;
      }).join("|");
    }).filter(Boolean).join("|");
    return [feature, element, rules].filter(Boolean).join("|");
  }).filter(Boolean);
}
const placeholder = computed(() => {
  let center = options.value.center;
  if (center && typeof center === "object") {
    center = `${center.lat},${center.lng}`;
  }
  const placeholderOptions = defu(props.placeholderOptions, {
    // only map option values
    zoom: options.value.zoom,
    center
  }, {
    size: `${props.width}x${props.height}`,
    key: apiKey,
    scale: 2,
    // we assume a high DPI to avoid hydration issues
    style: props.mapOptions?.styles ? transformMapStyles(props.mapOptions.styles) : void 0,
    markers: [
      ...props.markers || [],
      props.centerMarker && center
    ].filter(Boolean).map((m) => {
      if (typeof m === "object" && m.location) {
        m = m.location;
      }
      if (typeof m === "object" && m.lat) {
        return `${m.lat},${m.lng}`;
      }
      return m;
    }).join("|")
  });
  return withQuery("https://maps.googleapis.com/maps/api/staticmap", placeholderOptions);
});
const placeholderAttrs = computed(() => {
  return defu(props.placeholderAttrs, {
    src: placeholder.value,
    alt: "Google Maps Static Map",
    loading: props.aboveTheFold ? "eager" : "lazy",
    style: {
      cursor: "pointer",
      width: "100%",
      objectFit: "cover",
      height: "100%"
    }
  });
});
const rootAttrs = computed(() => {
  return defu(props.rootAttrs, {
    "aria-busy": status.value === "loading",
    "aria-label": status.value === "awaitingLoad" ? "Google Maps Static Map" : status.value === "loading" ? "Google Maps Map Embed Loading" : "Google Maps Embed",
    "aria-live": "polite",
    "role": "application",
    "style": {
      cursor: "pointer",
      position: "relative",
      maxWidth: "100%",
      width: `${props.width}px`,
      height: `'auto'`,
      aspectRatio: `${props.width}/${props.height}`
    },
    ...trigger instanceof Promise ? trigger.ssrAttrs || {} : {}
  });
});
onBeforeUnmount(async () => {
  await Promise.all([...mapMarkers.value.entries()].map(([, marker]) => resetMapMarkerMap(marker)));
  mapMarkers.value.clear();
  map.value?.unbindAll();
  map.value = void 0;
  mapEl.value?.firstChild?.remove();
});
</script>

<template>
  <div ref="rootEl" v-bind="rootAttrs">
    <div v-show="ready" ref="mapEl" :style="{ width: '100%', height: '100%', maxWidth: '100%' }" />
    <slot v-if="!ready" :placeholder="placeholder" name="placeholder">
      <img v-bind="placeholderAttrs">
    </slot>
    <slot v-if="status !== 'awaitingLoad' && !ready" name="loading">
      <ScriptAriaLoadingIndicator />
    </slot>
    <slot v-if="status === 'awaitingLoad'" name="awaitingLoad" />
    <slot v-else-if="status === 'error'" name="error" />
    <slot />
  </div>
</template>
