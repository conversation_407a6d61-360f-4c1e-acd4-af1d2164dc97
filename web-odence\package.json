{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@nuxt/eslint": "^1.9.0", "@nuxt/image": "^1.11.0", "@nuxtjs/i18n": "^10.1.0", "@nuxtjs/turnstile": "^1.1.0", "@vee-validate/nuxt": "^4.15.1", "@vee-validate/yup": "^4.15.1", "daisyui": "^5.1.24", "eslint": "^9.35.0", "nuxt": "^4.1.1", "sharp": "^0.34.3", "typescript": "^5.9.2", "vue": "^3.5.21", "vue-router": "^4.5.1", "yup": "^1.7.1"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide", "unrs-resolver"], "devDependencies": {"@types/node": "^24.7.0"}}