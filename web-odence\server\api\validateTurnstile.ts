export default defineEventHandler(async (event) => {
  const body = await readBody(event)

  // Verify Turnstile token
  const verifyRes = await $fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
    method: 'POST',
    body: new URLSearchParams({
      secret: process.env.NUXT_TURNSTILE_SECRET_KEY!,
      response: body.token,
    }),
  })

  if (!verifyRes.success) {
    throw createError({ statusCode: 400, statusMessage: 'Turnstile verification failed' })
  }

  // If passed ✅
  return { success: true, message: 'Form submitted successfully' }
})
