<script setup>
const props = defineProps({
    animationType: {
        type: String,
        default: 'fade-up',
        validator: (value) => [
            'fade-up', 
            'fade-left', 
            'fade-right',
            'fade-right-rotate',
            'fade-left-rotate',
            'zoom-in',
            'zoom-out',
            'zoom-out-rotate',
            'rotate',
            'rotate-sm',
            'flip',
            'bounce'
        ].includes(value)
    },
    threshold: {
        type: Number,
        default: 0.5
    },
    tag: {
        type: String,
        default: 'div'
    }
});

const isInView = ref(false);
const observerTarget = ref(null);

const { stop } = useIntersectionObserver(
    observerTarget,
    ([{ isIntersecting }]) => {
        if (isIntersecting) {
            isInView.value = true;
            stop();
        }
    },
    { root: null, rootMargin: '0px', threshold: props.threshold }
);

onBeforeUnmount(() => {
    stop();
});
</script>

<template>
    <component :is="tag" ref="observerTarget" :class="['animated-content', { 'visible': isInView }, animationType]">
        <slot></slot>
    </component>
</template>

<style scoped>
    .animation-container {
        overflow: hidden; 
    }
    
    .animated-content {
        will-change: transform, opacity; 
    }
    
    
    .fade-up:not(.visible) {
        opacity: 0;
        transform: translateY(2.5rem);
    }
    
    .fade-left:not(.visible) {
        opacity: 0;
        transform: translateX(-2.5rem);
    }
    
    .fade-right:not(.visible) {
        opacity: 0;
        transform: translateX(2.5rem);
    }

    .fade-right-rotate:not(.visible) {
        opacity: 0;
        transform: translateX(5rem) rotate(15deg);
    }

    .fade-left-rotate:not(.visible) {
        opacity: 0;
        transform: translateX(-5rem) rotate(-15deg);
    }
    
    .zoom-in:not(.visible) {
        opacity: 0;
        transform: scale(0.5);
    }
    
    .zoom-out:not(.visible) {
        opacity: 0;
        transform: scale(1.5);
    }
    
    .rotate:not(.visible) {
        opacity: 0;
        transform: rotate(-90deg) scale(0.7);
    }

    .rotate-sm:not(.visible) {
        opacity: 0;
        transform: rotate(-45deg) scale(0.7);
    }
    
    .flip:not(.visible) {
        opacity: 0;
        transform: rotateY(90deg);
    }
    
    .bounce:not(.visible) {
        opacity: 0;
        transform: scale(0);
    }

    .zoom-out-rotate:not(.visible) {
        opacity: 0;
        transform: rotate(45deg) scale(1.2);
    }
    

    .animated-content.visible {
        transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        opacity: 1;
        transform: translateY(0) translateX(0) rotate(0deg) scale(1) rotateY(0deg);
    }
    

    .bounce.visible {
        animation: bounce-in 0.8s;
    }
    
    @keyframes bounce-in {
        0% {
            transform: scale(0);
            opacity: 0;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.5;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
</style>
