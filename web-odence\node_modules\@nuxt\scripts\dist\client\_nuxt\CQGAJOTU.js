const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./1mHV-1pA.js","./7VVldQq3.js","./error-404.CDmSTChJ.css","./CYHtqd0e.js","./error-500.DpcyFY5F.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(r){if(r.ep)return;r.ep=!0;const o=s(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Zn(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const re={},Ht=[],Ye=()=>{},Oo=()=>!1,ds=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Yn=e=>e.startsWith("onUpdate:"),xe=Object.assign,Xn=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Al=Object.prototype.hasOwnProperty,se=(e,t)=>Al.call(e,t),W=Array.isArray,Pt=e=>hs(e)==="[object Map]",Lo=e=>hs(e)==="[object Set]",Or=e=>hs(e)==="[object Date]",V=e=>typeof e=="function",ce=e=>typeof e=="string",qe=e=>typeof e=="symbol",oe=e=>e!==null&&typeof e=="object",Qn=e=>(oe(e)||V(e))&&V(e.then)&&V(e.catch),Ho=Object.prototype.toString,hs=e=>Ho.call(e),kl=e=>hs(e).slice(8,-1),Po=e=>hs(e)==="[object Object]",$n=e=>ce(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ft=Zn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Gs=e=>{const t=Object.create(null);return(s=>t[s]||(t[s]=e(s)))},Ol=/-(\w)/g,De=Gs(e=>e.replace(Ol,(t,s)=>s?s.toUpperCase():"")),Ll=/\B([A-Z])/g,Tt=Gs(e=>e.replace(Ll,"-$1").toLowerCase()),Ks=Gs(e=>e.charAt(0).toUpperCase()+e.slice(1)),an=Gs(e=>e?`on${Ks(e)}`:""),dt=(e,t)=>!Object.is(e,t),Rs=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Fo=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Hl=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Pl=e=>{const t=ce(e)?Number(e):NaN;return isNaN(t)?e:t};let Lr;const Js=()=>Lr||(Lr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Zs(e){if(W(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],r=ce(n)?Il(n):Zs(n);if(r)for(const o in r)t[o]=r[o]}return t}else if(ce(e)||oe(e))return e}const Fl=/;(?![^(]*\))/g,jl=/:([^]+)/,Nl=/\/\*[^]*?\*\//g;function Il(e){const t={};return e.replace(Nl,"").split(Fl).forEach(s=>{if(s){const n=s.split(jl);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Ae(e){let t="";if(ce(e))t=e;else if(W(e))for(let s=0;s<e.length;s++){const n=Ae(e[s]);n&&(t+=n+" ")}else if(oe(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}function Bl(e){if(!e)return null;let{class:t,style:s}=e;return t&&!ce(t)&&(e.class=Ae(t)),s&&(e.style=Zs(s)),e}const Dl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ul=Zn(Dl);function jo(e){return!!e||e===""}function Vl(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=Os(e[n],t[n]);return s}function Os(e,t){if(e===t)return!0;let s=Or(e),n=Or(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=qe(e),n=qe(t),s||n)return e===t;if(s=W(e),n=W(t),s||n)return s&&n?Vl(e,t):!1;if(s=oe(e),n=oe(t),s||n){if(!s||!n)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!Os(e[i],t[i]))return!1}}return String(e)===String(t)}const No=e=>!!(e&&e.__v_isRef===!0),ge=e=>ce(e)?e:e==null?"":W(e)||oe(e)&&(e.toString===Ho||!V(e.toString))?No(e)?ge(e.value):JSON.stringify(e,Io,2):String(e),Io=(e,t)=>No(t)?Io(e,t.value):Pt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,r],o)=>(s[fn(n,o)+" =>"]=r,s),{})}:Lo(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>fn(s))}:qe(t)?fn(t):oe(t)&&!W(t)&&!Po(t)?String(t):t,fn=(e,t="")=>{var s;return qe(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Re;class Bo{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Re,!t&&Re&&(this.index=(Re.scopes||(Re.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=Re;try{return Re=this,t()}finally{Re=s}}}on(){++this._on===1&&(this.prevScope=Re,Re=this)}off(){this._on>0&&--this._on===0&&(Re=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Wl(e){return new Bo(e)}function Do(){return Re}let le;const un=new WeakSet;class Uo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Re&&Re.active&&Re.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,un.has(this)&&(un.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Wo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Hr(this),zo(this);const t=le,s=ze;le=this,ze=!0;try{return this.fn()}finally{qo(this),le=t,ze=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)sr(t);this.deps=this.depsTail=void 0,Hr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?un.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Tn(this)&&this.run()}get dirty(){return Tn(this)}}let Vo=0,es,ts;function Wo(e,t=!1){if(e.flags|=8,t){e.next=ts,ts=e;return}e.next=es,es=e}function er(){Vo++}function tr(){if(--Vo>0)return;if(ts){let t=ts;for(ts=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;es;){let t=es;for(es=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function zo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function qo(e){let t,s=e.depsTail,n=s;for(;n;){const r=n.prevDep;n.version===-1?(n===s&&(s=r),sr(n),zl(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=r}e.deps=t,e.depsTail=s}function Tn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Go(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Go(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ls)||(e.globalVersion=ls,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Tn(e))))return;e.flags|=2;const t=e.dep,s=le,n=ze;le=e,ze=!0;try{zo(e);const r=e.fn(e._value);(t.version===0||dt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{le=s,ze=n,qo(e),e.flags&=-3}}function sr(e,t=!1){const{dep:s,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)sr(o,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function zl(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let ze=!0;const Ko=[];function nt(){Ko.push(ze),ze=!1}function rt(){const e=Ko.pop();ze=e===void 0?!0:e}function Hr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=le;le=void 0;try{t()}finally{le=s}}}let ls=0;class ql{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class nr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!le||!ze||le===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==le)s=this.activeLink=new ql(le,this),le.deps?(s.prevDep=le.depsTail,le.depsTail.nextDep=s,le.depsTail=s):le.deps=le.depsTail=s,Jo(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=le.depsTail,s.nextDep=void 0,le.depsTail.nextDep=s,le.depsTail=s,le.deps===s&&(le.deps=n)}return s}trigger(t){this.version++,ls++,this.notify(t)}notify(t){er();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{tr()}}}function Jo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Jo(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Ls=new WeakMap,wt=Symbol(""),Rn=Symbol(""),cs=Symbol("");function _e(e,t,s){if(ze&&le){let n=Ls.get(e);n||Ls.set(e,n=new Map);let r=n.get(s);r||(n.set(s,r=new nr),r.map=n,r.key=s),r.track()}}function tt(e,t,s,n,r,o){const i=Ls.get(e);if(!i){ls++;return}const l=c=>{c&&c.trigger()};if(er(),t==="clear")i.forEach(l);else{const c=W(e),d=c&&$n(s);if(c&&s==="length"){const a=Number(n);i.forEach((f,h)=>{(h==="length"||h===cs||!qe(h)&&h>=a)&&l(f)})}else switch((s!==void 0||i.has(void 0))&&l(i.get(s)),d&&l(i.get(cs)),t){case"add":c?d&&l(i.get("length")):(l(i.get(wt)),Pt(e)&&l(i.get(Rn)));break;case"delete":c||(l(i.get(wt)),Pt(e)&&l(i.get(Rn)));break;case"set":Pt(e)&&l(i.get(wt));break}}tr()}function Gl(e,t){const s=Ls.get(e);return s&&s.get(t)}function Mt(e){const t=ee(e);return t===e?t:(_e(t,"iterate",cs),Be(e)?t:t.map(me))}function Ys(e){return _e(e=ee(e),"iterate",cs),e}const Kl={__proto__:null,[Symbol.iterator](){return dn(this,Symbol.iterator,me)},concat(...e){return Mt(this).concat(...e.map(t=>W(t)?Mt(t):t))},entries(){return dn(this,"entries",e=>(e[1]=me(e[1]),e))},every(e,t){return $e(this,"every",e,t,void 0,arguments)},filter(e,t){return $e(this,"filter",e,t,s=>s.map(me),arguments)},find(e,t){return $e(this,"find",e,t,me,arguments)},findIndex(e,t){return $e(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return $e(this,"findLast",e,t,me,arguments)},findLastIndex(e,t){return $e(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return $e(this,"forEach",e,t,void 0,arguments)},includes(...e){return hn(this,"includes",e)},indexOf(...e){return hn(this,"indexOf",e)},join(e){return Mt(this).join(e)},lastIndexOf(...e){return hn(this,"lastIndexOf",e)},map(e,t){return $e(this,"map",e,t,void 0,arguments)},pop(){return Yt(this,"pop")},push(...e){return Yt(this,"push",e)},reduce(e,...t){return Pr(this,"reduce",e,t)},reduceRight(e,...t){return Pr(this,"reduceRight",e,t)},shift(){return Yt(this,"shift")},some(e,t){return $e(this,"some",e,t,void 0,arguments)},splice(...e){return Yt(this,"splice",e)},toReversed(){return Mt(this).toReversed()},toSorted(e){return Mt(this).toSorted(e)},toSpliced(...e){return Mt(this).toSpliced(...e)},unshift(...e){return Yt(this,"unshift",e)},values(){return dn(this,"values",me)}};function dn(e,t,s){const n=Ys(e),r=n[t]();return n!==e&&!Be(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=s(o.value)),o}),r}const Jl=Array.prototype;function $e(e,t,s,n,r,o){const i=Ys(e),l=i!==e&&!Be(e),c=i[t];if(c!==Jl[t]){const f=c.apply(e,o);return l?me(f):f}let d=s;i!==e&&(l?d=function(f,h){return s.call(this,me(f),h,e)}:s.length>2&&(d=function(f,h){return s.call(this,f,h,e)}));const a=c.call(i,d,n);return l&&r?r(a):a}function Pr(e,t,s,n){const r=Ys(e);let o=s;return r!==e&&(Be(e)?s.length>3&&(o=function(i,l,c){return s.call(this,i,l,c,e)}):o=function(i,l,c){return s.call(this,i,me(l),c,e)}),r[t](o,...n)}function hn(e,t,s){const n=ee(e);_e(n,"iterate",cs);const r=n[t](...s);return(r===-1||r===!1)&&ir(s[0])?(s[0]=ee(s[0]),n[t](...s)):r}function Yt(e,t,s=[]){nt(),er();const n=ee(e)[t].apply(e,s);return tr(),rt(),n}const Zl=Zn("__proto__,__v_isRef,__isVue"),Zo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(qe));function Yl(e){qe(e)||(e=String(e));const t=ee(this);return _e(t,"has",e),t.hasOwnProperty(e)}class Yo{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return o;if(s==="__v_raw")return n===(r?o?ic:ei:o?$o:Qo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const i=W(t);if(!r){let c;if(i&&(c=Kl[s]))return c;if(s==="hasOwnProperty")return Yl}const l=Reflect.get(t,s,ue(t)?t:n);return(qe(s)?Zo.has(s):Zl(s))||(r||_e(t,"get",s),o)?l:ue(l)?i&&$n(s)?l:l.value:oe(l)?r?ti(l):ot(l):l}}class Xo extends Yo{constructor(t=!1){super(!1,t)}set(t,s,n,r){let o=t[s];if(!this._isShallow){const c=it(o);if(!Be(n)&&!it(n)&&(o=ee(o),n=ee(n)),!W(t)&&ue(o)&&!ue(n))return c||(o.value=n),!0}const i=W(t)&&$n(s)?Number(s)<t.length:se(t,s),l=Reflect.set(t,s,n,ue(t)?t:r);return t===ee(r)&&(i?dt(n,o)&&tt(t,"set",s,n):tt(t,"add",s,n)),l}deleteProperty(t,s){const n=se(t,s);t[s];const r=Reflect.deleteProperty(t,s);return r&&n&&tt(t,"delete",s,void 0),r}has(t,s){const n=Reflect.has(t,s);return(!qe(s)||!Zo.has(s))&&_e(t,"has",s),n}ownKeys(t){return _e(t,"iterate",W(t)?"length":wt),Reflect.ownKeys(t)}}class Xl extends Yo{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Ql=new Xo,$l=new Xl,ec=new Xo(!0);const Mn=e=>e,ys=e=>Reflect.getPrototypeOf(e);function tc(e,t,s){return function(...n){const r=this.__v_raw,o=ee(r),i=Pt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,d=r[e](...n),a=s?Mn:t?Hs:me;return!t&&_e(o,"iterate",c?Rn:wt),{next(){const{value:f,done:h}=d.next();return h?{value:f,done:h}:{value:l?[a(f[0]),a(f[1])]:a(f),done:h}},[Symbol.iterator](){return this}}}}function ws(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function sc(e,t){const s={get(r){const o=this.__v_raw,i=ee(o),l=ee(r);e||(dt(r,l)&&_e(i,"get",r),_e(i,"get",l));const{has:c}=ys(i),d=t?Mn:e?Hs:me;if(c.call(i,r))return d(o.get(r));if(c.call(i,l))return d(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&_e(ee(r),"iterate",wt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ee(o),l=ee(r);return e||(dt(r,l)&&_e(i,"has",r),_e(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=ee(l),d=t?Mn:e?Hs:me;return!e&&_e(c,"iterate",wt),l.forEach((a,f)=>r.call(o,d(a),d(f),i))}};return xe(s,e?{add:ws("add"),set:ws("set"),delete:ws("delete"),clear:ws("clear")}:{add(r){!t&&!Be(r)&&!it(r)&&(r=ee(r));const o=ee(this);return ys(o).has.call(o,r)||(o.add(r),tt(o,"add",r,r)),this},set(r,o){!t&&!Be(o)&&!it(o)&&(o=ee(o));const i=ee(this),{has:l,get:c}=ys(i);let d=l.call(i,r);d||(r=ee(r),d=l.call(i,r));const a=c.call(i,r);return i.set(r,o),d?dt(o,a)&&tt(i,"set",r,o):tt(i,"add",r,o),this},delete(r){const o=ee(this),{has:i,get:l}=ys(o);let c=i.call(o,r);c||(r=ee(r),c=i.call(o,r)),l&&l.call(o,r);const d=o.delete(r);return c&&tt(o,"delete",r,void 0),d},clear(){const r=ee(this),o=r.size!==0,i=r.clear();return o&&tt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=tc(r,e,t)}),s}function rr(e,t){const s=sc(e,t);return(n,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(se(s,r)&&r in n?s:n,r,o)}const nc={get:rr(!1,!1)},rc={get:rr(!1,!0)},oc={get:rr(!0,!1)};const Qo=new WeakMap,$o=new WeakMap,ei=new WeakMap,ic=new WeakMap;function lc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function cc(e){return e.__v_skip||!Object.isExtensible(e)?0:lc(kl(e))}function ot(e){return it(e)?e:or(e,!1,Ql,nc,Qo)}function kt(e){return or(e,!1,ec,rc,$o)}function ti(e){return or(e,!0,$l,oc,ei)}function or(e,t,s,n,r){if(!oe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=cc(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?n:s);return r.set(e,l),l}function bt(e){return it(e)?bt(e.__v_raw):!!(e&&e.__v_isReactive)}function it(e){return!!(e&&e.__v_isReadonly)}function Be(e){return!!(e&&e.__v_isShallow)}function ir(e){return e?!!e.__v_raw:!1}function ee(e){const t=e&&e.__v_raw;return t?ee(t):e}function ac(e){return!se(e,"__v_skip")&&Object.isExtensible(e)&&Fo(e,"__v_skip",!0),e}const me=e=>oe(e)?ot(e):e,Hs=e=>oe(e)?ti(e):e;function ue(e){return e?e.__v_isRef===!0:!1}function je(e){return si(e,!1)}function En(e){return si(e,!0)}function si(e,t){return ue(e)?e:new fc(e,t)}class fc{constructor(t,s){this.dep=new nr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:ee(t),this._value=s?t:me(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||Be(t)||it(t);t=n?t:ee(t),dt(t,s)&&(this._rawValue=t,this._value=n?t:me(t),this.dep.trigger())}}function Z(e){return ue(e)?e.value:e}function uc(e){return V(e)?e():Z(e)}const dc={get:(e,t,s)=>t==="__v_raw"?e:Z(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const r=e[t];return ue(r)&&!ue(s)?(r.value=s,!0):Reflect.set(e,t,s,n)}};function ni(e){return bt(e)?e:new Proxy(e,dc)}class hc{constructor(t,s,n){this._object=t,this._key=s,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Gl(ee(this._object),this._key)}}class pc{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function gc(e,t,s){return ue(e)?e:V(e)?new pc(e):oe(e)&&arguments.length>1?mc(e,t,s):je(e)}function mc(e,t,s){const n=e[t];return ue(n)?n:new hc(e,t,s)}class yc{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new nr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ls-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&le!==this)return Wo(this,!0),!0}get value(){const t=this.dep.track();return Go(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function wc(e,t,s=!1){let n,r;return V(e)?n=e:(n=e.get,r=e.set),new yc(n,r,s)}const bs={},Ps=new WeakMap;let yt;function bc(e,t=!1,s=yt){if(s){let n=Ps.get(s);n||Ps.set(s,n=[]),n.push(e)}}function vc(e,t,s=re){const{immediate:n,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=s,d=v=>r?v:Be(v)||r===!1||r===0?st(v,1):st(v);let a,f,h,u,w=!1,y=!1;if(ue(e)?(f=()=>e.value,w=Be(e)):bt(e)?(f=()=>d(e),w=!0):W(e)?(y=!0,w=e.some(v=>bt(v)||Be(v)),f=()=>e.map(v=>{if(ue(v))return v.value;if(bt(v))return d(v);if(V(v))return c?c(v,2):v()})):V(e)?t?f=c?()=>c(e,2):e:f=()=>{if(h){nt();try{h()}finally{rt()}}const v=yt;yt=a;try{return c?c(e,3,[u]):e(u)}finally{yt=v}}:f=Ye,t&&r){const v=f,b=r===!0?1/0:r;f=()=>st(v(),b)}const M=Do(),x=()=>{a.stop(),M&&M.active&&Xn(M.effects,a)};if(o&&t){const v=t;t=(...b)=>{v(...b),x()}}let k=y?new Array(e.length).fill(bs):bs;const g=v=>{if(!(!(a.flags&1)||!a.dirty&&!v))if(t){const b=a.run();if(r||w||(y?b.some((L,D)=>dt(L,k[D])):dt(b,k))){h&&h();const L=yt;yt=a;try{const D=[b,k===bs?void 0:y&&k[0]===bs?[]:k,u];k=b,c?c(t,3,D):t(...D)}finally{yt=L}}}else a.run()};return l&&l(g),a=new Uo(f),a.scheduler=i?()=>i(g,!1):g,u=v=>bc(v,!1,a),h=a.onStop=()=>{const v=Ps.get(a);if(v){if(c)c(v,4);else for(const b of v)b();Ps.delete(a)}},t?n?g(!0):k=a.run():i?i(g.bind(null,!0),!0):a.run(),x.pause=a.pause.bind(a),x.resume=a.resume.bind(a),x.stop=x,x}function st(e,t=1/0,s){if(t<=0||!oe(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ue(e))st(e.value,t,s);else if(W(e))for(let n=0;n<e.length;n++)st(e[n],t,s);else if(Lo(e)||Pt(e))e.forEach(n=>{st(n,t,s)});else if(Po(e)){for(const n in e)st(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&st(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ps(e,t,s,n){try{return n?e(...n):e()}catch(r){qt(r,t,s)}}function Xe(e,t,s,n){if(V(e)){const r=ps(e,t,s,n);return r&&Qn(r)&&r.catch(o=>{qt(o,t,s)}),r}if(W(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Xe(e[o],t,s,n));return r}}function qt(e,t,s,n=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||re;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const a=l.ec;if(a){for(let f=0;f<a.length;f++)if(a[f](e,c,d)===!1)return}l=l.parent}if(o){nt(),ps(o,null,10,[e,c,d]),rt();return}}_c(e,s,r,n,i)}function _c(e,t,s,n=!0,r=!1){if(r)throw e;console.error(e)}const Se=[];let Je=-1;const jt=[];let at=null,At=0;const ri=Promise.resolve();let Fs=null;function lr(e){const t=Fs||ri;return e?t.then(this?e.bind(this):e):t}function xc(e){let t=Je+1,s=Se.length;for(;t<s;){const n=t+s>>>1,r=Se[n],o=as(r);o<e||o===e&&r.flags&2?t=n+1:s=n}return t}function cr(e){if(!(e.flags&1)){const t=as(e),s=Se[Se.length-1];!s||!(e.flags&2)&&t>=as(s)?Se.push(e):Se.splice(xc(t),0,e),e.flags|=1,oi()}}function oi(){Fs||(Fs=ri.then(ii))}function An(e){W(e)?jt.push(...e):at&&e.id===-1?at.splice(At+1,0,e):e.flags&1||(jt.push(e),e.flags|=1),oi()}function Fr(e,t,s=Je+1){for(;s<Se.length;s++){const n=Se[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;Se.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function js(e){if(jt.length){const t=[...new Set(jt)].sort((s,n)=>as(s)-as(n));if(jt.length=0,at){at.push(...t);return}for(at=t,At=0;At<at.length;At++){const s=at[At];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}at=null,At=0}}const as=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ii(e){try{for(Je=0;Je<Se.length;Je++){const t=Se[Je];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),ps(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Je<Se.length;Je++){const t=Se[Je];t&&(t.flags&=-2)}Je=-1,Se.length=0,js(),Fs=null,(Se.length||jt.length)&&ii()}}let we=null,li=null;function Ns(e){const t=we;return we=e,li=e&&e.type.__scopeId||null,t}function ve(e,t=we,s){if(!t||e._n)return e;const n=(...r)=>{n._d&&Jr(-1);const o=Ns(t);let i;try{i=e(...r)}finally{Ns(o),n._d&&Jr(1)}return i};return n._n=!0,n._c=!0,n._d=!0,n}function Cc(e,t){if(we===null)return e;const s=en(we),n=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=re]=t[r];o&&(V(o)&&(o={mounted:o,updated:o}),o.deep&&st(i),n.push({dir:o,instance:s,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function Ze(e,t,s,n){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[n];c&&(nt(),Xe(c,s,8,[e.el,l,e,t]),rt())}}const Sc=Symbol("_vte"),Tc=e=>e.__isTeleport,Rc=Symbol("_leaveCb");function ar(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ar(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Gt(e,t){return V(e)?xe({name:e.name},t,{setup:e}):e}function fr(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Nt(e,t,s,n,r=!1){if(W(e)){e.forEach((w,y)=>Nt(w,t&&(W(t)?t[y]:t),s,n,r));return}if(vt(n)&&!r){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Nt(e,t,s,n.component.subTree);return}const o=n.shapeFlag&4?en(n.component):n.el,i=r?null:o,{i:l,r:c}=e,d=t&&t.r,a=l.refs===re?l.refs={}:l.refs,f=l.setupState,h=ee(f),u=f===re?Oo:w=>se(h,w);if(d!=null&&d!==c){if(ce(d))a[d]=null,u(d)&&(f[d]=null);else if(ue(d)){d.value=null;const w=t;w.k&&(a[w.k]=null)}}if(V(c))ps(c,l,12,[i,a]);else{const w=ce(c),y=ue(c);if(w||y){const M=()=>{if(e.f){const x=w?u(c)?f[c]:a[c]:c.value;if(r)W(x)&&Xn(x,o);else if(W(x))x.includes(o)||x.push(o);else if(w)a[c]=[o],u(c)&&(f[c]=a[c]);else{const k=[o];c.value=k,e.k&&(a[e.k]=k)}}else w?(a[c]=i,u(c)&&(f[c]=i)):y&&(c.value=i,e.k&&(a[e.k]=i))};i?(M.id=-1,He(M,s)):M()}}}let jr=!1;const Et=()=>{jr||(console.error("Hydration completed but contains mismatches."),jr=!0)},Mc=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Ec=e=>e.namespaceURI.includes("MathML"),vs=e=>{if(e.nodeType===1){if(Mc(e))return"svg";if(Ec(e))return"mathml"}},Ot=e=>e.nodeType===8;function Ac(e){const{mt:t,p:s,o:{patchProp:n,createText:r,nextSibling:o,parentNode:i,remove:l,insert:c,createComment:d}}=e,a=(g,v)=>{if(!v.hasChildNodes()){s(null,g,v),js(),v._vnode=g;return}f(v.firstChild,g,null,null,null),js(),v._vnode=g},f=(g,v,b,L,D,G=!1)=>{G=G||!!v.dynamicChildren;const q=Ot(g)&&g.data==="[",P=()=>y(g,v,b,L,D,q),{type:$,ref:Q,shapeFlag:te,patchFlag:de}=v;let he=g.nodeType;v.el=g,de===-2&&(G=!1,v.dynamicChildren=null);let j=null;switch($){case xt:he!==3?v.children===""?(c(v.el=r(""),i(g),g),j=g):j=P():(g.data!==v.children&&(Et(),g.data=v.children),j=o(g));break;case Ue:k(g)?(j=o(g),x(v.el=g.content.firstChild,g,b)):he!==8||q?j=P():j=o(g);break;case rs:if(q&&(g=o(g),he=g.nodeType),he===1||he===3){j=g;const K=!v.children.length;for(let F=0;F<v.staticCount;F++)K&&(v.children+=j.nodeType===1?j.outerHTML:j.data),F===v.staticCount-1&&(v.anchor=j),j=o(j);return q?o(j):j}else P();break;case fe:q?j=w(g,v,b,L,D,G):j=P();break;default:if(te&1)(he!==1||v.type.toLowerCase()!==g.tagName.toLowerCase())&&!k(g)?j=P():j=h(g,v,b,L,D,G);else if(te&6){v.slotScopeIds=D;const K=i(g);if(q?j=M(g):Ot(g)&&g.data==="teleport start"?j=M(g,g.data,"teleport end"):j=o(g),t(v,K,null,b,L,vs(K),G),vt(v)&&!v.type.__asyncResolved){let F;q?(F=X(fe),F.anchor=j?j.previousSibling:K.lastChild):F=g.nodeType===3?Pe(""):X("div"),F.el=g,v.component.subTree=F}}else te&64?he!==8?j=P():j=v.type.hydrate(g,v,b,L,D,G,e,u):te&128&&(j=v.type.hydrate(g,v,b,L,vs(i(g)),D,G,e,f))}return Q!=null&&Nt(Q,null,L,v),j},h=(g,v,b,L,D,G)=>{G=G||!!v.dynamicChildren;const{type:q,props:P,patchFlag:$,shapeFlag:Q,dirs:te,transition:de}=v,he=q==="input"||q==="option";if(he||$!==-1){te&&Ze(v,null,b,"created");let j=!1;if(k(g)){j=Oi(null,de)&&b&&b.vnode.props&&b.vnode.props.appear;const F=g.content.firstChild;if(j){const ae=F.getAttribute("class");ae&&(F.$cls=ae),de.beforeEnter(F)}x(F,g,b),v.el=g=F}if(Q&16&&!(P&&(P.innerHTML||P.textContent))){let F=u(g.firstChild,v,g,b,L,D,G);for(;F;){_s(g,1)||Et();const ae=F;F=F.nextSibling,l(ae)}}else if(Q&8){let F=v.children;F[0]===`
`&&(g.tagName==="PRE"||g.tagName==="TEXTAREA")&&(F=F.slice(1)),g.textContent!==F&&(_s(g,0)||Et(),g.textContent=v.children)}if(P){if(he||!G||$&48){const F=g.tagName.includes("-");for(const ae in P)(he&&(ae.endsWith("value")||ae==="indeterminate")||ds(ae)&&!Ft(ae)||ae[0]==="."||F)&&n(g,ae,null,P[ae],void 0,b)}else if(P.onClick)n(g,"onClick",null,P.onClick,void 0,b);else if($&4&&bt(P.style))for(const F in P.style)P.style[F]}let K;(K=P&&P.onVnodeBeforeMount)&&Ne(K,b,v),te&&Ze(v,null,b,"beforeMount"),((K=P&&P.onVnodeMounted)||te||j)&&Ii(()=>{K&&Ne(K,b,v),j&&de.enter(g),te&&Ze(v,null,b,"mounted")},L)}return g.nextSibling},u=(g,v,b,L,D,G,q)=>{q=q||!!v.dynamicChildren;const P=v.children,$=P.length;for(let Q=0;Q<$;Q++){const te=q?P[Q]:P[Q]=Fe(P[Q]),de=te.type===xt;g?(de&&!q&&Q+1<$&&Fe(P[Q+1]).type===xt&&(c(r(g.data.slice(te.children.length)),b,o(g)),g.data=te.children),g=f(g,te,L,D,G,q)):de&&!te.children?c(te.el=r(""),b):(_s(b,1)||Et(),s(null,te,b,null,L,D,vs(b),G))}return g},w=(g,v,b,L,D,G)=>{const{slotScopeIds:q}=v;q&&(D=D?D.concat(q):q);const P=i(g),$=u(o(g),v,P,b,L,D,G);return $&&Ot($)&&$.data==="]"?o(v.anchor=$):(Et(),c(v.anchor=d("]"),P,$),$)},y=(g,v,b,L,D,G)=>{if(_s(g.parentElement,1)||Et(),v.el=null,G){const $=M(g);for(;;){const Q=o(g);if(Q&&Q!==$)l(Q);else break}}const q=o(g),P=i(g);return l(g),s(null,v,P,q,b,L,vs(P),D),b&&(b.vnode.el=v.el,$s(b,v.el)),q},M=(g,v="[",b="]")=>{let L=0;for(;g;)if(g=o(g),g&&Ot(g)&&(g.data===v&&L++,g.data===b)){if(L===0)return o(g);L--}return g},x=(g,v,b)=>{const L=v.parentNode;L&&L.replaceChild(g,v);let D=b;for(;D;)D.vnode.el===v&&(D.vnode.el=D.subTree.el=g),D=D.parent},k=g=>g.nodeType===1&&g.tagName==="TEMPLATE";return[a,f]}const Nr="data-allow-mismatch",kc={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function _s(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(Nr);)e=e.parentElement;const s=e&&e.getAttribute(Nr);if(s==null)return!1;if(s==="")return!0;{const n=s.split(",");return t===0&&n.includes("children")?!0:n.includes(kc[t])}}Js().requestIdleCallback;Js().cancelIdleCallback;function Oc(e,t){if(Ot(e)&&e.data==="["){let s=1,n=e.nextSibling;for(;n;){if(n.nodeType===1){if(t(n)===!1)break}else if(Ot(n))if(n.data==="]"){if(--s===0)break}else n.data==="["&&s++;n=n.nextSibling}}else t(e)}const vt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function Ir(e){V(e)&&(e={loader:e});const{loader:t,loadingComponent:s,errorComponent:n,delay:r=200,hydrate:o,timeout:i,suspensible:l=!0,onError:c}=e;let d=null,a,f=0;const h=()=>(f++,d=null,u()),u=()=>{let w;return d||(w=d=t().catch(y=>{if(y=y instanceof Error?y:new Error(String(y)),c)return new Promise((M,x)=>{c(y,()=>M(h()),()=>x(y),f+1)});throw y}).then(y=>w!==d&&d?d:(y&&(y.__esModule||y[Symbol.toStringTag]==="Module")&&(y=y.default),a=y,y)))};return Gt({name:"AsyncComponentWrapper",__asyncLoader:u,__asyncHydrate(w,y,M){let x=!1;(y.bu||(y.bu=[])).push(()=>x=!0);const k=()=>{x||M()},g=o?()=>{const v=o(k,b=>Oc(w,b));v&&(y.bum||(y.bum=[])).push(v)}:k;a?g():u().then(()=>!y.isUnmounted&&g())},get __asyncResolved(){return a},setup(){const w=ye;if(fr(w),a)return()=>pn(a,w);const y=g=>{d=null,qt(g,w,13,!n)};if(l&&w.suspense||Wt)return u().then(g=>()=>pn(g,w)).catch(g=>(y(g),()=>n?X(n,{error:g}):null));const M=je(!1),x=je(),k=je(!!r);return r&&setTimeout(()=>{k.value=!1},r),i!=null&&setTimeout(()=>{if(!M.value&&!x.value){const g=new Error(`Async component timed out after ${i}ms.`);y(g),x.value=g}},i),u().then(()=>{M.value=!0,w.parent&&ur(w.parent.vnode)&&w.parent.update()}).catch(g=>{y(g),x.value=g}),()=>{if(M.value&&a)return pn(a,w);if(x.value&&n)return X(n,{error:x.value});if(s&&!k.value)return X(s)}}})}function pn(e,t){const{ref:s,props:n,children:r,ce:o}=t.vnode,i=X(e,n,r);return i.ref=s,i.ce=o,delete t.vnode.ce,i}const ur=e=>e.type.__isKeepAlive;function ci(e,t){fi(e,"a",t)}function ai(e,t){fi(e,"da",t)}function fi(e,t,s=ye){const n=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Xs(t,n,s),s){let r=s.parent;for(;r&&r.parent;)ur(r.parent.vnode)&&Lc(n,t,s,r),r=r.parent}}function Lc(e,t,s,n){const r=Xs(t,e,n,!0);di(()=>{Xn(n[t],r)},s)}function Xs(e,t,s=ye,n=!1){if(s){const r=s[e]||(s[e]=[]),o=t.__weh||(t.__weh=(...i)=>{nt();const l=Ct(s),c=Xe(t,s,e,i);return l(),rt(),c});return n?r.unshift(o):r.push(o),o}}const lt=e=>(t,s=ye)=>{(!Wt||e==="sp")&&Xs(e,(...n)=>t(...n),s)},Hc=lt("bm"),ui=lt("m"),Pc=lt("bu"),Fc=lt("u"),dr=lt("bum"),di=lt("um"),jc=lt("sp"),Nc=lt("rtg"),Ic=lt("rtc");function hi(e,t=ye){Xs("ec",e,t)}const pi="components";function kn(e,t){return mi(pi,e,!0,t)||e}const gi=Symbol.for("v-ndc");function Bc(e){return ce(e)?mi(pi,e,!1)||e:e||gi}function mi(e,t,s=!0,n=!1){const r=we||ye;if(r){const o=r.type;{const l=ka(o,!1);if(l&&(l===t||l===De(t)||l===Ks(De(t))))return o}const i=Br(r[e]||o[e],t)||Br(r.appContext[e],t);return!i&&n?o:i}}function Br(e,t){return e&&(e[t]||e[De(t)]||e[Ks(De(t))])}function xs(e,t,s,n){let r;const o=s,i=W(e);if(i||ce(e)){const l=i&&bt(e);let c=!1,d=!1;l&&(c=!Be(e),d=it(e),e=Ys(e)),r=new Array(e.length);for(let a=0,f=e.length;a<f;a++)r[a]=t(c?d?Hs(me(e[a])):me(e[a]):e[a],a,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(oe(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const a=l[c];r[c]=t(e[a],a,c,o)}}else r=[];return r}function We(e,t,s={},n,r){if(we.ce||we.parent&&vt(we.parent)&&we.parent.ce)return t!=="default"&&(s.name=t),U(),Ee(fe,null,[X("slot",s,n&&n())],64);let o=e[t];o&&o._c&&(o._d=!1),U();const i=o&&yi(o(s)),l=s.key||i&&i.key,c=Ee(fe,{key:(l&&!qe(l)?l:`_${t}`)+(!i&&n?"_fb":"")},i||(n?n():[]),i&&e._===1?64:-2);return!r&&c.scopeId&&(c.slotScopeIds=[c.scopeId+"-s"]),o&&o._c&&(o._d=!0),c}function yi(e){return e.some(t=>Ut(t)?!(t.type===Ue||t.type===fe&&!yi(t.children)):!0)?e:null}const On=e=>e?zi(e)?en(e):On(e.parent):null,ss=xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>On(e.parent),$root:e=>On(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>bi(e),$forceUpdate:e=>e.f||(e.f=()=>{cr(e.update)}),$nextTick:e=>e.n||(e.n=lr.bind(e.proxy)),$watch:e=>la.bind(e)}),gn=(e,t)=>e!==re&&!e.__isScriptSetup&&se(e,t),Dc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const u=i[t];if(u!==void 0)switch(u){case 1:return n[t];case 2:return r[t];case 4:return s[t];case 3:return o[t]}else{if(gn(n,t))return i[t]=1,n[t];if(r!==re&&se(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&se(d,t))return i[t]=3,o[t];if(s!==re&&se(s,t))return i[t]=4,s[t];Ln&&(i[t]=0)}}const a=ss[t];let f,h;if(a)return t==="$attrs"&&_e(e.attrs,"get",""),a(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(s!==re&&se(s,t))return i[t]=4,s[t];if(h=c.config.globalProperties,se(h,t))return h[t]},set({_:e},t,s){const{data:n,setupState:r,ctx:o}=e;return gn(r,t)?(r[t]=s,!0):n!==re&&se(n,t)?(n[t]=s,!0):se(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:r,propsOptions:o,type:i}},l){let c,d;return!!(s[l]||e!==re&&l[0]!=="$"&&se(e,l)||gn(t,l)||(c=o[0])&&se(c,l)||se(n,l)||se(ss,l)||se(r.config.globalProperties,l)||(d=i.__cssModules)&&d[l])},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:se(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Dr(e){return W(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function Uc(e){const t=Kt();let s=e();return Nn(),Qn(s)&&(s=s.catch(n=>{throw Ct(t),n})),[s,()=>Ct(t)]}let Ln=!0;function Vc(e){const t=bi(e),s=e.proxy,n=e.ctx;Ln=!1,t.beforeCreate&&Ur(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:d,created:a,beforeMount:f,mounted:h,beforeUpdate:u,updated:w,activated:y,deactivated:M,beforeDestroy:x,beforeUnmount:k,destroyed:g,unmounted:v,render:b,renderTracked:L,renderTriggered:D,errorCaptured:G,serverPrefetch:q,expose:P,inheritAttrs:$,components:Q,directives:te,filters:de}=t;if(d&&Wc(d,n,null),i)for(const K in i){const F=i[K];V(F)&&(n[K]=F.bind(s))}if(r){const K=r.call(s,s);oe(K)&&(e.data=ot(K))}if(Ln=!0,o)for(const K in o){const F=o[K],ae=V(F)?F.bind(s,s):V(F.get)?F.get.bind(s,s):Ye,gs=!V(F)&&V(F.set)?F.set.bind(s):Ye,pt=Ie({get:ae,set:gs});Object.defineProperty(n,K,{enumerable:!0,configurable:!0,get:()=>pt.value,set:Ge=>pt.value=Ge})}if(l)for(const K in l)wi(l[K],n,s,K);if(c){const K=V(c)?c.call(s):c;Reflect.ownKeys(K).forEach(F=>{_i(F,K[F])})}a&&Ur(a,e,"c");function j(K,F){W(F)?F.forEach(ae=>K(ae.bind(s))):F&&K(F.bind(s))}if(j(Hc,f),j(ui,h),j(Pc,u),j(Fc,w),j(ci,y),j(ai,M),j(hi,G),j(Ic,L),j(Nc,D),j(dr,k),j(di,v),j(jc,q),W(P))if(P.length){const K=e.exposed||(e.exposed={});P.forEach(F=>{Object.defineProperty(K,F,{get:()=>s[F],set:ae=>s[F]=ae,enumerable:!0})})}else e.exposed||(e.exposed={});b&&e.render===Ye&&(e.render=b),$!=null&&(e.inheritAttrs=$),Q&&(e.components=Q),te&&(e.directives=te),q&&fr(e)}function Wc(e,t,s=Ye){W(e)&&(e=Hn(e));for(const n in e){const r=e[n];let o;oe(r)?"default"in r?o=It(r.from||n,r.default,!0):o=It(r.from||n):o=It(r),ue(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[n]=o}}function Ur(e,t,s){Xe(W(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function wi(e,t,s,n){let r=n.includes(".")?Pi(s,n):()=>s[n];if(ce(e)){const o=t[e];V(o)&&ns(r,o)}else if(V(e))ns(r,e.bind(s));else if(oe(e))if(W(e))e.forEach(o=>wi(o,t,s,n));else{const o=V(e.handler)?e.handler.bind(s):t[e.handler];V(o)&&ns(r,o,e)}}function bi(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!s&&!n?c=t:(c={},r.length&&r.forEach(d=>Is(c,d,i,!0)),Is(c,t,i)),oe(t)&&o.set(t,c),c}function Is(e,t,s,n=!1){const{mixins:r,extends:o}=t;o&&Is(e,o,s,!0),r&&r.forEach(i=>Is(e,i,s,!0));for(const i in t)if(!(n&&i==="expose")){const l=zc[i]||s&&s[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const zc={data:Vr,props:Wr,emits:Wr,methods:$t,computed:$t,beforeCreate:Ce,created:Ce,beforeMount:Ce,mounted:Ce,beforeUpdate:Ce,updated:Ce,beforeDestroy:Ce,beforeUnmount:Ce,destroyed:Ce,unmounted:Ce,activated:Ce,deactivated:Ce,errorCaptured:Ce,serverPrefetch:Ce,components:$t,directives:$t,watch:Gc,provide:Vr,inject:qc};function Vr(e,t){return t?e?function(){return xe(V(e)?e.call(this,this):e,V(t)?t.call(this,this):t)}:t:e}function qc(e,t){return $t(Hn(e),Hn(t))}function Hn(e){if(W(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Ce(e,t){return e?[...new Set([].concat(e,t))]:t}function $t(e,t){return e?xe(Object.create(null),e,t):t}function Wr(e,t){return e?W(e)&&W(t)?[...new Set([...e,...t])]:xe(Object.create(null),Dr(e),Dr(t??{})):t}function Gc(e,t){if(!e)return t;if(!t)return e;const s=xe(Object.create(null),e);for(const n in t)s[n]=Ce(e[n],t[n]);return s}function vi(){return{app:null,config:{isNativeTag:Oo,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Kc=0;function Jc(e,t){return function(n,r=null){V(n)||(n=xe({},n)),r!=null&&!oe(r)&&(r=null);const o=vi(),i=new WeakSet,l=[];let c=!1;const d=o.app={_uid:Kc++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:La,get config(){return o.config},set config(a){},use(a,...f){return i.has(a)||(a&&V(a.install)?(i.add(a),a.install(d,...f)):V(a)&&(i.add(a),a(d,...f))),d},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),d},component(a,f){return f?(o.components[a]=f,d):o.components[a]},directive(a,f){return f?(o.directives[a]=f,d):o.directives[a]},mount(a,f,h){if(!c){const u=d._ceVNode||X(n,r);return u.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),f&&t?t(u,a):e(u,a,h),c=!0,d._container=a,a.__vue_app__=d,en(u.component)}},onUnmount(a){l.push(a)},unmount(){c&&(Xe(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,f){return o.provides[a]=f,d},runWithContext(a){const f=_t;_t=d;try{return a()}finally{_t=f}}};return d}}let _t=null;function _i(e,t){if(ye){let s=ye.provides;const n=ye.parent&&ye.parent.provides;n===s&&(s=ye.provides=Object.create(n)),s[e]=t}}function It(e,t,s=!1){const n=Kt();if(n||_t){let r=_t?_t._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&V(t)?t.call(n&&n.proxy):t}}function hr(){return!!(Kt()||_t)}const xi={},Ci=()=>Object.create(xi),Si=e=>Object.getPrototypeOf(e)===xi;function Zc(e,t,s,n=!1){const r={},o=Ci();e.propsDefaults=Object.create(null),Ti(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);s?e.props=n?r:kt(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Yc(e,t,s,n){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ee(r),[c]=e.propsOptions;let d=!1;if((n||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let f=0;f<a.length;f++){let h=a[f];if(Qs(e.emitsOptions,h))continue;const u=t[h];if(c)if(se(o,h))u!==o[h]&&(o[h]=u,d=!0);else{const w=De(h);r[w]=Pn(c,l,w,u,e,!1)}else u!==o[h]&&(o[h]=u,d=!0)}}}else{Ti(e,t,r,o)&&(d=!0);let a;for(const f in l)(!t||!se(t,f)&&((a=Tt(f))===f||!se(t,a)))&&(c?s&&(s[f]!==void 0||s[a]!==void 0)&&(r[f]=Pn(c,l,f,void 0,e,!0)):delete r[f]);if(o!==l)for(const f in o)(!t||!se(t,f))&&(delete o[f],d=!0)}d&&tt(e.attrs,"set","")}function Ti(e,t,s,n){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Ft(c))continue;const d=t[c];let a;r&&se(r,a=De(c))?!o||!o.includes(a)?s[a]=d:(l||(l={}))[a]=d:Qs(e.emitsOptions,c)||(!(c in n)||d!==n[c])&&(n[c]=d,i=!0)}if(o){const c=ee(s),d=l||re;for(let a=0;a<o.length;a++){const f=o[a];s[f]=Pn(r,c,f,d[f],e,!se(d,f))}}return i}function Pn(e,t,s,n,r,o){const i=e[s];if(i!=null){const l=se(i,"default");if(l&&n===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&V(c)){const{propsDefaults:d}=r;if(s in d)n=d[s];else{const a=Ct(r);n=d[s]=c.call(null,t),a()}}else n=c;r.ce&&r.ce._setProp(s,n)}i[0]&&(o&&!l?n=!1:i[1]&&(n===""||n===Tt(s))&&(n=!0))}return n}const Xc=new WeakMap;function Ri(e,t,s=!1){const n=s?Xc:t.propsCache,r=n.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!V(e)){const a=f=>{c=!0;const[h,u]=Ri(f,t,!0);xe(i,h),u&&l.push(...u)};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!c)return oe(e)&&n.set(e,Ht),Ht;if(W(o))for(let a=0;a<o.length;a++){const f=De(o[a]);zr(f)&&(i[f]=re)}else if(o)for(const a in o){const f=De(a);if(zr(f)){const h=o[a],u=i[f]=W(h)||V(h)?{type:h}:xe({},h),w=u.type;let y=!1,M=!0;if(W(w))for(let x=0;x<w.length;++x){const k=w[x],g=V(k)&&k.name;if(g==="Boolean"){y=!0;break}else g==="String"&&(M=!1)}else y=V(w)&&w.name==="Boolean";u[0]=y,u[1]=M,(y||se(u,"default"))&&l.push(f)}}const d=[i,l];return oe(e)&&n.set(e,d),d}function zr(e){return e[0]!=="$"&&!Ft(e)}const pr=e=>e==="_"||e==="_ctx"||e==="$stable",gr=e=>W(e)?e.map(Fe):[Fe(e)],Qc=(e,t,s)=>{if(t._n)return t;const n=ve((...r)=>gr(t(...r)),s);return n._c=!1,n},Mi=(e,t,s)=>{const n=e._ctx;for(const r in e){if(pr(r))continue;const o=e[r];if(V(o))t[r]=Qc(r,o,n);else if(o!=null){const i=gr(o);t[r]=()=>i}}},Ei=(e,t)=>{const s=gr(t);e.slots.default=()=>s},Ai=(e,t,s)=>{for(const n in t)(s||!pr(n))&&(e[n]=t[n])},$c=(e,t,s)=>{const n=e.slots=Ci();if(e.vnode.shapeFlag&32){const r=t._;r?(Ai(n,t,s),s&&Fo(n,"_",r,!0)):Mi(t,n)}else t&&Ei(e,t)},ea=(e,t,s)=>{const{vnode:n,slots:r}=e;let o=!0,i=re;if(n.shapeFlag&32){const l=t._;l?s&&l===1?o=!1:Ai(r,t,s):(o=!t.$stable,Mi(t,r)),i=t}else t&&(Ei(e,t),i={default:1});if(o)for(const l in r)!pr(l)&&i[l]==null&&delete r[l]},He=Ii;function ta(e){return ki(e)}function sa(e){return ki(e,Ac)}function ki(e,t){const s=Js();s.__VUE__=!0;const{insert:n,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:d,setElementText:a,parentNode:f,nextSibling:h,setScopeId:u=Ye,insertStaticContent:w}=e,y=(p,m,_,T=null,C=null,S=null,O=void 0,A=null,E=!!m.dynamicChildren)=>{if(p===m)return;p&&!ut(p,m)&&(T=ms(p),Ge(p,C,S,!0),p=null),m.patchFlag===-2&&(E=!1,m.dynamicChildren=null);const{type:R,ref:I,shapeFlag:H}=m;switch(R){case xt:M(p,m,_,T);break;case Ue:x(p,m,_,T);break;case rs:p==null&&k(m,_,T,O);break;case fe:Q(p,m,_,T,C,S,O,A,E);break;default:H&1?b(p,m,_,T,C,S,O,A,E):H&6?te(p,m,_,T,C,S,O,A,E):(H&64||H&128)&&R.process(p,m,_,T,C,S,O,A,E,Rt)}I!=null&&C?Nt(I,p&&p.ref,S,m||p,!m):I==null&&p&&p.ref!=null&&Nt(p.ref,null,S,p,!0)},M=(p,m,_,T)=>{if(p==null)n(m.el=l(m.children),_,T);else{const C=m.el=p.el;m.children!==p.children&&d(C,m.children)}},x=(p,m,_,T)=>{p==null?n(m.el=c(m.children||""),_,T):m.el=p.el},k=(p,m,_,T)=>{[p.el,p.anchor]=w(p.children,m,_,T,p.el,p.anchor)},g=({el:p,anchor:m},_,T)=>{let C;for(;p&&p!==m;)C=h(p),n(p,_,T),p=C;n(m,_,T)},v=({el:p,anchor:m})=>{let _;for(;p&&p!==m;)_=h(p),r(p),p=_;r(m)},b=(p,m,_,T,C,S,O,A,E)=>{m.type==="svg"?O="svg":m.type==="math"&&(O="mathml"),p==null?L(m,_,T,C,S,O,A,E):q(p,m,C,S,O,A,E)},L=(p,m,_,T,C,S,O,A)=>{let E,R;const{props:I,shapeFlag:H,transition:N,dirs:z}=p;if(E=p.el=i(p.type,S,I&&I.is,I),H&8?a(E,p.children):H&16&&G(p.children,E,null,T,C,mn(p,S),O,A),z&&Ze(p,null,T,"created"),D(E,p,p.scopeId,O,T),I){for(const ie in I)ie!=="value"&&!Ft(ie)&&o(E,ie,null,I[ie],S,T);"value"in I&&o(E,"value",null,I.value,S),(R=I.onVnodeBeforeMount)&&Ne(R,T,p)}z&&Ze(p,null,T,"beforeMount");const J=Oi(C,N);J&&N.beforeEnter(E),n(E,m,_),((R=I&&I.onVnodeMounted)||J||z)&&He(()=>{R&&Ne(R,T,p),J&&N.enter(E),z&&Ze(p,null,T,"mounted")},C)},D=(p,m,_,T,C)=>{if(_&&u(p,_),T)for(let S=0;S<T.length;S++)u(p,T[S]);if(C){let S=C.subTree;if(m===S||ji(S.type)&&(S.ssContent===m||S.ssFallback===m)){const O=C.vnode;D(p,O,O.scopeId,O.slotScopeIds,C.parent)}}},G=(p,m,_,T,C,S,O,A,E=0)=>{for(let R=E;R<p.length;R++){const I=p[R]=A?ft(p[R]):Fe(p[R]);y(null,I,m,_,T,C,S,O,A)}},q=(p,m,_,T,C,S,O)=>{const A=m.el=p.el;let{patchFlag:E,dynamicChildren:R,dirs:I}=m;E|=p.patchFlag&16;const H=p.props||re,N=m.props||re;let z;if(_&&gt(_,!1),(z=N.onVnodeBeforeUpdate)&&Ne(z,_,m,p),I&&Ze(m,p,_,"beforeUpdate"),_&&gt(_,!0),(H.innerHTML&&N.innerHTML==null||H.textContent&&N.textContent==null)&&a(A,""),R?P(p.dynamicChildren,R,A,_,T,mn(m,C),S):O||F(p,m,A,null,_,T,mn(m,C),S,!1),E>0){if(E&16)$(A,H,N,_,C);else if(E&2&&H.class!==N.class&&o(A,"class",null,N.class,C),E&4&&o(A,"style",H.style,N.style,C),E&8){const J=m.dynamicProps;for(let ie=0;ie<J.length;ie++){const ne=J[ie],Te=H[ne],be=N[ne];(be!==Te||ne==="value")&&o(A,ne,Te,be,C,_)}}E&1&&p.children!==m.children&&a(A,m.children)}else!O&&R==null&&$(A,H,N,_,C);((z=N.onVnodeUpdated)||I)&&He(()=>{z&&Ne(z,_,m,p),I&&Ze(m,p,_,"updated")},T)},P=(p,m,_,T,C,S,O)=>{for(let A=0;A<m.length;A++){const E=p[A],R=m[A],I=E.el&&(E.type===fe||!ut(E,R)||E.shapeFlag&198)?f(E.el):_;y(E,R,I,null,T,C,S,O,!0)}},$=(p,m,_,T,C)=>{if(m!==_){if(m!==re)for(const S in m)!Ft(S)&&!(S in _)&&o(p,S,m[S],null,C,T);for(const S in _){if(Ft(S))continue;const O=_[S],A=m[S];O!==A&&S!=="value"&&o(p,S,A,O,C,T)}"value"in _&&o(p,"value",m.value,_.value,C)}},Q=(p,m,_,T,C,S,O,A,E)=>{const R=m.el=p?p.el:l(""),I=m.anchor=p?p.anchor:l("");let{patchFlag:H,dynamicChildren:N,slotScopeIds:z}=m;z&&(A=A?A.concat(z):z),p==null?(n(R,_,T),n(I,_,T),G(m.children||[],_,I,C,S,O,A,E)):H>0&&H&64&&N&&p.dynamicChildren?(P(p.dynamicChildren,N,_,C,S,O,A),(m.key!=null||C&&m===C.subTree)&&Li(p,m,!0)):F(p,m,_,I,C,S,O,A,E)},te=(p,m,_,T,C,S,O,A,E)=>{m.slotScopeIds=A,p==null?m.shapeFlag&512?C.ctx.activate(m,_,T,O,E):de(m,_,T,C,S,O,E):he(p,m,E)},de=(p,m,_,T,C,S,O)=>{const A=p.component=Ta(p,T,C);if(ur(p)&&(A.ctx.renderer=Rt),Ra(A,!1,O),A.asyncDep){if(C&&C.registerDep(A,j,O),!p.el){const E=A.subTree=X(Ue);x(null,E,m,_),p.placeholder=E.el}}else j(A,p,m,_,C,S,O)},he=(p,m,_)=>{const T=m.component=p.component;if(ha(p,m,_))if(T.asyncDep&&!T.asyncResolved){K(T,m,_);return}else T.next=m,T.update();else m.el=p.el,T.vnode=m},j=(p,m,_,T,C,S,O)=>{const A=()=>{if(p.isMounted){let{next:H,bu:N,u:z,parent:J,vnode:ie}=p;{const Oe=Hi(p);if(Oe){H&&(H.el=ie.el,K(p,H,O)),Oe.asyncDep.then(()=>{p.isUnmounted||A()});return}}let ne=H,Te;gt(p,!1),H?(H.el=ie.el,K(p,H,O)):H=ie,N&&Rs(N),(Te=H.props&&H.props.onVnodeBeforeUpdate)&&Ne(Te,J,H,ie),gt(p,!0);const be=yn(p),Ve=p.subTree;p.subTree=be,y(Ve,be,f(Ve.el),ms(Ve),p,C,S),H.el=be.el,ne===null&&$s(p,be.el),z&&He(z,C),(Te=H.props&&H.props.onVnodeUpdated)&&He(()=>Ne(Te,J,H,ie),C)}else{let H;const{el:N,props:z}=m,{bm:J,m:ie,parent:ne,root:Te,type:be}=p,Ve=vt(m);if(gt(p,!1),J&&Rs(J),!Ve&&(H=z&&z.onVnodeBeforeMount)&&Ne(H,ne,m),gt(p,!0),N&&cn){const Oe=()=>{p.subTree=yn(p),cn(N,p.subTree,p,C,null)};Ve&&be.__asyncHydrate?be.__asyncHydrate(N,p,Oe):Oe()}else{Te.ce&&Te.ce._def.shadowRoot!==!1&&Te.ce._injectChildStyle(be);const Oe=p.subTree=yn(p);y(null,Oe,_,T,p,C,S),m.el=Oe.el}if(ie&&He(ie,C),!Ve&&(H=z&&z.onVnodeMounted)){const Oe=m;He(()=>Ne(H,ne,Oe),C)}(m.shapeFlag&256||ne&&vt(ne.vnode)&&ne.vnode.shapeFlag&256)&&p.a&&He(p.a,C),p.isMounted=!0,m=_=T=null}};p.scope.on();const E=p.effect=new Uo(A);p.scope.off();const R=p.update=E.run.bind(E),I=p.job=E.runIfDirty.bind(E);I.i=p,I.id=p.uid,E.scheduler=()=>cr(I),gt(p,!0),R()},K=(p,m,_)=>{m.component=p;const T=p.vnode.props;p.vnode=m,p.next=null,Yc(p,m.props,T,_),ea(p,m.children,_),nt(),Fr(p),rt()},F=(p,m,_,T,C,S,O,A,E=!1)=>{const R=p&&p.children,I=p?p.shapeFlag:0,H=m.children,{patchFlag:N,shapeFlag:z}=m;if(N>0){if(N&128){gs(R,H,_,T,C,S,O,A,E);return}else if(N&256){ae(R,H,_,T,C,S,O,A,E);return}}z&8?(I&16&&Jt(R,C,S),H!==R&&a(_,H)):I&16?z&16?gs(R,H,_,T,C,S,O,A,E):Jt(R,C,S,!0):(I&8&&a(_,""),z&16&&G(H,_,T,C,S,O,A,E))},ae=(p,m,_,T,C,S,O,A,E)=>{p=p||Ht,m=m||Ht;const R=p.length,I=m.length,H=Math.min(R,I);let N;for(N=0;N<H;N++){const z=m[N]=E?ft(m[N]):Fe(m[N]);y(p[N],z,_,null,C,S,O,A,E)}R>I?Jt(p,C,S,!0,!1,H):G(m,_,T,C,S,O,A,E,H)},gs=(p,m,_,T,C,S,O,A,E)=>{let R=0;const I=m.length;let H=p.length-1,N=I-1;for(;R<=H&&R<=N;){const z=p[R],J=m[R]=E?ft(m[R]):Fe(m[R]);if(ut(z,J))y(z,J,_,null,C,S,O,A,E);else break;R++}for(;R<=H&&R<=N;){const z=p[H],J=m[N]=E?ft(m[N]):Fe(m[N]);if(ut(z,J))y(z,J,_,null,C,S,O,A,E);else break;H--,N--}if(R>H){if(R<=N){const z=N+1,J=z<I?m[z].el:T;for(;R<=N;)y(null,m[R]=E?ft(m[R]):Fe(m[R]),_,J,C,S,O,A,E),R++}}else if(R>N)for(;R<=H;)Ge(p[R],C,S,!0),R++;else{const z=R,J=R,ie=new Map;for(R=J;R<=N;R++){const Le=m[R]=E?ft(m[R]):Fe(m[R]);Le.key!=null&&ie.set(Le.key,R)}let ne,Te=0;const be=N-J+1;let Ve=!1,Oe=0;const Zt=new Array(be);for(R=0;R<be;R++)Zt[R]=0;for(R=z;R<=H;R++){const Le=p[R];if(Te>=be){Ge(Le,C,S,!0);continue}let Ke;if(Le.key!=null)Ke=ie.get(Le.key);else for(ne=J;ne<=N;ne++)if(Zt[ne-J]===0&&ut(Le,m[ne])){Ke=ne;break}Ke===void 0?Ge(Le,C,S,!0):(Zt[Ke-J]=R+1,Ke>=Oe?Oe=Ke:Ve=!0,y(Le,m[Ke],_,null,C,S,O,A,E),Te++)}const Er=Ve?na(Zt):Ht;for(ne=Er.length-1,R=be-1;R>=0;R--){const Le=J+R,Ke=m[Le],Ar=m[Le+1],kr=Le+1<I?Ar.el||Ar.placeholder:T;Zt[R]===0?y(null,Ke,_,kr,C,S,O,A,E):Ve&&(ne<0||R!==Er[ne]?pt(Ke,_,kr,2):ne--)}}},pt=(p,m,_,T,C=null)=>{const{el:S,type:O,transition:A,children:E,shapeFlag:R}=p;if(R&6){pt(p.component.subTree,m,_,T);return}if(R&128){p.suspense.move(m,_,T);return}if(R&64){O.move(p,m,_,Rt);return}if(O===fe){n(S,m,_);for(let H=0;H<E.length;H++)pt(E[H],m,_,T);n(p.anchor,m,_);return}if(O===rs){g(p,m,_);return}if(T!==2&&R&1&&A)if(T===0)A.beforeEnter(S),n(S,m,_),He(()=>A.enter(S),C);else{const{leave:H,delayLeave:N,afterLeave:z}=A,J=()=>{p.ctx.isUnmounted?r(S):n(S,m,_)},ie=()=>{S._isLeaving&&S[Rc](!0),H(S,()=>{J(),z&&z()})};N?N(S,J,ie):ie()}else n(S,m,_)},Ge=(p,m,_,T=!1,C=!1)=>{const{type:S,props:O,ref:A,children:E,dynamicChildren:R,shapeFlag:I,patchFlag:H,dirs:N,cacheIndex:z}=p;if(H===-2&&(C=!1),A!=null&&(nt(),Nt(A,null,_,p,!0),rt()),z!=null&&(m.renderCache[z]=void 0),I&256){m.ctx.deactivate(p);return}const J=I&1&&N,ie=!vt(p);let ne;if(ie&&(ne=O&&O.onVnodeBeforeUnmount)&&Ne(ne,m,p),I&6)El(p.component,_,T);else{if(I&128){p.suspense.unmount(_,T);return}J&&Ze(p,null,m,"beforeUnmount"),I&64?p.type.remove(p,m,_,Rt,T):R&&!R.hasOnce&&(S!==fe||H>0&&H&64)?Jt(R,m,_,!1,!0):(S===fe&&H&384||!C&&I&16)&&Jt(E,m,_),T&&Rr(p)}(ie&&(ne=O&&O.onVnodeUnmounted)||J)&&He(()=>{ne&&Ne(ne,m,p),J&&Ze(p,null,m,"unmounted")},_)},Rr=p=>{const{type:m,el:_,anchor:T,transition:C}=p;if(m===fe){Ml(_,T);return}if(m===rs){v(p);return}const S=()=>{r(_),C&&!C.persisted&&C.afterLeave&&C.afterLeave()};if(p.shapeFlag&1&&C&&!C.persisted){const{leave:O,delayLeave:A}=C,E=()=>O(_,S);A?A(p.el,S,E):E()}else S()},Ml=(p,m)=>{let _;for(;p!==m;)_=h(p),r(p),p=_;r(m)},El=(p,m,_)=>{const{bum:T,scope:C,job:S,subTree:O,um:A,m:E,a:R}=p;qr(E),qr(R),T&&Rs(T),C.stop(),S&&(S.flags|=8,Ge(O,p,m,_)),A&&He(A,m),He(()=>{p.isUnmounted=!0},m)},Jt=(p,m,_,T=!1,C=!1,S=0)=>{for(let O=S;O<p.length;O++)Ge(p[O],m,_,T,C)},ms=p=>{if(p.shapeFlag&6)return ms(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),_=m&&m[Sc];return _?h(_):m};let on=!1;const Mr=(p,m,_)=>{p==null?m._vnode&&Ge(m._vnode,null,null,!0):y(m._vnode||null,p,m,null,null,null,_),m._vnode=p,on||(on=!0,Fr(),js(),on=!1)},Rt={p:y,um:Ge,m:pt,r:Rr,mt:de,mc:G,pc:F,pbc:P,n:ms,o:e};let ln,cn;return t&&([ln,cn]=t(Rt)),{render:Mr,hydrate:ln,createApp:Jc(Mr,ln)}}function mn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function gt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Oi(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Li(e,t,s=!1){const n=e.children,r=t.children;if(W(n)&&W(r))for(let o=0;o<n.length;o++){const i=n[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=ft(r[o]),l.el=i.el),!s&&l.patchFlag!==-2&&Li(i,l)),l.type===xt&&l.patchFlag!==-1&&(l.el=i.el),l.type===Ue&&!l.el&&(l.el=i.el)}}function na(e){const t=e.slice(),s=[0];let n,r,o,i,l;const c=e.length;for(n=0;n<c;n++){const d=e[n];if(d!==0){if(r=s[s.length-1],e[r]<d){t[n]=r,s.push(n);continue}for(o=0,i=s.length-1;o<i;)l=o+i>>1,e[s[l]]<d?o=l+1:i=l;d<e[s[o]]&&(o>0&&(t[n]=s[o-1]),s[o]=n)}}for(o=s.length,i=s[o-1];o-- >0;)s[o]=i,i=t[i];return s}function Hi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Hi(t)}function qr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ra=Symbol.for("v-scx"),oa=()=>It(ra);function ia(e,t){return mr(e,null,t)}function ns(e,t,s){return mr(e,t,s)}function mr(e,t,s=re){const{immediate:n,deep:r,flush:o,once:i}=s,l=xe({},s),c=t&&n||!t&&o!=="post";let d;if(Wt){if(o==="sync"){const u=oa();d=u.__watcherHandles||(u.__watcherHandles=[])}else if(!c){const u=()=>{};return u.stop=Ye,u.resume=Ye,u.pause=Ye,u}}const a=ye;l.call=(u,w,y)=>Xe(u,a,w,y);let f=!1;o==="post"?l.scheduler=u=>{He(u,a&&a.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(u,w)=>{w?u():cr(u)}),l.augmentJob=u=>{t&&(u.flags|=4),f&&(u.flags|=2,a&&(u.id=a.uid,u.i=a))};const h=vc(e,t,l);return Wt&&(d?d.push(h):c&&h()),h}function la(e,t,s){const n=this.proxy,r=ce(e)?e.includes(".")?Pi(n,e):()=>n[e]:e.bind(n,n);let o;V(t)?o=t:(o=t.handler,s=t);const i=Ct(this),l=mr(r,o.bind(n),s);return i(),l}function Pi(e,t){const s=t.split(".");return()=>{let n=e;for(let r=0;r<s.length&&n;r++)n=n[s[r]];return n}}const ca=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${De(t)}Modifiers`]||e[`${Tt(t)}Modifiers`];function aa(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||re;let r=s;const o=t.startsWith("update:"),i=o&&ca(n,t.slice(7));i&&(i.trim&&(r=s.map(a=>ce(a)?a.trim():a)),i.number&&(r=s.map(Hl)));let l,c=n[l=an(t)]||n[l=an(De(t))];!c&&o&&(c=n[l=an(Tt(t))]),c&&Xe(c,e,6,r);const d=n[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Xe(d,e,6,r)}}function Fi(e,t,s=!1){const n=t.emitsCache,r=n.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!V(e)){const c=d=>{const a=Fi(d,t,!0);a&&(l=!0,xe(i,a))};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(oe(e)&&n.set(e,null),null):(W(o)?o.forEach(c=>i[c]=null):xe(i,o),oe(e)&&n.set(e,i),i)}function Qs(e,t){return!e||!ds(t)?!1:(t=t.slice(2).replace(/Once$/,""),se(e,t[0].toLowerCase()+t.slice(1))||se(e,Tt(t))||se(e,t))}function yn(e){const{type:t,vnode:s,proxy:n,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:d,renderCache:a,props:f,data:h,setupState:u,ctx:w,inheritAttrs:y}=e,M=Ns(e);let x,k;try{if(s.shapeFlag&4){const v=r||n,b=v;x=Fe(d.call(b,v,a,f,u,h,w)),k=l}else{const v=t;x=Fe(v.length>1?v(f,{attrs:l,slots:i,emit:c}):v(f,null)),k=t.props?l:ua(l)}}catch(v){os.length=0,qt(v,e,1),x=X(Ue)}let g=x;if(k&&y!==!1){const v=Object.keys(k),{shapeFlag:b}=g;v.length&&b&7&&(o&&v.some(Yn)&&(k=da(k,o)),g=Vt(g,k,!1,!0))}return s.dirs&&(g=Vt(g,null,!1,!0),g.dirs=g.dirs?g.dirs.concat(s.dirs):s.dirs),s.transition&&ar(g,s.transition),x=g,Ns(M),x}function fa(e,t=!0){let s;for(let n=0;n<e.length;n++){const r=e[n];if(Ut(r)){if(r.type!==Ue||r.children==="v-if"){if(s)return;s=r}}else return}return s}const ua=e=>{let t;for(const s in e)(s==="class"||s==="style"||ds(s))&&((t||(t={}))[s]=e[s]);return t},da=(e,t)=>{const s={};for(const n in e)(!Yn(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function ha(e,t,s){const{props:n,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,d=o.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return n?Gr(n,i,d):!!i;if(c&8){const a=t.dynamicProps;for(let f=0;f<a.length;f++){const h=a[f];if(i[h]!==n[h]&&!Qs(d,h))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:n===i?!1:n?i?Gr(n,i,d):!0:!!i;return!1}function Gr(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let r=0;r<n.length;r++){const o=n[r];if(t[o]!==e[o]&&!Qs(s,o))return!0}return!1}function $s({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const ji=e=>e.__isSuspense;let Fn=0;const pa={name:"Suspense",__isSuspense:!0,process(e,t,s,n,r,o,i,l,c,d){if(e==null)ma(t,s,n,r,o,i,l,c,d);else{if(o&&o.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}ya(e,t,s,n,r,i,l,c,d)}},hydrate:wa,normalize:ba},ga=pa;function fs(e,t){const s=e.props&&e.props[t];V(s)&&s()}function ma(e,t,s,n,r,o,i,l,c){const{p:d,o:{createElement:a}}=c,f=a("div"),h=e.suspense=Ni(e,r,n,t,f,s,o,i,l,c);d(null,h.pendingBranch=e.ssContent,f,null,n,h,o,i),h.deps>0?(fs(e,"onPending"),fs(e,"onFallback"),d(null,e.ssFallback,t,s,n,null,o,i),Bt(h,e.ssFallback)):h.resolve(!1,!0)}function ya(e,t,s,n,r,o,i,l,{p:c,um:d,o:{createElement:a}}){const f=t.suspense=e.suspense;f.vnode=t,t.el=e.el;const h=t.ssContent,u=t.ssFallback,{activeBranch:w,pendingBranch:y,isInFallback:M,isHydrating:x}=f;if(y)f.pendingBranch=h,ut(h,y)?(c(y,h,f.hiddenContainer,null,r,f,o,i,l),f.deps<=0?f.resolve():M&&(x||(c(w,u,s,n,r,null,o,i,l),Bt(f,u)))):(f.pendingId=Fn++,x?(f.isHydrating=!1,f.activeBranch=y):d(y,r,f),f.deps=0,f.effects.length=0,f.hiddenContainer=a("div"),M?(c(null,h,f.hiddenContainer,null,r,f,o,i,l),f.deps<=0?f.resolve():(c(w,u,s,n,r,null,o,i,l),Bt(f,u))):w&&ut(h,w)?(c(w,h,s,n,r,f,o,i,l),f.resolve(!0)):(c(null,h,f.hiddenContainer,null,r,f,o,i,l),f.deps<=0&&f.resolve()));else if(w&&ut(h,w))c(w,h,s,n,r,f,o,i,l),Bt(f,h);else if(fs(t,"onPending"),f.pendingBranch=h,h.shapeFlag&512?f.pendingId=h.component.suspenseId:f.pendingId=Fn++,c(null,h,f.hiddenContainer,null,r,f,o,i,l),f.deps<=0)f.resolve();else{const{timeout:k,pendingId:g}=f;k>0?setTimeout(()=>{f.pendingId===g&&f.fallback(u)},k):k===0&&f.fallback(u)}}function Ni(e,t,s,n,r,o,i,l,c,d,a=!1){const{p:f,m:h,um:u,n:w,o:{parentNode:y,remove:M}}=d;let x;const k=va(e);k&&t&&t.pendingBranch&&(x=t.pendingId,t.deps++);const g=e.props?Pl(e.props.timeout):void 0,v=o,b={vnode:e,parent:t,parentComponent:s,namespace:i,container:n,hiddenContainer:r,deps:0,pendingId:Fn++,timeout:typeof g=="number"?g:-1,activeBranch:null,pendingBranch:null,isInFallback:!a,isHydrating:a,isUnmounted:!1,effects:[],resolve(L=!1,D=!1){const{vnode:G,activeBranch:q,pendingBranch:P,pendingId:$,effects:Q,parentComponent:te,container:de}=b;let he=!1;b.isHydrating?b.isHydrating=!1:L||(he=q&&P.transition&&P.transition.mode==="out-in",he&&(q.transition.afterLeave=()=>{$===b.pendingId&&(h(P,de,o===v?w(q):o,0),An(Q))}),q&&(y(q.el)===de&&(o=w(q)),u(q,te,b,!0)),he||h(P,de,o,0)),Bt(b,P),b.pendingBranch=null,b.isInFallback=!1;let j=b.parent,K=!1;for(;j;){if(j.pendingBranch){j.effects.push(...Q),K=!0;break}j=j.parent}!K&&!he&&An(Q),b.effects=[],k&&t&&t.pendingBranch&&x===t.pendingId&&(t.deps--,t.deps===0&&!D&&t.resolve()),fs(G,"onResolve")},fallback(L){if(!b.pendingBranch)return;const{vnode:D,activeBranch:G,parentComponent:q,container:P,namespace:$}=b;fs(D,"onFallback");const Q=w(G),te=()=>{b.isInFallback&&(f(null,L,P,Q,q,null,$,l,c),Bt(b,L))},de=L.transition&&L.transition.mode==="out-in";de&&(G.transition.afterLeave=te),b.isInFallback=!0,u(G,q,null,!0),de||te()},move(L,D,G){b.activeBranch&&h(b.activeBranch,L,D,G),b.container=L},next(){return b.activeBranch&&w(b.activeBranch)},registerDep(L,D,G){const q=!!b.pendingBranch;q&&b.deps++;const P=L.vnode.el;L.asyncDep.catch($=>{qt($,L,0)}).then($=>{if(L.isUnmounted||b.isUnmounted||b.pendingId!==L.suspenseId)return;L.asyncResolved=!0;const{vnode:Q}=L;In(L,$),P&&(Q.el=P);const te=!P&&L.subTree.el;D(L,Q,y(P||L.subTree.el),P?null:w(L.subTree),b,i,G),te&&M(te),$s(L,Q.el),q&&--b.deps===0&&b.resolve()})},unmount(L,D){b.isUnmounted=!0,b.activeBranch&&u(b.activeBranch,s,L,D),b.pendingBranch&&u(b.pendingBranch,s,L,D)}};return b}function wa(e,t,s,n,r,o,i,l,c){const d=t.suspense=Ni(t,n,s,e.parentNode,document.createElement("div"),null,r,o,i,l,!0),a=c(e,d.pendingBranch=t.ssContent,s,d,o,i);return d.deps===0&&d.resolve(!1,!0),a}function ba(e){const{shapeFlag:t,children:s}=e,n=t&32;e.ssContent=Kr(n?s.default:s),e.ssFallback=n?Kr(s.fallback):X(Ue)}function Kr(e){let t;if(V(e)){const s=Dt&&e._c;s&&(e._d=!1,U()),e=e(),s&&(e._d=!0,t=ke,Bi())}return W(e)&&(e=fa(e)),e=Fe(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(s=>s!==e)),e}function Ii(e,t){t&&t.pendingBranch?W(e)?t.effects.push(...e):t.effects.push(e):An(e)}function Bt(e,t){e.activeBranch=t;const{vnode:s,parentComponent:n}=e;let r=t.el;for(;!r&&t.component;)t=t.component.subTree,r=t.el;s.el=r,n&&n.subTree===s&&(n.vnode.el=r,$s(n,r))}function va(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const fe=Symbol.for("v-fgt"),xt=Symbol.for("v-txt"),Ue=Symbol.for("v-cmt"),rs=Symbol.for("v-stc"),os=[];let ke=null;function U(e=!1){os.push(ke=e?null:[])}function Bi(){os.pop(),ke=os[os.length-1]||null}let Dt=1;function Jr(e,t=!1){Dt+=e,e<0&&ke&&t&&(ke.hasOnce=!0)}function Di(e){return e.dynamicChildren=Dt>0?ke||Ht:null,Bi(),Dt>0&&ke&&ke.push(e),e}function Y(e,t,s,n,r,o){return Di(B(e,t,s,n,r,o,!0))}function Ee(e,t,s,n,r){return Di(X(e,t,s,n,r,!0))}function Ut(e){return e?e.__v_isVNode===!0:!1}function ut(e,t){return e.type===t.type&&e.key===t.key}const Ui=({key:e})=>e??null,Ms=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ce(e)||ue(e)||V(e)?{i:we,r:e,k:t,f:!!s}:e:null);function B(e,t=null,s=null,n=0,r=null,o=e===fe?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ui(t),ref:t&&Ms(t),scopeId:li,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:n,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:we};return l?(yr(c,s),o&128&&e.normalize(c)):s&&(c.shapeFlag|=ce(s)?8:16),Dt>0&&!i&&ke&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&ke.push(c),c}const X=_a;function _a(e,t=null,s=null,n=0,r=null,o=!1){if((!e||e===gi)&&(e=Ue),Ut(e)){const l=Vt(e,t,!0);return s&&yr(l,s),Dt>0&&!o&&ke&&(l.shapeFlag&6?ke[ke.indexOf(e)]=l:ke.push(l)),l.patchFlag=-2,l}if(Oa(e)&&(e=e.__vccOpts),t){t=Vi(t);let{class:l,style:c}=t;l&&!ce(l)&&(t.class=Ae(l)),oe(c)&&(ir(c)&&!W(c)&&(c=xe({},c)),t.style=Zs(c))}const i=ce(e)?1:ji(e)?128:Tc(e)?64:oe(e)?4:V(e)?2:0;return B(e,t,s,n,r,i,o,!0)}function Vi(e){return e?ir(e)||Si(e)?xe({},e):e:null}function Vt(e,t,s=!1,n=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,d=t?Wi(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Ui(d),ref:t&&t.ref?s&&o?W(o)?o.concat(Ms(t)):[o,Ms(t)]:Ms(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==fe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Vt(e.ssContent),ssFallback:e.ssFallback&&Vt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&n&&ar(a,c.clone(a)),a}function Pe(e=" ",t=0){return X(xt,null,e,t)}function xa(e,t){const s=X(rs,null,e);return s.staticCount=t,s}function Me(e="",t=!1){return t?(U(),Ee(Ue,null,e)):X(Ue,null,e)}function Fe(e){return e==null||typeof e=="boolean"?X(Ue):W(e)?X(fe,null,e.slice()):Ut(e)?ft(e):X(xt,null,String(e))}function ft(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Vt(e)}function yr(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(W(t))s=16;else if(typeof t=="object")if(n&65){const r=t.default;r&&(r._c&&(r._d=!1),yr(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!Si(t)?t._ctx=we:r===3&&we&&(we.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else V(t)?(t={default:t,_ctx:we},s=32):(t=String(t),n&64?(s=16,t=[Pe(t)]):s=8);e.children=t,e.shapeFlag|=s}function Wi(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const r in n)if(r==="class")t.class!==n.class&&(t.class=Ae([t.class,n.class]));else if(r==="style")t.style=Zs([t.style,n.style]);else if(ds(r)){const o=t[r],i=n[r];i&&o!==i&&!(W(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=n[r])}return t}function Ne(e,t,s,n=null){Xe(e,t,7,[s,n])}const Ca=vi();let Sa=0;function Ta(e,t,s){const n=e.type,r=(t?t.appContext:e.appContext)||Ca,o={uid:Sa++,vnode:e,type:n,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Bo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ri(n,r),emitsOptions:Fi(n,r),emit:null,emitted:null,propsDefaults:re,inheritAttrs:n.inheritAttrs,ctx:re,data:re,props:re,attrs:re,slots:re,refs:re,setupState:re,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=aa.bind(null,o),e.ce&&e.ce(o),o}let ye=null;const Kt=()=>ye||we;let Bs,jn;{const e=Js(),t=(s,n)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(n),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Bs=t("__VUE_INSTANCE_SETTERS__",s=>ye=s),jn=t("__VUE_SSR_SETTERS__",s=>Wt=s)}const Ct=e=>{const t=ye;return Bs(e),e.scope.on(),()=>{e.scope.off(),Bs(t)}},Nn=()=>{ye&&ye.scope.off(),Bs(null)};function zi(e){return e.vnode.shapeFlag&4}let Wt=!1;function Ra(e,t=!1,s=!1){t&&jn(t);const{props:n,children:r}=e.vnode,o=zi(e);Zc(e,n,o,t),$c(e,r,s||t);const i=o?Ma(e,t):void 0;return t&&jn(!1),i}function Ma(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Dc);const{setup:n}=s;if(n){nt();const r=e.setupContext=n.length>1?Aa(e):null,o=Ct(e),i=ps(n,e,0,[e.props,r]),l=Qn(i);if(rt(),o(),(l||e.sp)&&!vt(e)&&fr(e),l){if(i.then(Nn,Nn),t)return i.then(c=>{In(e,c)}).catch(c=>{qt(c,e,0)});e.asyncDep=i}else In(e,i)}else qi(e)}function In(e,t,s){V(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:oe(t)&&(e.setupState=ni(t)),qi(e)}function qi(e,t,s){const n=e.type;e.render||(e.render=n.render||Ye);{const r=Ct(e);nt();try{Vc(e)}finally{rt(),r()}}}const Ea={get(e,t){return _e(e,"get",""),e[t]}};function Aa(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Ea),slots:e.slots,emit:e.emit,expose:t}}function en(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ni(ac(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in ss)return ss[s](e)},has(t,s){return s in t||s in ss}})):e.proxy}function ka(e,t=!0){return V(e)?e.displayName||e.name:e.name||t&&e.__name}function Oa(e){return V(e)&&"__vccOpts"in e}const Ie=(e,t)=>wc(e,t,Wt);function us(e,t,s){const n=arguments.length;return n===2?oe(t)&&!W(t)?Ut(t)?X(e,null,[t]):X(e,t):X(e,null,t):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&Ut(s)&&(s=[s]),X(e,t,s))}const La="3.5.19";/**
* @vue/runtime-dom v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Bn;const Zr=typeof window<"u"&&window.trustedTypes;if(Zr)try{Bn=Zr.createPolicy("vue",{createHTML:e=>e})}catch{}const Gi=Bn?e=>Bn.createHTML(e):e=>e,Ha="http://www.w3.org/2000/svg",Pa="http://www.w3.org/1998/Math/MathML",et=typeof document<"u"?document:null,Yr=et&&et.createElement("template"),Fa={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const r=t==="svg"?et.createElementNS(Ha,e):t==="mathml"?et.createElementNS(Pa,e):s?et.createElement(e,{is:s}):et.createElement(e);return e==="select"&&n&&n.multiple!=null&&r.setAttribute("multiple",n.multiple),r},createText:e=>et.createTextNode(e),createComment:e=>et.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>et.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,r,o){const i=s?s.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===o||!(r=r.nextSibling)););else{Yr.innerHTML=Gi(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=Yr.content;if(n==="svg"||n==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,s)}return[i?i.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},ja=Symbol("_vtc");function Na(e,t,s){const n=e[ja];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Xr=Symbol("_vod"),Ia=Symbol("_vsh"),Ba=Symbol(""),Da=/(^|;)\s*display\s*:/;function Ua(e,t,s){const n=e.style,r=ce(s);let o=!1;if(s&&!r){if(t)if(ce(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();s[l]==null&&Es(n,l,"")}else for(const i in t)s[i]==null&&Es(n,i,"");for(const i in s)i==="display"&&(o=!0),Es(n,i,s[i])}else if(r){if(t!==s){const i=n[Ba];i&&(s+=";"+i),n.cssText=s,o=Da.test(s)}}else t&&e.removeAttribute("style");Xr in e&&(e[Xr]=o?n.display:"",e[Ia]&&(n.display="none"))}const Qr=/\s*!important$/;function Es(e,t,s){if(W(s))s.forEach(n=>Es(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=Va(e,t);Qr.test(s)?e.setProperty(Tt(n),s.replace(Qr,""),"important"):e[n]=s}}const $r=["Webkit","Moz","ms"],wn={};function Va(e,t){const s=wn[t];if(s)return s;let n=De(t);if(n!=="filter"&&n in e)return wn[t]=n;n=Ks(n);for(let r=0;r<$r.length;r++){const o=$r[r]+n;if(o in e)return wn[t]=o}return t}const eo="http://www.w3.org/1999/xlink";function to(e,t,s,n,r,o=Ul(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(eo,t.slice(6,t.length)):e.setAttributeNS(eo,t,s):s==null||o&&!jo(s)?e.removeAttribute(t):e.setAttribute(t,o?"":qe(s)?String(s):s)}function so(e,t,s,n,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Gi(s):s);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=s==null?e.type==="checkbox"?"on":"":String(s);(l!==c||!("_value"in e))&&(e.value=c),s==null&&e.removeAttribute(t),e._value=s;return}let i=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=jo(s):s==null&&l==="string"?(s="",i=!0):l==="number"&&(s=0,i=!0)}try{e[t]=s}catch{}i&&e.removeAttribute(r||t)}function Ki(e,t,s,n){e.addEventListener(t,s,n)}function Wa(e,t,s,n){e.removeEventListener(t,s,n)}const no=Symbol("_vei");function za(e,t,s,n,r=null){const o=e[no]||(e[no]={}),i=o[t];if(n&&i)i.value=n;else{const[l,c]=qa(t);if(n){const d=o[t]=Ja(n,r);Ki(e,l,d,c)}else i&&(Wa(e,l,i,c),o[t]=void 0)}}const ro=/(?:Once|Passive|Capture)$/;function qa(e){let t;if(ro.test(e)){t={};let n;for(;n=e.match(ro);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Tt(e.slice(2)),t]}let bn=0;const Ga=Promise.resolve(),Ka=()=>bn||(Ga.then(()=>bn=0),bn=Date.now());function Ja(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Xe(Za(n,s.value),t,5,[n])};return s.value=e,s.attached=Ka(),s}function Za(e,t){if(W(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>r=>!r._stopped&&n&&n(r))}else return t}const oo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ya=(e,t,s,n,r,o)=>{const i=r==="svg";t==="class"?Na(e,n,i):t==="style"?Ua(e,s,n):ds(t)?Yn(t)||za(e,t,s,n,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Xa(e,t,n,i))?(so(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&to(e,t,n,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ce(n))?so(e,De(t),n,o,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),to(e,t,n,i))};function Xa(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&oo(t)&&V(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return oo(t)&&ce(s)?!1:t in e}const io=e=>{const t=e.props["onUpdate:modelValue"]||!1;return W(t)?s=>Rs(t,s):t},vn=Symbol("_assign"),Qa={created(e,{value:t},s){e.checked=Os(t,s.props.value),e[vn]=io(s),Ki(e,"change",()=>{e[vn]($a(e))})},beforeUpdate(e,{value:t,oldValue:s},n){e[vn]=io(n),t!==s&&(e.checked=Os(t,n.props.value))}};function $a(e){return"_value"in e?e._value:e.value}const Ji=xe({patchProp:Ya},Fa);let is,lo=!1;function e1(){return is||(is=ta(Ji))}function t1(){return is=lo?is:sa(Ji),lo=!0,is}const s1=((...e)=>{const t=e1().createApp(...e),{mount:s}=t;return t.mount=n=>{const r=Yi(n);if(!r)return;const o=t._component;!V(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=s(r,!1,Zi(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t}),n1=((...e)=>{const t=t1().createApp(...e),{mount:s}=t;return t.mount=n=>{const r=Yi(n);if(r)return s(r,!0,Zi(r))},t});function Zi(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Yi(e){return ce(e)?document.querySelector(e):e}const r1=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,o1=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,i1=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function l1(e,t){if(e==="__proto__"||e==="constructor"&&t&&typeof t=="object"&&"prototype"in t){c1(e);return}return t}function c1(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}function Ds(e,t={}){if(typeof e!="string")return e;if(e[0]==='"'&&e[e.length-1]==='"'&&e.indexOf("\\")===-1)return e.slice(1,-1);const s=e.trim();if(s.length<=9)switch(s.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!i1.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(r1.test(e)||o1.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,l1)}return JSON.parse(e)}catch(n){if(t.strict)throw n;return e}}const a1=/#/g,f1=/&/g,u1=/\//g,d1=/=/g,wr=/\+/g,h1=/%5e/gi,p1=/%60/gi,g1=/%7c/gi,m1=/%20/gi;function y1(e){return encodeURI(""+e).replace(g1,"|")}function Dn(e){return y1(typeof e=="string"?e:JSON.stringify(e)).replace(wr,"%2B").replace(m1,"+").replace(a1,"%23").replace(f1,"%26").replace(p1,"`").replace(h1,"^").replace(u1,"%2F")}function _n(e){return Dn(e).replace(d1,"%3D")}function Us(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function w1(e){return Us(e.replace(wr," "))}function b1(e){return Us(e.replace(wr," "))}function tn(e=""){const t=Object.create(null);e[0]==="?"&&(e=e.slice(1));for(const s of e.split("&")){const n=s.match(/([^=]+)=?(.*)/)||[];if(n.length<2)continue;const r=w1(n[1]);if(r==="__proto__"||r==="constructor")continue;const o=b1(n[2]||"");t[r]===void 0?t[r]=o:Array.isArray(t[r])?t[r].push(o):t[r]=[t[r],o]}return t}function v1(e,t){return(typeof t=="number"||typeof t=="boolean")&&(t=String(t)),t?Array.isArray(t)?t.map(s=>`${_n(e)}=${Dn(s)}`).join("&"):`${_n(e)}=${Dn(t)}`:_n(e)}function Xi(e){return Object.keys(e).filter(t=>e[t]!==void 0).map(t=>v1(t,e[t])).filter(Boolean).join("&")}const _1=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,x1=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,C1=/^([/\\]\s*){2,}[^/\\]/,S1=/^[\s\0]*(blob|data|javascript|vbscript):$/i,T1=/\/$|\/\?|\/#/,R1=/^\.?\//;function M1(e){return["./","../"].some(t=>e.startsWith(t))}function ht(e,t={}){return typeof t=="boolean"&&(t={acceptRelative:t}),t.strict?_1.test(e):x1.test(e)||(t.acceptRelative?C1.test(e):!1)}function E1(e){return!!e&&S1.test(e)}function Un(e="",t){return t?T1.test(e):e.endsWith("/")}function sn(e="",t){if(!t)return(Un(e)?e.slice(0,-1):e)||"/";if(!Un(e,!0))return e||"/";let s=e,n="";const r=e.indexOf("#");r!==-1&&(s=e.slice(0,r),n=e.slice(r));const[o,...i]=s.split("?");return((o.endsWith("/")?o.slice(0,-1):o)||"/")+(i.length>0?`?${i.join("?")}`:"")+n}function Vs(e="",t){if(!t)return e.endsWith("/")?e:e+"/";if(Un(e,!0))return e||"/";let s=e,n="";const r=e.indexOf("#");if(r!==-1&&(s=e.slice(0,r),n=e.slice(r),!s))return n;const[o,...i]=s.split("?");return o+"/"+(i.length>0?`?${i.join("?")}`:"")+n}function A1(e=""){return e.startsWith("/")}function co(e=""){return A1(e)?e:"/"+e}function Qi(e,t){if($i(t)||ht(e))return e;const s=sn(t);return e.startsWith(s)?e:St(s,e)}function k1(e,t){if($i(t))return e;const s=sn(t);if(!e.startsWith(s))return e;const n=e.slice(s.length);return n[0]==="/"?n:"/"+n}function Lt(e,t){const s=sl(e),n={...tn(s.search),...t};return s.search=Xi(n),nl(s)}function $i(e){return!e||e==="/"}function O1(e){return e&&e!=="/"}function St(e,...t){let s=e||"";for(const n of t.filter(r=>O1(r)))if(s){const r=n.replace(R1,"");s=Vs(s)+r}else s=n;return s}function el(...e){const t=/\/(?!\/)/,s=e.filter(Boolean),n=[];let r=0;for(const i of s)if(!(!i||i==="/")){for(const[l,c]of i.split(t).entries())if(!(!c||c===".")){if(c===".."){if(n.length===1&&ht(n[0]))continue;n.pop(),r--;continue}if(l===1&&n[n.length-1]?.endsWith(":/")){n[n.length-1]+="/"+c;continue}n.push(c),r++}}let o=n.join("/");return r>=0?s[0]?.startsWith("/")&&!o.startsWith("/")?o="/"+o:s[0]?.startsWith("./")&&!o.startsWith("./")&&(o="./"+o):o="../".repeat(-1*r)+o,s[s.length-1]?.endsWith("/")&&!o.endsWith("/")&&(o+="/"),o}function L1(e,t,s={}){return s.trailingSlash||(e=Vs(e),t=Vs(t)),s.leadingSlash||(e=co(e),t=co(t)),s.encoding||(e=Us(e),t=Us(t)),e===t}const tl=Symbol.for("ufo:protocolRelative");function sl(e="",t){const s=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(s){const[,f,h=""]=s;return{protocol:f.toLowerCase(),pathname:h,href:f+h,auth:"",host:"",search:"",hash:""}}if(!ht(e,{acceptRelative:!0}))return ao(e);const[,n="",r,o=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,i="",l=""]=o.match(/([^#/?]*)(.*)?/)||[];n==="file:"&&(l=l.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:c,search:d,hash:a}=ao(l);return{protocol:n.toLowerCase(),auth:r?r.slice(0,Math.max(0,r.length-1)):"",host:i,pathname:c,search:d,hash:a,[tl]:!n}}function ao(e=""){const[t="",s="",n=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:s,hash:n}}function nl(e){const t=e.pathname||"",s=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",n=e.hash||"",r=e.auth?e.auth+"@":"",o=e.host||"";return(e.protocol||e[tl]?(e.protocol||"")+"//":"")+r+o+t+s+n}class H1 extends Error{constructor(t,s){super(t,s),this.name="FetchError",s?.cause&&!this.cause&&(this.cause=s.cause)}}function P1(e){const t=e.error?.message||e.error?.toString()||"",s=e.request?.method||e.options?.method||"GET",n=e.request?.url||String(e.request)||"/",r=`[${s}] ${JSON.stringify(n)}`,o=e.response?`${e.response.status} ${e.response.statusText}`:"<no response>",i=`${r}: ${o}${t?` ${t}`:""}`,l=new H1(i,e.error?{cause:e.error}:void 0);for(const c of["request","options","response"])Object.defineProperty(l,c,{get(){return e[c]}});for(const[c,d]of[["data","_data"],["status","status"],["statusCode","status"],["statusText","statusText"],["statusMessage","statusText"]])Object.defineProperty(l,c,{get(){return e.response&&e.response[d]}});return l}const F1=new Set(Object.freeze(["PATCH","POST","PUT","DELETE"]));function fo(e="GET"){return F1.has(e.toUpperCase())}function j1(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}const N1=new Set(["image/svg","application/xml","application/xhtml","application/html"]),I1=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function B1(e=""){if(!e)return"json";const t=e.split(";").shift()||"";return I1.test(t)?"json":N1.has(t)||t.startsWith("text/")?"text":"blob"}function D1(e,t,s,n){const r=U1(t?.headers??e?.headers,s?.headers,n);let o;return(s?.query||s?.params||t?.params||t?.query)&&(o={...s?.params,...s?.query,...t?.params,...t?.query}),{...s,...t,query:o,params:o,headers:r}}function U1(e,t,s){if(!t)return new s(e);const n=new s(t);if(e)for(const[r,o]of Symbol.iterator in e||Array.isArray(e)?e:new s(e))n.set(r,o);return n}async function Cs(e,t){if(t)if(Array.isArray(t))for(const s of t)await s(e);else await t(e)}const V1=new Set([408,409,425,429,500,502,503,504]),W1=new Set([101,204,205,304]);function rl(e={}){const{fetch:t=globalThis.fetch,Headers:s=globalThis.Headers,AbortController:n=globalThis.AbortController}=e;async function r(l){const c=l.error&&l.error.name==="AbortError"&&!l.options.timeout||!1;if(l.options.retry!==!1&&!c){let a;typeof l.options.retry=="number"?a=l.options.retry:a=fo(l.options.method)?0:1;const f=l.response&&l.response.status||500;if(a>0&&(Array.isArray(l.options.retryStatusCodes)?l.options.retryStatusCodes.includes(f):V1.has(f))){const h=typeof l.options.retryDelay=="function"?l.options.retryDelay(l):l.options.retryDelay||0;return h>0&&await new Promise(u=>setTimeout(u,h)),o(l.request,{...l.options,retry:a-1})}}const d=P1(l);throw Error.captureStackTrace&&Error.captureStackTrace(d,o),d}const o=async function(c,d={}){const a={request:c,options:D1(c,d,e.defaults,s),response:void 0,error:void 0};a.options.method&&(a.options.method=a.options.method.toUpperCase()),a.options.onRequest&&await Cs(a,a.options.onRequest),typeof a.request=="string"&&(a.options.baseURL&&(a.request=Qi(a.request,a.options.baseURL)),a.options.query&&(a.request=Lt(a.request,a.options.query),delete a.options.query),"query"in a.options&&delete a.options.query,"params"in a.options&&delete a.options.params),a.options.body&&fo(a.options.method)&&(j1(a.options.body)?(a.options.body=typeof a.options.body=="string"?a.options.body:JSON.stringify(a.options.body),a.options.headers=new s(a.options.headers||{}),a.options.headers.has("content-type")||a.options.headers.set("content-type","application/json"),a.options.headers.has("accept")||a.options.headers.set("accept","application/json")):("pipeTo"in a.options.body&&typeof a.options.body.pipeTo=="function"||typeof a.options.body.pipe=="function")&&("duplex"in a.options||(a.options.duplex="half")));let f;if(!a.options.signal&&a.options.timeout){const u=new n;f=setTimeout(()=>{const w=new Error("[TimeoutError]: The operation was aborted due to timeout");w.name="TimeoutError",w.code=23,u.abort(w)},a.options.timeout),a.options.signal=u.signal}try{a.response=await t(a.request,a.options)}catch(u){return a.error=u,a.options.onRequestError&&await Cs(a,a.options.onRequestError),await r(a)}finally{f&&clearTimeout(f)}if((a.response.body||a.response._bodyInit)&&!W1.has(a.response.status)&&a.options.method!=="HEAD"){const u=(a.options.parseResponse?"json":a.options.responseType)||B1(a.response.headers.get("content-type")||"");switch(u){case"json":{const w=await a.response.text(),y=a.options.parseResponse||Ds;a.response._data=y(w);break}case"stream":{a.response._data=a.response.body||a.response._bodyInit;break}default:a.response._data=await a.response[u]()}}return a.options.onResponse&&await Cs(a,a.options.onResponse),!a.options.ignoreResponseError&&a.response.status>=400&&a.response.status<600?(a.options.onResponseError&&await Cs(a,a.options.onResponseError),await r(a)):a.response},i=async function(c,d){return(await o(c,d))._data};return i.raw=o,i.native=(...l)=>t(...l),i.create=(l={},c={})=>rl({...e,...c,defaults:{...e.defaults,...c.defaults,...l}}),i}const Ws=(function(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")})(),z1=Ws.fetch?(...e)=>Ws.fetch(...e):()=>Promise.reject(new Error("[ofetch] global.fetch is not supported!")),q1=Ws.Headers,G1=Ws.AbortController,K1=rl({fetch:z1,Headers:q1,AbortController:G1}),J1=K1,Z1=()=>window?.__NUXT__?.config||window?.useNuxtApp?.().payload?.config,br=()=>Z1().app,Y1=()=>br().baseURL,X1=()=>br().buildAssetsDir,vr=(...e)=>el(ol(),X1(),...e),ol=(...e)=>{const t=br(),s=t.cdnURL||t.baseURL;return e.length?el(s,...e):s};globalThis.__buildAssetsURL=vr,globalThis.__publicAssetsURL=ol;globalThis.$fetch||(globalThis.$fetch=J1.create({baseURL:Y1()}));"global"in globalThis||(globalThis.global=globalThis);function Vn(e,t={},s){for(const n in e){const r=e[n],o=s?`${s}:${n}`:n;typeof r=="object"&&r!==null?Vn(r,t,o):typeof r=="function"&&(t[o]=r)}return t}const Q1={run:e=>e()},$1=()=>Q1,il=typeof console.createTask<"u"?console.createTask:$1;function ef(e,t){const s=t.shift(),n=il(s);return e.reduce((r,o)=>r.then(()=>n.run(()=>o(...t))),Promise.resolve())}function tf(e,t){const s=t.shift(),n=il(s);return Promise.all(e.map(r=>n.run(()=>r(...t))))}function xn(e,t){for(const s of[...e])s(t)}class sf{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,s,n={}){if(!t||typeof s!="function")return()=>{};const r=t;let o;for(;this._deprecatedHooks[t];)o=this._deprecatedHooks[t],t=o.to;if(o&&!n.allowDeprecated){let i=o.message;i||(i=`${r} hook has been deprecated`+(o.to?`, please use ${o.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!s.name)try{Object.defineProperty(s,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(s),()=>{s&&(this.removeHook(t,s),s=void 0)}}hookOnce(t,s){let n,r=(...o)=>(typeof n=="function"&&n(),n=void 0,r=void 0,s(...o));return n=this.hook(t,r),n}removeHook(t,s){if(this._hooks[t]){const n=this._hooks[t].indexOf(s);n!==-1&&this._hooks[t].splice(n,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,s){this._deprecatedHooks[t]=typeof s=="string"?{to:s}:s;const n=this._hooks[t]||[];delete this._hooks[t];for(const r of n)this.hook(t,r)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const s in t)this.deprecateHook(s,t[s])}addHooks(t){const s=Vn(t),n=Object.keys(s).map(r=>this.hook(r,s[r]));return()=>{for(const r of n.splice(0,n.length))r()}}removeHooks(t){const s=Vn(t);for(const n in s)this.removeHook(n,s[n])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...s){return s.unshift(t),this.callHookWith(ef,t,...s)}callHookParallel(t,...s){return s.unshift(t),this.callHookWith(tf,t,...s)}callHookWith(t,s,...n){const r=this._before||this._after?{name:s,args:n,context:{}}:void 0;this._before&&xn(this._before,r);const o=t(s in this._hooks?[...this._hooks[s]]:[],n);return o instanceof Promise?o.finally(()=>{this._after&&r&&xn(this._after,r)}):(this._after&&r&&xn(this._after,r),o)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const s=this._before.indexOf(t);s!==-1&&this._before.splice(s,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const s=this._after.indexOf(t);s!==-1&&this._after.splice(s,1)}}}}function ll(){return new sf}function nf(e={}){let t,s=!1;const n=i=>{if(t&&t!==i)throw new Error("Context conflict")};let r;if(e.asyncContext){const i=e.AsyncLocalStorage||globalThis.AsyncLocalStorage;i?r=new i:console.warn("[unctx] `AsyncLocalStorage` is not provided.")}const o=()=>{if(r){const i=r.getStore();if(i!==void 0)return i}return t};return{use:()=>{const i=o();if(i===void 0)throw new Error("Context is not available");return i},tryUse:()=>o(),set:(i,l)=>{l||n(i),t=i,s=!0},unset:()=>{t=void 0,s=!1},call:(i,l)=>{n(i),t=i;try{return r?r.run(i,l):l()}finally{s||(t=void 0)}},async callAsync(i,l){t=i;const c=()=>{t=i},d=()=>t===i?c:void 0;Wn.add(d);try{const a=r?r.run(i,l):l();return s||(t=void 0),await a}finally{Wn.delete(d)}}}}function rf(e={}){const t={};return{get(s,n={}){return t[s]||(t[s]=nf({...e,...n})),t[s]}}}const zs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof global<"u"?global:typeof window<"u"?window:{},uo="__unctx__",of=zs[uo]||(zs[uo]=rf()),lf=(e,t={})=>of.get(e,t),ho="__unctx_async_handlers__",Wn=zs[ho]||(zs[ho]=new Set);function cl(e){const t=[];for(const r of Wn){const o=r();o&&t.push(o)}const s=()=>{for(const r of t)r()};let n=e();return n&&typeof n=="object"&&"catch"in n&&(n=n.catch(r=>{throw s(),r})),[n,s]}const po={id:"__nuxt-loader"},cf={componentName:"NuxtLink",prefetch:!0,prefetchOn:{visibility:!0}},af="#__nuxt",al="nuxt-app",go=36e5,ff="vite:preloadError";function fl(e=al){return lf(e,{asyncContext:!1})}const uf="__nuxt_plugin";function df(e){let t=0;const s={_id:e.id||al||"nuxt-app",_scope:Wl(),provide:void 0,versions:{get nuxt(){return"4.0.3"},get vue(){return s.vueApp.version}},payload:kt({...e.ssrContext?.payload||{},data:kt({}),state:ot({}),once:new Set,_errors:kt({})}),static:{data:{}},runWithContext(r){return s._scope.active&&!Do()?s._scope.run(()=>mo(s,r)):mo(s,r)},isHydrating:!0,deferHydration(){if(!s.isHydrating)return()=>{};t++;let r=!1;return()=>{if(!r&&(r=!0,t--,t===0))return s.isHydrating=!1,s.callHook("app:suspense:resolve")}},_asyncDataPromises:{},_asyncData:kt({}),_payloadRevivers:{},...e};{const r=window.__NUXT__;if(r)for(const o in r)switch(o){case"data":case"state":case"_errors":Object.assign(s.payload[o],r[o]);break;default:s.payload[o]=r[o]}}s.hooks=ll(),s.hook=s.hooks.hook,s.callHook=s.hooks.callHook,s.provide=(r,o)=>{const i="$"+r;Ss(s,i,o),Ss(s.vueApp.config.globalProperties,i,o)},Ss(s.vueApp,"$nuxt",s),Ss(s.vueApp.config.globalProperties,"$nuxt",s);{window.addEventListener(ff,o=>{s.callHook("app:chunkError",{error:o.payload}),o.payload.message.includes("Unable to preload CSS")&&o.preventDefault()}),window.useNuxtApp||=pe;const r=s.hook("app:error",(...o)=>{console.error("[nuxt] error caught during app initialization",...o)});s.hook("app:mounted",r)}const n=s.payload.config;return s.provide("config",n),s}function hf(e,t){t.hooks&&e.hooks.addHooks(t.hooks)}async function pf(e,t){if(typeof t=="function"){const{provide:s}=await e.runWithContext(()=>t(e))||{};if(s&&typeof s=="object")for(const n in s)e.provide(n,s[n])}}async function gf(e,t){const s=new Set,n=[],r=[];let o,i=0;async function l(c){const d=c.dependsOn?.filter(a=>t.some(f=>f._name===a)&&!s.has(a))??[];if(d.length>0)n.push([new Set(d),c]);else{const a=pf(e,c).then(async()=>{c._name&&(s.add(c._name),await Promise.all(n.map(async([f,h])=>{f.has(c._name)&&(f.delete(c._name),f.size===0&&(i++,await l(h)))})))}).catch(f=>{if(!c.parallel&&!e.payload.error)throw f;o||=f});c.parallel?r.push(a):await a}}for(const c of t)hf(e,c);for(const c of t)await l(c);if(await Promise.all(r),i)for(let c=0;c<i;c++)await Promise.all(r);if(o)throw e.payload.error||o}function ct(e){if(typeof e=="function")return e;const t=e._name||e.name;return delete e.name,Object.assign(e.setup||(()=>{}),e,{[uf]:!0,_name:t})}function mo(e,t,s){const n=()=>t();return fl(e._id).set(e),e.vueApp.runWithContext(n)}function mf(e){let t;return hr()&&(t=Kt()?.appContext.app.$nuxt),t||=fl(e).tryUse(),t||null}function pe(e){const t=mf(e);if(!t)throw new Error("[nuxt] instance unavailable");return t}function zt(e){return pe().$config}function Ss(e,t,s){Object.defineProperty(e,t,{get:()=>s})}function yf(e,t){return{ctx:{table:e},matchAll:s=>dl(s,e)}}function ul(e){const t={};for(const s in e)t[s]=s==="dynamic"?new Map(Object.entries(e[s]).map(([n,r])=>[n,ul(r)])):new Map(Object.entries(e[s]));return t}function wf(e){return yf(ul(e))}function dl(e,t,s){e.endsWith("/")&&(e=e.slice(0,-1)||"/");const n=[];for(const[o,i]of yo(t.wildcard))(e===o||e.startsWith(o+"/"))&&n.push(i);for(const[o,i]of yo(t.dynamic))if(e.startsWith(o+"/")){const l="/"+e.slice(o.length).split("/").splice(2).join("/");n.push(...dl(l,i))}const r=t.static.get(e);return r&&n.push(r),n.filter(Boolean)}function yo(e){return[...e.entries()].sort((t,s)=>t[0].length-s[0].length)}function Cn(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function zn(e,t,s=".",n){if(!Cn(t))return zn(e,{},s,n);const r=Object.assign({},t);for(const o in e){if(o==="__proto__"||o==="constructor")continue;const i=e[o];i!=null&&(n&&n(r,o,i,s)||(Array.isArray(i)&&Array.isArray(r[o])?r[o]=[...i,...r[o]]:Cn(i)&&Cn(r[o])?r[o]=zn(i,r[o],(s?`${s}.`:"")+o.toString(),n):r[o]=i))}return r}function bf(e){return(...t)=>t.reduce((s,n)=>zn(s,n,"",e),{})}const vf=bf();function _f(e,t){try{return t in e}catch{return!1}}class wo extends Error{static __h3_error__=!0;statusCode=500;fatal=!1;unhandled=!1;statusMessage;data;cause;constructor(t,s={}){super(t,s),s.cause&&!this.cause&&(this.cause=s.cause)}toJSON(){const t={message:this.message,statusCode:qn(this.statusCode,500)};return this.statusMessage&&(t.statusMessage=hl(this.statusMessage)),this.data!==void 0&&(t.data=this.data),t}}function xf(e){if(typeof e=="string")return new wo(e);if(Cf(e))return e;const t=new wo(e.message??e.statusMessage??"",{cause:e.cause||e});if(_f(e,"stack"))try{Object.defineProperty(t,"stack",{get(){return e.stack}})}catch{try{t.stack=e.stack}catch{}}if(e.data&&(t.data=e.data),e.statusCode?t.statusCode=qn(e.statusCode,t.statusCode):e.status&&(t.statusCode=qn(e.status,t.statusCode)),e.statusMessage?t.statusMessage=e.statusMessage:e.statusText&&(t.statusMessage=e.statusText),t.statusMessage){const s=t.statusMessage;hl(t.statusMessage)!==s&&console.warn("[h3] Please prefer using `message` for longer error messages instead of `statusMessage`. In the future, `statusMessage` will be sanitized by default.")}return e.fatal!==void 0&&(t.fatal=e.fatal),e.unhandled!==void 0&&(t.unhandled=e.unhandled),t}function Cf(e){return e?.constructor?.__h3_error__===!0}const Sf=/[^\u0009\u0020-\u007E]/g;function hl(e=""){return e.replace(Sf,"")}function qn(e,t=200){return!e||(typeof e=="string"&&(e=Number.parseInt(e,10)),e<100||e>999)?t:e}const pl=Symbol("route"),Qe=()=>pe()?.$router,gl=()=>hr()?It(pl,pe()._route):pe()._route;const Tf=()=>{try{if(pe()._processingMiddleware)return!0}catch{return!1}return!1},ml=(e,t)=>{e||="/";const s=typeof e=="string"?e:"path"in e?Gn(e):Qe().resolve(e).href;if(t?.open){const{target:c="_blank",windowFeatures:d={}}=t.open,a=Object.entries(d).filter(([f,h])=>h!==void 0).map(([f,h])=>`${f.toLowerCase()}=${h}`).join(", ");return open(s,c,a),Promise.resolve()}const n=ht(s,{acceptRelative:!0}),r=t?.external||n;if(r){if(!t?.external)throw new Error("Navigating to an external URL is not allowed by default. Use `navigateTo(url, { external: true })`.");const{protocol:c}=new URL(s,window.location.href);if(c&&E1(c))throw new Error(`Cannot navigate to a URL with '${c}' protocol.`)}const o=Tf();if(!r&&o){if(t?.replace){if(typeof e=="string"){const{pathname:c,search:d,hash:a}=sl(e);return{path:c,...d&&{query:tn(d)},...a&&{hash:a},replace:!0}}return{...e,replace:!0}}return e}const i=Qe(),l=pe();return r?(l._scope.stop(),t?.replace?location.replace(s):location.href=s,o?l.isHydrating?new Promise(()=>{}):!1:Promise.resolve()):t?.replace?i.replace(e):i.push(e)};function Gn(e){return Lt(e.path||"",e.query||{})+(e.hash||"")}const yl="__nuxt_error",_r=()=>gc(pe().payload,"error"),Rf=e=>{const t=xr(e);try{const s=pe(),n=_r();s.hooks.callHook("app:error",t),n.value||=t}catch{throw t}return t},Mf=async(e={})=>{const t=pe(),s=_r();t.callHook("app:error:cleared",e),e.redirect&&await Qe().replace(e.redirect),s.value=void 0},Ef=e=>!!e&&typeof e=="object"&&yl in e,xr=e=>{const t=xf(e);return Object.defineProperty(t,yl,{value:!0,configurable:!1,writable:!1}),t};function bo(e){const t=kf(e),s=new ArrayBuffer(t.length),n=new DataView(s);for(let r=0;r<s.byteLength;r++)n.setUint8(r,t.charCodeAt(r));return s}const Af="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function kf(e){e.length%4===0&&(e=e.replace(/==?$/,""));let t="",s=0,n=0;for(let r=0;r<e.length;r++)s<<=6,s|=Af.indexOf(e[r]),n+=6,n===24&&(t+=String.fromCharCode((s&16711680)>>16),t+=String.fromCharCode((s&65280)>>8),t+=String.fromCharCode(s&255),s=n=0);return n===12?(s>>=4,t+=String.fromCharCode(s)):n===18&&(s>>=2,t+=String.fromCharCode((s&65280)>>8),t+=String.fromCharCode(s&255)),t}const Of=-1,Lf=-2,Hf=-3,Pf=-4,Ff=-5,jf=-6;function Nf(e,t){return If(JSON.parse(e),t)}function If(e,t){if(typeof e=="number")return r(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const s=e,n=Array(s.length);function r(o,i=!1){if(o===Of)return;if(o===Hf)return NaN;if(o===Pf)return 1/0;if(o===Ff)return-1/0;if(o===jf)return-0;if(i)throw new Error("Invalid input");if(o in n)return n[o];const l=s[o];if(!l||typeof l!="object")n[o]=l;else if(Array.isArray(l))if(typeof l[0]=="string"){const c=l[0],d=t?.[c];if(d)return n[o]=d(r(l[1]));switch(c){case"Date":n[o]=new Date(l[1]);break;case"Set":const a=new Set;n[o]=a;for(let u=1;u<l.length;u+=1)a.add(r(l[u]));break;case"Map":const f=new Map;n[o]=f;for(let u=1;u<l.length;u+=2)f.set(r(l[u]),r(l[u+1]));break;case"RegExp":n[o]=new RegExp(l[1],l[2]);break;case"Object":n[o]=Object(l[1]);break;case"BigInt":n[o]=BigInt(l[1]);break;case"null":const h=Object.create(null);n[o]=h;for(let u=1;u<l.length;u+=2)h[l[u]]=r(l[u+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const u=globalThis[c],w=l[1],y=bo(w),M=new u(y);n[o]=M;break}case"ArrayBuffer":{const u=l[1],w=bo(u);n[o]=w;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(l.length);n[o]=c;for(let d=0;d<l.length;d+=1){const a=l[d];a!==Lf&&(c[d]=r(a))}}else{const c={};n[o]=c;for(const d in l){const a=l[d];c[d]=r(a)}}return n[o]}return r(0)}const Bf=new Set(["link","style","script","noscript"]),Df=new Set(["title","titleTemplate","script","style","noscript"]),vo=new Set(["base","meta","link","style","script","noscript"]),Uf=new Set(["title","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"]),Vf=new Set(["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"]),Wf=new Set(["key","tagPosition","tagPriority","tagDuplicateStrategy","innerHTML","textContent","processTemplateParams"]),zf=new Set(["templateParams","htmlAttrs","bodyAttrs"]),qf=new Set(["theme-color","google-site-verification","og","article","book","profile","twitter","author"]),Gf=["name","property","http-equiv"],Kf=new Set(["viewport","description","keywords","robots"]);function wl(e){const t=e.split(":");return t.length?qf.has(t[1]):!1}function Kn(e){const{props:t,tag:s}=e;if(Vf.has(s))return s;if(s==="link"&&t.rel==="canonical")return"canonical";if(t.charset)return"charset";if(e.tag==="meta"){for(const n of Gf)if(t[n]!==void 0){const r=t[n],o=r.includes(":"),i=Kf.has(r),c=!(o||i)&&e.key?`:key:${e.key}`:"";return`${s}:${r}${c}`}}if(e.key)return`${s}:key:${e.key}`;if(t.id)return`${s}:id:${t.id}`;if(Df.has(s)){const n=e.textContent||e.innerHTML;if(n)return`${s}:content:${n}`}}function _o(e){const t=e._h||e._d;if(t)return t;const s=e.textContent||e.innerHTML;return s||`${e.tag}:${Object.entries(e.props).map(([n,r])=>`${n}:${String(r)}`).join(",")}`}function qs(e,t,s){typeof e==="function"&&(!s||s!=="titleTemplate"&&!(s[0]==="o"&&s[1]==="n"))&&(e=e());let r;if(t&&(r=t(s,e)),Array.isArray(r))return r.map(o=>qs(o,t));if(r?.constructor===Object){const o={};for(const i of Object.keys(r))o[i]=qs(r[i],t,i);return o}return r}function Jf(e,t){const s=e==="style"?new Map:new Set;function n(r){const o=r.trim();if(o)if(e==="style"){const[i,...l]=o.split(":").map(c=>c.trim());i&&l.length&&s.set(i,l.join(":"))}else o.split(" ").filter(Boolean).forEach(i=>s.add(i))}return typeof t=="string"?e==="style"?t.split(";").forEach(n):n(t):Array.isArray(t)?t.forEach(r=>n(r)):t&&typeof t=="object"&&Object.entries(t).forEach(([r,o])=>{o&&o!=="false"&&(e==="style"?s.set(r.trim(),o):n(r))}),s}function bl(e,t){return e.props=e.props||{},t?e.tag==="templateParams"?(e.props=t,e):(Object.entries(t).forEach(([s,n])=>{if(n===null){e.props[s]=null;return}if(s==="class"||s==="style"){e.props[s]=Jf(s,n);return}if(Wf.has(s)){if(["textContent","innerHTML"].includes(s)&&typeof n=="object"){let i=t.type;if(t.type||(i="application/json"),!i?.endsWith("json")&&i!=="speculationrules")return;t.type=i,e.props.type=i,e[s]=JSON.stringify(n)}else e[s]=n;return}const r=String(n),o=s.startsWith("data-");r==="true"||r===""?e.props[s]=o?r:!0:!n&&o&&r==="false"?e.props[s]="false":n!==void 0&&(e.props[s]=n)}),e):e}function Zf(e,t){const s=typeof t=="object"&&typeof t!="function"?t:{[e==="script"||e==="noscript"||e==="style"?"innerHTML":"textContent"]:t},n=bl({tag:e,props:{}},s);return n.key&&Bf.has(n.tag)&&(n.props["data-hid"]=n._h=n.key),n.tag==="script"&&typeof n.innerHTML=="object"&&(n.innerHTML=JSON.stringify(n.innerHTML),n.props.type=n.props.type||"application/json"),Array.isArray(n.props.content)?n.props.content.map(r=>({...n,props:{...n.props,content:r}})):n}function Yf(e,t){if(!e)return[];typeof e=="function"&&(e=e());const s=(r,o)=>{for(let i=0;i<t.length;i++)o=t[i](r,o);return o};e=s(void 0,e);const n=[];return e=qs(e,s),Object.entries(e||{}).forEach(([r,o])=>{if(o!==void 0)for(const i of Array.isArray(o)?o:[o])n.push(Zf(r,i))}),n.flat()}const xo=(e,t)=>e._w===t._w?e._p-t._p:e._w-t._w,Co={base:-10,title:10},Xf={critical:-8,high:-1,low:2},So={meta:{"content-security-policy":-30,charset:-20,viewport:-15},link:{preconnect:20,stylesheet:60,preload:70,modulepreload:70,prefetch:90,"dns-prefetch":90,prerender:90},script:{async:30,defer:80,sync:50},style:{imported:40,sync:60}},Qf=/@import/,Xt=e=>e===""||e===!0;function $f(e,t){if(typeof t.tagPriority=="number")return t.tagPriority;let s=100;const n=Xf[t.tagPriority]||0,r=e.resolvedOptions.disableCapoSorting?{link:{},script:{},style:{}}:So;if(t.tag in Co)s=Co[t.tag];else if(t.tag==="meta"){const o=t.props["http-equiv"]==="content-security-policy"?"content-security-policy":t.props.charset?"charset":t.props.name==="viewport"?"viewport":null;o&&(s=So.meta[o])}else t.tag==="link"&&t.props.rel?s=r.link[t.props.rel]:t.tag==="script"?Xt(t.props.async)?s=r.script.async:t.props.src&&!Xt(t.props.defer)&&!Xt(t.props.async)&&t.props.type!=="module"&&!t.props.type?.endsWith("json")?s=r.script.sync:Xt(t.props.defer)&&t.props.src&&!Xt(t.props.async)&&(s=r.script.defer):t.tag==="style"&&(s=t.innerHTML&&Qf.test(t.innerHTML)?r.style.imported:r.style.sync);return(s||100)+n}function To(e,t){const s=typeof t=="function"?t(e):t,n=s.key||String(e.plugins.size+1);e.plugins.get(n)||(e.plugins.set(n,s),e.hooks.addHooks(s.hooks||{}))}function eu(e={}){const t=ll();t.addHooks(e.hooks||{});const s=!e.document,n=new Map,r=new Map,o=new Set,i={_entryCount:1,plugins:r,dirty:!1,resolvedOptions:e,hooks:t,ssr:s,entries:n,headEntries(){return[...n.values()]},use:l=>To(i,l),push(l,c){const d={...c||{}};delete d.head;const a=d._index??i._entryCount++,f={_i:a,input:l,options:d},h={_poll(u=!1){i.dirty=!0,!u&&o.add(a),t.callHook("entries:updated",i)},dispose(){n.delete(a)&&i.invalidate()},patch(u){(!d.mode||d.mode==="server"&&s||d.mode==="client"&&!s)&&(f.input=u,n.set(a,f),h._poll())}};return h.patch(l),h},async resolveTags(){const l={tagMap:new Map,tags:[],entries:[...i.entries.values()]};for(await t.callHook("entries:resolve",l);o.size;){const h=o.values().next().value;o.delete(h);const u=n.get(h);if(u){const w={tags:Yf(u.input,e.propResolvers||[]).map(y=>Object.assign(y,u.options)),entry:u};await t.callHook("entries:normalize",w),u._tags=w.tags.map((y,M)=>(y._w=$f(i,y),y._p=(u._i<<10)+M,y._d=Kn(y),y))}}let c=!1;l.entries.flatMap(h=>(h._tags||[]).map(u=>({...u,props:{...u.props}}))).sort(xo).reduce((h,u)=>{const w=String(u._d||u._p);if(!h.has(w))return h.set(w,u);const y=h.get(w);if((u?.tagDuplicateStrategy||(zf.has(u.tag)?"merge":null)||(u.key&&u.key===y.key?"merge":null))==="merge"){const x={...y.props};Object.entries(u.props).forEach(([k,g])=>x[k]=k==="style"?new Map([...y.props.style||new Map,...g]):k==="class"?new Set([...y.props.class||new Set,...g]):g),h.set(w,{...u,props:x})}else u._p>>10===y._p>>10&&u.tag==="meta"&&wl(w)?(h.set(w,Object.assign([...Array.isArray(y)?y:[y],u],u)),c=!0):(u._w===y._w?u._p>y._p:u?._w<y?._w)&&h.set(w,u);return h},l.tagMap);const d=l.tagMap.get("title"),a=l.tagMap.get("titleTemplate");if(i._title=d?.textContent,a){const h=a?.textContent;if(i._titleTemplate=h,h){let u=typeof h=="function"?h(d?.textContent):h;typeof u=="string"&&!i.plugins.has("template-params")&&(u=u.replace("%s",d?.textContent||"")),d?u===null?l.tagMap.delete("title"):l.tagMap.set("title",{...d,textContent:u}):(a.tag="title",a.textContent=u)}}l.tags=Array.from(l.tagMap.values()),c&&(l.tags=l.tags.flat().sort(xo)),await t.callHook("tags:beforeResolve",l),await t.callHook("tags:resolve",l),await t.callHook("tags:afterResolve",l);const f=[];for(const h of l.tags){const{innerHTML:u,tag:w,props:y}=h;if(Uf.has(w)&&!(Object.keys(y).length===0&&!h.innerHTML&&!h.textContent)&&!(w==="meta"&&!y.content&&!y["http-equiv"]&&!y.charset)){if(w==="script"&&u){if(y.type?.endsWith("json")){const M=typeof u=="string"?u:JSON.stringify(u);h.innerHTML=M.replace(/</g,"\\u003C")}else typeof u=="string"&&(h.innerHTML=u.replace(new RegExp(`</${w}`,"g"),`<\\/${w}`));h._d=Kn(h)}f.push(h)}}return f},invalidate(){for(const l of n.values())o.add(l._i);i.dirty=!0,t.callHook("entries:updated",i)}};return(e?.plugins||[]).forEach(l=>To(i,l)),i.hooks.callHook("init",i),e.init?.forEach(l=>l&&i.push(l)),i}const tu=(e,t)=>ue(t)?uc(t):t,vl="usehead";function su(e){return{install(s){s.config.globalProperties.$unhead=e,s.config.globalProperties.$head=e,s.provide(vl,e)}}.install}function nu(){if(hr()){const e=It(vl);if(!e)throw new Error("useHead() was called without provide context, ensure you call it through the setup() function.");return e}throw new Error("useHead() was called without provide context, ensure you call it through the setup() function.")}function e2(e,t={}){const s=t.head||nu();return s.ssr?s.push(e||{},t):ru(s,e,t)}function ru(e,t,s={}){const n=je(!1);let r;return ia(()=>{const i=n.value?{}:qs(t,tu);r?r.patch(i):r=e.push(i,s)}),Kt()&&(dr(()=>{r.dispose()}),ai(()=>{n.value=!0}),ci(()=>{n.value=!1})),r}const ou="modulepreload",iu=function(e,t){return new URL(e,t).href},Ro={},Mo=function(t,s,n){let r=Promise.resolve();if(s&&s.length>0){let d=function(a){return Promise.all(a.map(f=>Promise.resolve(f).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};const i=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),c=l?.nonce||l?.getAttribute("nonce");r=d(s.map(a=>{if(a=iu(a,n),a in Ro)return;Ro[a]=!0;const f=a.endsWith(".css"),h=f?'[rel="stylesheet"]':"";if(!!n)for(let y=i.length-1;y>=0;y--){const M=i[y];if(M.href===a&&(!f||M.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${h}`))return;const w=document.createElement("link");if(w.rel=f?"stylesheet":ou,f||(w.as="script"),w.crossOrigin="",w.href=a,c&&w.setAttribute("nonce",c),document.head.appendChild(w),f)return new Promise((y,M)=>{w.addEventListener("load",y),w.addEventListener("error",()=>M(new Error(`Unable to preload CSS for ${a}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};let As,ks;function lu(){return As=$fetch(vr(`builds/meta/${zt().app.buildId}.json`),{responseType:"json"}),As.then(e=>{ks=wf(e.matcher)}).catch(e=>{console.error("[nuxt] Error fetching app manifest.",e)}),As}function nn(){return As||lu()}async function Cr(e){const t=typeof e=="string"?e:e.path;if(await nn(),!ks)return console.error("[nuxt] Error creating app manifest matcher.",ks),{};try{return vf({},...ks.matchAll(t).reverse())}catch(s){return console.error("[nuxt] Error matching route rules.",s),{}}}async function Eo(e,t={}){if(!await xl(e))return null;const n=await au(e,t);return await _l(n)||null}const cu="_payload.json";async function au(e,t={}){const s=new URL(e,"http://localhost");if(s.host!=="localhost"||ht(s.pathname,{acceptRelative:!0}))throw new Error("Payload URL must not include hostname: "+e);const n=zt(),r=t.hash||(t.fresh?Date.now():n.app.buildId),o=n.app.cdnURL,i=o&&await xl(e)?o:n.app.baseURL;return St(i,s.pathname,cu+(r?`?${r}`:""))}async function _l(e){const t=fetch(e,{cache:"force-cache"}).then(s=>s.text().then(Cl));try{return await t}catch(s){console.warn("[nuxt] Cannot load payload ",e,s)}return null}async function xl(e=gl().path){const t=pe();return e=sn(e),(await nn()).prerendered.includes(e)?!0:t.runWithContext(async()=>{const n=await Cr({path:e});return!!n.prerender&&!n.redirect})}let mt=null;async function fu(){if(mt)return mt;const e=document.getElementById("__NUXT_DATA__");if(!e)return{};const t=await Cl(e.textContent||""),s=e.dataset.src?await _l(e.dataset.src):void 0;return mt={...t,...s,...window.__NUXT__},mt.config?.public&&(mt.config.public=ot(mt.config.public)),mt}async function Cl(e){return await Nf(e,pe()._payloadRevivers)}function uu(e,t){pe()._payloadRevivers[e]=t}const du=[["NuxtError",e=>xr(e)],["EmptyShallowRef",e=>En(e==="_"?void 0:e==="0n"?BigInt(0):Ds(e))],["EmptyRef",e=>je(e==="_"?void 0:e==="0n"?BigInt(0):Ds(e))],["ShallowRef",e=>En(e)],["ShallowReactive",e=>kt(e)],["Ref",e=>je(e)],["Reactive",e=>ot(e)]],hu=ct({name:"nuxt:revive-payload:client",order:-30,async setup(e){let t,s;for(const[n,r]of du)uu(n,r);Object.assign(e.payload,([t,s]=cl(()=>e.runWithContext(fu)),t=await t,s(),t)),delete window.__NUXT__}});async function Sr(e,t={}){const s=t.document||e.resolvedOptions.document;if(!s||!e.dirty)return;const n={shouldRender:!0,tags:[]};if(await e.hooks.callHook("dom:beforeRender",n),!!n.shouldRender)return e._domUpdatePromise||(e._domUpdatePromise=new Promise(async r=>{const o=new Map,i=new Promise(u=>{e.resolveTags().then(w=>{u(w.map(y=>{const M=o.get(y._d)||0,x={tag:y,id:(M?`${y._d}:${M}`:y._d)||_o(y),shouldRender:!0};return y._d&&wl(y._d)&&o.set(y._d,M+1),x}))})});let l=e._dom;if(!l){l={title:s.title,elMap:new Map().set("htmlAttrs",s.documentElement).set("bodyAttrs",s.body)};for(const u of["body","head"]){const w=s[u]?.children;for(const y of w){const M=y.tagName.toLowerCase();if(!vo.has(M))continue;const x=bl({tag:M,props:{}},{innerHTML:y.innerHTML,...y.getAttributeNames().reduce((k,g)=>(k[g]=y.getAttribute(g),k),{})||{}});if(x.key=y.getAttribute("data-hid")||void 0,x._d=Kn(x)||_o(x),l.elMap.has(x._d)){let k=1,g=x._d;for(;l.elMap.has(g);)g=`${x._d}:${k++}`;l.elMap.set(g,y)}else l.elMap.set(x._d,y)}}}l.pendingSideEffects={...l.sideEffects},l.sideEffects={};function c(u,w,y){const M=`${u}:${w}`;l.sideEffects[M]=y,delete l.pendingSideEffects[M]}function d({id:u,$el:w,tag:y}){const M=y.tag.endsWith("Attrs");l.elMap.set(u,w),M||(y.textContent&&y.textContent!==w.textContent&&(w.textContent=y.textContent),y.innerHTML&&y.innerHTML!==w.innerHTML&&(w.innerHTML=y.innerHTML),c(u,"el",()=>{w?.remove(),l.elMap.delete(u)}));for(const x in y.props){if(!Object.prototype.hasOwnProperty.call(y.props,x))continue;const k=y.props[x];if(x.startsWith("on")&&typeof k=="function"){const v=w?.dataset;if(v&&v[`${x}fired`]){const b=x.slice(0,-5);k.call(w,new Event(b.substring(2)))}w.getAttribute(`data-${x}`)!==""&&((y.tag==="bodyAttrs"?s.defaultView:w).addEventListener(x.substring(2),k.bind(w)),w.setAttribute(`data-${x}`,""));continue}const g=`attr:${x}`;if(x==="class"){if(!k)continue;for(const v of k)M&&c(u,`${g}:${v}`,()=>w.classList.remove(v)),!w.classList.contains(v)&&w.classList.add(v)}else if(x==="style"){if(!k)continue;for(const[v,b]of k)c(u,`${g}:${v}`,()=>{w.style.removeProperty(v)}),w.style.setProperty(v,b)}else k!==!1&&k!==null&&(w.getAttribute(x)!==k&&w.setAttribute(x,k===!0?"":String(k)),M&&c(u,g,()=>w.removeAttribute(x)))}}const a=[],f={bodyClose:void 0,bodyOpen:void 0,head:void 0},h=await i;for(const u of h){const{tag:w,shouldRender:y,id:M}=u;if(y){if(w.tag==="title"){s.title=w.textContent,c("title","",()=>s.title=l.title);continue}u.$el=u.$el||l.elMap.get(M),u.$el?d(u):vo.has(w.tag)&&a.push(u)}}for(const u of a){const w=u.tag.tagPosition||"head";u.$el=s.createElement(u.tag.tag),d(u),f[w]=f[w]||s.createDocumentFragment(),f[w].appendChild(u.$el)}for(const u of h)await e.hooks.callHook("dom:renderTag",u,s,c);f.head&&s.head.appendChild(f.head),f.bodyOpen&&s.body.insertBefore(f.bodyOpen,s.body.firstChild),f.bodyClose&&s.body.appendChild(f.bodyClose);for(const u in l.pendingSideEffects)l.pendingSideEffects[u]();e._dom=l,await e.hooks.callHook("dom:rendered",{renders:h}),r()}).finally(()=>{e._domUpdatePromise=void 0,e.dirty=!1})),e._domUpdatePromise}function pu(e={}){const t=e.domOptions?.render||Sr;e.document=e.document||(typeof window<"u"?document:void 0);const s=e.document?.head.querySelector('script[id="unhead:payload"]')?.innerHTML||!1;return eu({...e,plugins:[...e.plugins||[],{key:"client",hooks:{"entries:updated":t}}],init:[s?JSON.parse(s):!1,...e.init||[]]})}function gu(e,t){let s=0;return()=>{const n=++s;t(()=>{s===n&&e()})}}function mu(e={}){const t=pu({domOptions:{render:gu(()=>Sr(t),s=>setTimeout(s,0))},...e});return t.install=su(t),t}const yu={disableDefaults:!0},wu=ct({name:"nuxt:head",enforce:"pre",setup(e){const t=mu(yu);e.vueApp.use(t);{let s=!0;const n=async()=>{s=!1,await Sr(t)};t.hooks.hook("dom:beforeRender",r=>{r.shouldRender=!s}),e.hooks.hook("page:start",()=>{s=!0}),e.hooks.hook("page:finish",()=>{e.isHydrating||n()}),e.hooks.hook("app:error",n),e.hooks.hook("app:suspense:resolve",n)}}}),bu=async e=>{let t,s;const n=([t,s]=cl(()=>Cr({path:e.path})),t=await t,s(),t);if(n.redirect)return ht(n.redirect,{acceptRelative:!0})?(window.location.href=n.redirect,!1):n.redirect},vu=[bu];function Sn(e){const t=e&&typeof e=="object"?e:{};typeof e=="object"&&(e=nl({pathname:e.path||"",search:Xi(e.query||{}),hash:e.hash||""}));const s=new URL(e.toString(),window.location.href);return{path:s.pathname,fullPath:e,query:tn(s.search),hash:s.hash,params:t.params||{},name:void 0,matched:t.matched||[],redirectedFrom:void 0,meta:t.meta||{},href:e}}const _u=ct({name:"nuxt:router",enforce:"pre",setup(e){const t=k1(window.location.pathname,zt().app.baseURL)+window.location.search+window.location.hash,s=[],n={"navigate:before":[],"resolve:before":[],"navigate:after":[],error:[]},r=(f,h)=>(n[f].push(h),()=>n[f].splice(n[f].indexOf(h),1)),o=zt().app.baseURL,i=ot(Sn(t));async function l(f,h){try{const u=Sn(f);for(const w of n["navigate:before"]){const y=await w(u,i);if(y===!1||y instanceof Error)return;if(typeof y=="string"&&y.length)return l(y,!0)}for(const w of n["resolve:before"])await w(u,i);Object.assign(i,u),window.history[h?"replaceState":"pushState"]({},"",St(o,u.fullPath)),e.isHydrating||await e.runWithContext(Mf);for(const w of n["navigate:after"])await w(u,i)}catch(u){for(const w of n.error)await w(u)}}const d={currentRoute:Ie(()=>i),isReady:()=>Promise.resolve(),options:{},install:()=>Promise.resolve(),push:f=>l(f,!1),replace:f=>l(f,!0),back:()=>window.history.go(-1),go:f=>window.history.go(f),forward:()=>window.history.go(1),beforeResolve:f=>r("resolve:before",f),beforeEach:f=>r("navigate:before",f),afterEach:f=>r("navigate:after",f),onError:f=>r("error",f),resolve:Sn,addRoute:(f,h)=>{s.push(h)},getRoutes:()=>s,hasRoute:f=>s.some(h=>h.name===f),removeRoute:f=>{const h=s.findIndex(u=>u.name===f);h!==-1&&s.splice(h,1)}};e.vueApp.component("RouterLink",Gt({functional:!0,props:{to:{type:String,required:!0},custom:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,ariaCurrentValue:String},setup:(f,{slots:h})=>{const u=()=>l(f.to,f.replace);return()=>{const w=d.resolve(f.to);return f.custom?h.default?.({href:f.to,navigate:u,route:w}):us("a",{href:f.to,onClick:y=>(y.preventDefault(),u())},h)}}})),window.addEventListener("popstate",f=>{const h=f.target.location;d.replace(h.href.replace(h.origin,""))}),e._route=i,e._middleware||={global:[],named:{}};const a=e.payload.state._layout;return e.hooks.hookOnce("app:created",async()=>{d.beforeEach(async(f,h)=>{f.meta=ot(f.meta||{}),e.isHydrating&&a&&!it(f.meta.layout)&&(f.meta.layout=a),e._processingMiddleware=!0;{const u=new Set([...vu,...e._middleware.global]);{const w=await e.runWithContext(()=>Cr({path:f.path}));if(w.appMiddleware)for(const y in w.appMiddleware){const M=e._middleware.named[y];if(!M)return;w.appMiddleware[y]?u.add(M):u.delete(M)}}for(const w of u){const y=await e.runWithContext(()=>w(f,h));if(y!==!0&&(y||y===!1))return y}}}),d.afterEach(()=>{delete e._processingMiddleware}),await d.replace(t),L1(i.fullPath,t)||await e.runWithContext(()=>ml(i.fullPath))}),{provide:{route:i,router:d}}}}),Jn=globalThis.requestIdleCallback||(e=>{const t=Date.now(),s={didTimeout:!1,timeRemaining:()=>Math.max(0,50-(Date.now()-t))};return setTimeout(()=>{e(s)},1)}),xu=globalThis.cancelIdleCallback||(e=>{clearTimeout(e)}),rn=e=>{const t=pe();t.isHydrating?t.hooks.hookOnce("app:suspense:resolve",()=>{Jn(()=>e())}):Jn(()=>e())},Cu=ct({name:"nuxt:payload",setup(e){const t=new Set;Qe().beforeResolve(async(s,n)=>{if(s.path===n.path)return;const r=await Eo(s.path);if(r){for(const o of t)delete e.static.data[o];for(const o in r.data)o in e.static.data||t.add(o),e.static.data[o]=r.data[o]}}),rn(()=>{e.hooks.hook("link:prefetch",async s=>{const{hostname:n}=new URL(s,window.location.href);n===window.location.hostname&&await Eo(s).catch(()=>{console.warn("[nuxt] Error preloading payload for",s)})}),navigator.connection?.effectiveType!=="slow-2g"&&setTimeout(nn,1e3)})}}),Su=ct(()=>{const e=Qe();rn(()=>{e.beforeResolve(async()=>{await new Promise(t=>{setTimeout(t,100),requestAnimationFrame(()=>{setTimeout(t,0)})})})})}),Tu=ct(e=>{let t;async function s(){const n=await nn();t&&clearTimeout(t),t=setTimeout(s,go);try{const r=await $fetch(vr("builds/latest.json")+`?${Date.now()}`);r.id!==n.id&&e.hooks.callHook("app:manifest:update",r)}catch{}}rn(()=>{t=setTimeout(s,go)})});function Ru(e={}){const t=e.path||window.location.pathname;let s={};try{s=Ds(sessionStorage.getItem("nuxt:reload")||"{}")}catch{}if(e.force||s?.path!==t||s?.expires<Date.now()){try{sessionStorage.setItem("nuxt:reload",JSON.stringify({path:t,expires:Date.now()+(e.ttl??1e4)}))}catch{}if(e.persistState)try{sessionStorage.setItem("nuxt:reload:state",JSON.stringify({state:pe().payload.state}))}catch{}window.location.pathname!==t?window.location.href=t:window.location.reload()}}const Mu=ct({name:"nuxt:chunk-reload",setup(e){const t=Qe(),s=zt(),n=new Set;t.beforeEach(()=>{n.clear()}),e.hook("app:chunkError",({error:o})=>{n.add(o)});function r(o){const i=St(s.app.baseURL,o.fullPath);Ru({path:i,persistState:!0})}e.hook("app:manifest:update",()=>{t.beforeResolve(r)}),t.onError((o,i)=>{n.has(o)&&r(i)})}}),Eu=ct({name:"nuxt:global-components"}),Au=ct(()=>{}),ku=[hu,wu,_u,Cu,Su,Tu,Mu,Eu,Au],Tr={__name:"NIcon",props:{icon:{type:String,required:!1}},setup(e){return(t,s)=>(U(),Y("div",{class:Ae(["n-icon",e.icon])},null,2))}},Sl=(e,t)=>{const s=e.__vccOpts||e;for(const[n,r]of t)s[n]=r;return s},Ou={},Lu={class:"n-badge"};function Hu(e,t){return U(),Y("span",Lu,[We(e.$slots,"default")])}const Pu=Object.assign(Sl(Ou,[["render",Hu]]),{__name:"NBadge"});async function Tl(e,t=Qe()){const{path:s,matched:n}=t.resolve(e);if(!n.length||(t._routePreloaded||=new Set,t._routePreloaded.has(s)))return;const r=t._preloadPromises||=[];if(r.length>4)return Promise.all(r).then(()=>Tl(e,t));t._routePreloaded.add(s);const o=n.map(i=>i.components?.default).filter(i=>typeof i=="function");for(const i of o){const l=Promise.resolve(i()).catch(()=>{}).finally(()=>r.splice(r.indexOf(l)));r.push(l)}await Promise.all(r)}const Fu=(...e)=>e.find(t=>t!==void 0);function ju(e){const t=e.componentName||"NuxtLink";function s(o){return typeof o=="string"&&o.startsWith("#")}function n(o,i,l){const c=l??e.trailingSlash;if(!o||c!=="append"&&c!=="remove")return o;if(typeof o=="string")return Ts(o,c);const d="path"in o&&o.path!==void 0?o.path:i(o).path;return{...o,name:void 0,path:Ts(d,c)}}function r(o){const i=Qe(),l=zt(),c=Ie(()=>!!o.target&&o.target!=="_self"),d=Ie(()=>{const M=o.to||o.href||"";return typeof M=="string"&&ht(M,{acceptRelative:!0})}),a=kn("RouterLink"),f=a&&typeof a!="string"?a.useLink:void 0,h=Ie(()=>{if(o.external)return!0;const M=o.to||o.href||"";return typeof M=="object"?!1:M===""||d.value}),u=Ie(()=>{const M=o.to||o.href||"";return h.value?M:n(M,i.resolve,o.trailingSlash)}),w=h.value?void 0:f?.({...o,to:u}),y=Ie(()=>{const M=o.trailingSlash??e.trailingSlash;if(!u.value||d.value||s(u.value))return u.value;if(h.value){const x=typeof u.value=="object"&&"path"in u.value?Gn(u.value):u.value,k=typeof x=="object"?i.resolve(x).href:x;return Ts(k,M)}return typeof u.value=="object"?i.resolve(u.value)?.href??null:Ts(St(l.app.baseURL,u.value),M)});return{to:u,hasTarget:c,isAbsoluteUrl:d,isExternal:h,href:y,isActive:w?.isActive??Ie(()=>u.value===i.currentRoute.value.path),isExactActive:w?.isExactActive??Ie(()=>u.value===i.currentRoute.value.path),route:w?.route??Ie(()=>i.resolve(u.value)),async navigate(M){await ml(y.value,{replace:o.replace,external:h.value||c.value})}}}return Gt({name:t,props:{to:{type:[String,Object],default:void 0,required:!1},href:{type:[String,Object],default:void 0,required:!1},target:{type:String,default:void 0,required:!1},rel:{type:String,default:void 0,required:!1},noRel:{type:Boolean,default:void 0,required:!1},prefetch:{type:Boolean,default:void 0,required:!1},prefetchOn:{type:[String,Object],default:void 0,required:!1},noPrefetch:{type:Boolean,default:void 0,required:!1},activeClass:{type:String,default:void 0,required:!1},exactActiveClass:{type:String,default:void 0,required:!1},prefetchedClass:{type:String,default:void 0,required:!1},replace:{type:Boolean,default:void 0,required:!1},ariaCurrentValue:{type:String,default:void 0,required:!1},external:{type:Boolean,default:void 0,required:!1},custom:{type:Boolean,default:void 0,required:!1},trailingSlash:{type:String,default:void 0,required:!1}},useLink:r,setup(o,{slots:i}){const l=Qe(),{to:c,href:d,navigate:a,isExternal:f,hasTarget:h,isAbsoluteUrl:u}=r(o),w=En(!1),y=je(null),M=g=>{y.value=o.custom?g?.$el?.nextElementSibling:g?.$el};function x(g){return!w.value&&(typeof o.prefetchOn=="string"?o.prefetchOn===g:o.prefetchOn?.[g]??e.prefetchOn?.[g])&&(o.prefetch??e.prefetch)!==!1&&o.noPrefetch!==!0&&o.target!=="_blank"&&!Bu()}async function k(g=pe()){if(w.value)return;w.value=!0;const v=typeof c.value=="string"?c.value:f.value?Gn(c.value):l.resolve(c.value).fullPath,b=f.value?new URL(v,window.location.href).href:v;await Promise.all([g.hooks.callHook("link:prefetch",b).catch(()=>{}),!f.value&&!h.value&&Tl(c.value,l).catch(()=>{})])}if(x("visibility")){const g=pe();let v,b=null;ui(()=>{const L=Nu();rn(()=>{v=Jn(()=>{y?.value?.tagName&&(b=L.observe(y.value,async()=>{b?.(),b=null,await k(g)}))})})}),dr(()=>{v&&xu(v),b?.(),b=null})}return()=>{if(!f.value&&!h.value&&!s(c.value)){const b={ref:M,to:c.value,activeClass:o.activeClass||e.activeClass,exactActiveClass:o.exactActiveClass||e.exactActiveClass,replace:o.replace,ariaCurrentValue:o.ariaCurrentValue,custom:o.custom};return o.custom||(x("interaction")&&(b.onPointerenter=k.bind(null,void 0),b.onFocus=k.bind(null,void 0)),w.value&&(b.class=o.prefetchedClass||e.prefetchedClass),b.rel=o.rel||void 0),us(kn("RouterLink"),b,i.default)}const g=o.target||null,v=Fu(o.noRel?"":o.rel,e.externalRelAttribute,u.value||h.value?"noopener noreferrer":"")||null;return o.custom?i.default?i.default({href:d.value,navigate:a,prefetch:k,get route(){if(!d.value)return;const b=new URL(d.value,window.location.href);return{path:b.pathname,fullPath:b.pathname,get query(){return tn(b.search)},hash:b.hash,params:{},name:void 0,matched:[],redirectedFrom:void 0,meta:{},href:d.value}},rel:v,target:g,isExternal:f.value||h.value,isActive:!1,isExactActive:!1}):null:us("a",{ref:y,href:d.value||null,rel:v,target:g,onClick:b=>{if(!(f.value||h.value))return b.preventDefault(),o.replace?l.replace(d.value):l.push(d.value)}},i.default?.())}}})}const Rl=ju(cf);function Ts(e,t){const s=t==="append"?Vs:sn;return ht(e)&&!e.startsWith("http")?e:s(e,!0)}function Nu(){const e=pe();if(e._observer)return e._observer;let t=null;const s=new Map,n=(o,i)=>(t||=new IntersectionObserver(l=>{for(const c of l){const d=s.get(c.target);(c.isIntersecting||c.intersectionRatio>0)&&d&&d()}}),s.set(o,i),t.observe(o),()=>{s.delete(o),t?.unobserve(o),s.size===0&&(t?.disconnect(),t=null)});return e._observer={observe:n}}const Iu=/2g/;function Bu(){const e=navigator.connection;return!!(e&&(e.saveData||Iu.test(e.effectiveType)))}const Du={key:0,"i-carbon:arrow-up-right":"","translate-y--1":"","text-xs":"",op50:""},Uu={__name:"NLink",props:{to:{type:String,required:!1},href:{type:String,required:!1},target:{type:String,required:!1},underline:{type:Boolean,required:!1}},setup(e){const t=e,s=Ie(()=>t.href||t.to);return(n,r)=>{const o=Rl;return U(),Ee(o,Wi(s.value?{href:s.value,target:e.target,rel:e.target==="_blank"?"noopener noreferrer":null}:{},{class:{"n-link n-transition hover:n-link-hover n-link-base":s.value||e.underline}}),{default:ve(()=>[We(n.$slots,"default"),s.value&&e.target==="_blank"?(U(),Y("div",Du)):Me("",!0)]),_:3},16,["class"])}}},Vu=Gt({name:"NButton",props:{to:String,icon:String,border:{type:Boolean,default:!0},disabled:Boolean,type:{type:String,default:"button"}},setup(e,{attrs:t,slots:s}){return()=>us(e.to?Rl:"button",{to:e.to,...t,...!e.to&&{type:e.type},...e.disabled?{disabled:!0}:{tabindex:0},class:[e.border?"n-button-base active:n-button-active focus-visible:n-focus-base hover:n-button-hover":"",s.default?"":"n-icon-button","n-button n-transition n-disabled:n-disabled"].join(" ")},{default:()=>[We(s,"icon",{},()=>e.icon?[us(Tr,{icon:e.icon,class:s.default?"n-button-icon":""})]:[]),We(s,"default")]})}}),Wu={flex:"~ gap-3","items-center":""},zu={__name:"NIconTitle",props:{icon:{type:String,required:!1},text:{type:String,required:!1}},setup(e){return(t,s)=>(U(),Y("div",Wu,[e.icon?(U(),Y("div",{key:0,class:Ae(e.icon)},null,2)):Me("",!0),We(t.$slots,"default",{},()=>[B("div",null,ge(e.text),1)])]))}};typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const qu=e=>typeof e<"u";function Gu(e){return JSON.parse(JSON.stringify(e))}function Ku(e,t,s,n={}){var r,o,i;const{clone:l=!1,passive:c=!1,eventName:d,deep:a=!1,defaultValue:f,shouldEmit:h}=n,u=Kt(),w=u?.emit||((r=u?.$emit)==null?void 0:r.bind(u))||((i=(o=u?.proxy)==null?void 0:o.$emit)==null?void 0:i.bind(u?.proxy));let y=d;y=y||`update:${t.toString()}`;const M=g=>l?typeof l=="function"?l(g):Gu(g):g,x=()=>qu(e[t])?M(e[t]):f,k=g=>{h?h(g)&&w(y,g):w(y,g)};if(c){const g=x(),v=je(g);let b=!1;return ns(()=>e[t],L=>{b||(b=!0,v.value=M(L),lr(()=>b=!1))}),ns(v,L=>{!b&&(L!==e[t]||a)&&k(L)},{deep:a}),v}else return Ie({get(){return x()},set(g){k(g)}})}const Ju=["open"],Zu={"text-base":""},Yu={key:0,"text-sm":"",op50:""},Xu=Gt({__name:"OSectionBlock",props:{icon:{},text:{},description:{},containerClass:{default:""},headerClass:{},collapse:{type:Boolean,default:!0},open:{type:Boolean,default:!0},padding:{type:[Boolean,String],default:!0}},setup(e){const s=Ku(e,"open");function n(r){s.value=r.target.open}return(r,o)=>{const i=Tr,l=zu;return U(),Y(fe,null,[B("details",{open:Z(s),onToggle:n},[B("summary",{class:Ae(["cursor-pointer select-none n-bg-active hover:bg-active px-2 py-2 rounded transition-all",r.collapse?"":"pointer-events-none"])},[X(l,{icon:r.icon,text:r.text,"text-xl":"",transition:"",class:Ae([Z(s)?"op100":"op60",r.headerClass])},{default:ve(()=>[B("div",null,[B("div",Zu,[We(r.$slots,"text",{},()=>[Pe(ge(r.text),1)],!0)]),r.description||r.$slots.description?(U(),Y("div",Yu,[We(r.$slots,"description",{},()=>[Pe(ge(r.description),1)],!0)])):Me("",!0)]),o[0]||(o[0]=B("div",{class:"flex-auto"},null,-1)),We(r.$slots,"actions",{},void 0,!0),r.collapse?(U(),Ee(i,{key:0,icon:"carbon-chevron-down",class:"chevron","cursor-pointer":"","place-self-start":"","text-base":"",op75:"",transition:"","duration-500":""})):Me("",!0)]),_:3},8,["icon","text","class"])],2),B("div",{class:Ae(["flex flex-col flex-gap2 pb6 pt2",typeof r.padding=="string"?r.padding:r.padding?"px4":""])},[We(r.$slots,"details",{},void 0,!0),B("div",{class:Ae([r.containerClass,"mt1"])},[We(r.$slots,"default",{},void 0,!0)],2),We(r.$slots,"footer",{},void 0,!0)],2)],40,Ju),o[1]||(o[1]=B("div",{class:"x-divider"},null,-1))],64)}}}),Qu=Object.assign(Sl(Xu,[["__scopeId","data-v-0d7b3b46"]]),{__name:"OSectionBlock"});let $u;const Qt=[];function e0(e){if(Qt.push(e),!(typeof window>"u"))return window.__NUXT_DEVTOOLS__&&Qt.forEach(t=>t(window.__NUXT_DEVTOOLS__)),Object.defineProperty(window,"__NUXT_DEVTOOLS__",{set(t){t&&Qt.forEach(s=>s(t))},get(){return $u.value},configurable:!0}),()=>{Qt.splice(Qt.indexOf(e),1)}}async function t0(e){return e=e||(t=>Promise.resolve(t)),[{label:"Plausible Analytics",category:"analytics",scriptBundling:t=>{const s=Array.isArray(t?.extension)?t.extension.join("."):[t?.extension];return t?.extension?`https://plausible.io/js/script.${s}.js`:"https://plausible.io/js/script.js"},logo:'<svg height="32" id="Layer_2" viewBox="0 0 46 60" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><linearGradient id="New_Gradient_Swatch_1" x1="14.841" y1="22.544" x2="27.473" y2="44.649" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#909cf7"/><stop offset="1" stop-color="#4b38d8"/></linearGradient><linearGradient id="New_Gradient_Swatch_1-2" x1="7.984" y1="-1.358" x2="21.001" y2="21.422" xlink:href="#New_Gradient_Swatch_1"/><style>.cls-3{stroke-width:0;fill:#1f2961}</style></defs><g id="Plausible_-_Branding"><g id="Gradient_Logo_-_Purple_Gradient_on_White"><g id="Symbol_-_Purple_Gradient"><path d="M45.246 22.603C44.155 33.059 35.013 40.83 24.5 40.83h-4.047v9.57a9.6 9.6 0 0 1-9.6 9.6H3.36A3.36 3.36 0 0 1 0 56.64V36.938l5.038-7.07a3.362 3.362 0 0 1 4.037-1.149l2.866 1.2a3.353 3.353 0 0 0 4.025-1.145l6.717-9.417a3.34 3.34 0 0 1 4.014-1.14l5.52 2.32a3.347 3.347 0 0 0 4.022-1.142l6.46-9.063c2.025 3.56 3.014 7.789 2.547 12.27Z" style="stroke-width:0;fill:url(#New_Gradient_Swatch_1)"/><path d="M3.292 28.873c.823-1.155 2.021-2.044 3.414-2.312a5.41 5.41 0 0 1 3.147.316l2.865 1.2a1.357 1.357 0 0 0 1.62-.464l6.594-9.245c.823-1.154 2.02-2.041 3.412-2.309a5.368 5.368 0 0 1 3.128.314l5.52 2.32a1.35 1.35 0 0 0 1.619-.46l6.919-9.707C37.827 3.364 31.78 0 24.945 0H3.36A3.36 3.36 0 0 0 0 3.36v30.132l3.292-4.62Z" style="fill:url(#New_Gradient_Swatch_1-2);stroke-width:0"/></g></g></g></svg>',import:{name:"useScriptPlausibleAnalytics",from:await e("./runtime/registry/plausible-analytics")}},{label:"Cloudflare Web Analytics",src:"https://static.cloudflareinsights.com/beacon.min.js",category:"analytics",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="70.02" height="32" viewBox="0 0 256 117"><path fill="#FBAD41" d="M205.52 50.813c-.858 0-1.705.03-2.551.058c-.137.007-.272.04-.398.094a1.424 1.424 0 0 0-.92.994l-3.628 12.672c-1.565 5.449-.983 10.48 1.646 14.174c2.41 3.416 6.42 5.421 11.289 5.655l19.679 1.194c.585.03 1.092.312 1.4.776a1.92 1.92 0 0 1 .2 1.692a2.496 2.496 0 0 1-2.134 1.662l-20.448 1.193c-11.11.515-23.062 9.58-27.255 20.633l-1.474 3.9a1.092 1.092 0 0 0 .967 1.49h70.425a1.872 1.872 0 0 0 1.81-1.365A51.172 51.172 0 0 0 256 101.828c0-28.16-22.582-50.984-50.449-50.984"/><path fill="#F6821F" d="m174.782 115.362l1.303-4.583c1.568-5.449.987-10.48-1.639-14.173c-2.418-3.417-6.424-5.422-11.296-5.656l-92.312-1.193a1.822 1.822 0 0 1-1.459-.776a1.919 1.919 0 0 1-.203-1.693a2.496 2.496 0 0 1 2.154-1.662l93.173-1.193c11.063-.511 23.015-9.58 27.208-20.633l5.313-14.04c.214-.596.27-1.238.156-1.86C191.126 20.51 166.91 0 137.96 0C111.269 0 88.626 17.403 80.5 41.596a26.996 26.996 0 0 0-19.156-5.359C48.549 37.524 38.25 47.946 36.979 60.88a27.905 27.905 0 0 0 .702 9.642C16.773 71.145 0 88.454 0 109.726c0 1.923.137 3.818.413 5.667c.115.897.879 1.57 1.783 1.568h170.48a2.223 2.223 0 0 0 2.106-1.63"/></svg>',import:{name:"useScriptCloudflareWebAnalytics",from:await e("./runtime/registry/cloudflare-web-analytics")}},{label:"Fathom Analytics",scriptBundling:!1,category:"analytics",logo:'<svg width="32" height="32" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><circle cx="512" cy="512" r="512" style="fill:#9187ff"/><path d="M558.62 256c-36.31.16-78.78 10-129.45 28.4-170.71 62.15-206.9 139.74-144.79 310.45s139.73 206.89 310.45 144.76S801.74 599.88 739.6 429.16c-43.69-120-95-173.55-181-173.17zm59.1 140.16h26.73a5.33 5.33 0 0 1 5.16 6.72l-59.26 220.48a5.34 5.34 0 0 1-5.15 4h-26.75a5.33 5.33 0 0 1-5.16-6.72l3.6-13.4 2.63-9.75 53-197.38a5.33 5.33 0 0 1 5.14-3.94zM421.79 413.4h10.75a5.33 5.33 0 0 1 5.33 5.33v18a5.33 5.33 0 0 1-5.33 5.33h-9.13a36.76 36.76 0 0 0-5.51.24 4.7 4.7 0 0 0-2.56 1 4.19 4.19 0 0 0-1 1.66 18.91 18.91 0 0 0-.92 6.72v13.67h19.16a5.33 5.33 0 0 1 5.33 5.33v18a5.34 5.34 0 0 1-5.33 5.33h-19.21v108.71a5.34 5.34 0 0 1-5.34 5.34H387a5.33 5.33 0 0 1-5.33-5.34V448.48a36.74 36.74 0 0 1 3.6-16.64 29.76 29.76 0 0 1 9.73-11.16c7.9-5.48 17.62-7.27 26.82-7.31zm82.14 50c16.37 0 30.27 4.65 40.17 13.27s15.47 21.21 15.42 35.59v35.91l-16.11 59.92h-10.24a5.33 5.33 0 0 1-5.33-5.34v-4a39.13 39.13 0 0 1-4.76 3.56c-7.14 4.55-16.85 7.51-29.65 7.51a62.65 62.65 0 0 1-28.52-6.18 40.49 40.49 0 0 1-18.84-19.35 46.81 46.81 0 0 1-4-19.54 40.72 40.72 0 0 1 5.23-21.12 36.78 36.78 0 0 1 13.78-13.18c11.09-6.25 24.75-8.45 38.14-10.24 7.3-1 13.14-1.61 17.64-2.2a42 42 0 0 0 9.2-1.88 3.16 3.16 0 0 0 1.39-.86l.24-.48a6.77 6.77 0 0 0 .16-1.84v-.73a17.24 17.24 0 0 0-5.85-13.6c-3.8-3.31-9.77-5.55-18.07-5.57s-14.64 2.26-19 5.59a17.51 17.51 0 0 0-7.21 12.54 5.33 5.33 0 0 1-5.31 4.86h-22.25a5.33 5.33 0 0 1-5.33-5.57 45.64 45.64 0 0 1 17.6-34c10.47-8.34 24.85-13.12 41.49-13.12zm23.92 80.71c-1.92.48-4 1-6.31 1.45-6.47 1.28-14.29 2.41-21.87 3.48a61 61 0 0 0-14.76 3.65c-4.18 1.75-7.1 4-8.68 6.57a12.12 12.12 0 0 0-1.71 6.54v.2a12.93 12.93 0 0 0 1.32 5.87 11.81 11.81 0 0 0 3.76 4.22c3.41 2.45 9.13 4.14 16.85 4.14 11.95 0 19.52-3.5 24.32-8.32s7-11.56 7.08-19.11v-8.65zm0 0" style="fill:#fff"/></svg>',import:{name:"useScriptFathomAnalytics",from:await e("./runtime/registry/fathom-analytics")}},{label:"Matomo Analytics",scriptBundling:!1,category:"analytics",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="56.5" height="32" viewBox="0 0 256 145"><defs><path id="logosMatomoIcon0" d="m105.426 70.887l.035-.021l-.663-1.01c-.1-.153-.2-.313-.303-.46L58.935 0L0 43.91l43.078 66.305c.185.281.36.566.55.847l.229.35l.025-.016c6.676 9.471 17.678 15.673 30.144 15.673c20.373 0 36.889-16.513 36.889-36.89c0-7.083-2.029-13.675-5.489-19.292"/><path id="logosMatomoIcon1" fill="#000" d="M64.549 19.33c0-20.374-16.517-36.89-36.89-36.89S-9.23-1.044-9.23 19.33a36.686 36.686 0 0 0 6.08 20.263c-.003 0-.003 0-.003-.003l-.019.003L-31.179 0h-.04c-6.499-10.524-18.101-17.56-31.376-17.56c-13.275 0-24.877 7.036-31.376 17.56h-.037l-44.61 69.525c6.633-9.8 17.848-16.235 30.57-16.235c13.39 0 25.077 7.158 31.54 17.832h.047l29.15 40.921h.047c6.718 9.1 17.486 15.026 29.663 15.026c12.181 0 22.95-5.927 29.666-15.026h.05l.297-.46a36.949 36.949 0 0 0 2.116-3.312l43.675-68.256v.003A36.747 36.747 0 0 0 64.55 19.33M2.372 46.141c.213.204.435.397.654.594c-.22-.197-.438-.39-.654-.594m3.28 2.745c.243.181.48.369.728.544c-.247-.175-.485-.363-.729-.544m8.096 4.598c.306.128.628.228.94.347c-.312-.12-.634-.22-.94-.347m8.28 2.263c.428.065.853.143 1.287.197c-.434-.054-.856-.132-1.287-.197m9.93.203c.438-.05.869-.135 1.303-.197c-.434.062-.862.147-1.303.197m8.368-2.01c.393-.144.797-.275 1.184-.434c-.387.159-.788.29-1.185.434m8.368-4.326c.313-.216.61-.456.916-.684c-.307.228-.603.465-.916.684m6.258-5.526c.259-.285.528-.563.778-.857c-.25.294-.519.572-.778.857"/><path id="logosMatomoIcon2" fill="#95C748" d="m250.511 88.448l.035-.022l-.663-1.01c-.1-.153-.2-.312-.303-.46L204.02 17.56l-58.935 43.91l43.078 66.305c.185.281.36.566.55.847l.229.35l.025-.016c6.676 9.471 17.678 15.673 30.144 15.673c20.373 0 36.889-16.513 36.889-36.89c0-7.083-2.029-13.675-5.489-19.291"/><filter id="logosMatomoIcon3" width="106.9%" height="109.7%" x="-3.4%" y="-3.5%" filterUnits="objectBoundingBox"><feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="2"/><feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/></filter></defs><use href="#logosMatomoIcon2"/><path fill="#35BFC0" d="M73.779 107.74c0-20.374-16.516-36.89-36.89-36.89C16.516 70.85 0 87.366 0 107.74c0 20.376 16.516 36.892 36.89 36.892c20.373 0 36.889-16.52 36.889-36.893"/><path fill="#3253A0" d="M172.744 0c20.373 0 36.89 16.516 36.89 36.89a36.747 36.747 0 0 1-6.346 20.688v-.003l-43.675 68.256a36.949 36.949 0 0 1-2.116 3.313l-.297.46h-.05c-6.717 9.098-17.485 15.025-29.666 15.025c-12.177 0-22.945-5.927-29.663-15.026h-.046l-29.15-40.921h-.047C62.114 78.008 50.427 70.85 37.036 70.85c-12.721 0-23.936 6.436-30.569 16.235l44.61-69.525h.037C57.613 7.036 69.215 0 82.49 0c13.275 0 24.877 7.036 31.376 17.56h.04l28.006 39.593l.02-.003c0 .003 0 .003.002.003a36.684 36.684 0 0 1-6.08-20.264C135.855 16.516 152.372 0 172.745 0"/><use href="#logosMatomoIcon2"/><g transform="translate(145.085 17.56)"><mask id="logosMatomoIcon4" fill="#fff"><use href="#logosMatomoIcon0"/></mask><g mask="url(#logosMatomoIcon4)"><use filter="url(#logosMatomoIcon3)" href="#logosMatomoIcon1"/></g></g><path fill="#F38334" d="M209.487 36.89c0-20.374-16.516-36.89-36.89-36.89c-20.373 0-36.89 16.516-36.89 36.89c0 20.373 16.517 36.889 36.89 36.889c20.374 0 36.89-16.516 36.89-36.89"/><path fill="#3152A0" d="M172.597 73.782c-12.887 0-24.214-6.617-30.81-16.629h-.021L113.759 17.56h-.04C107.22 7.04 95.618.003 82.343.003C69.068.003 57.466 7.04 50.967 17.56h-.037L6.323 87.085c6.63-9.796 17.848-16.232 30.566-16.232c13.39 0 25.08 7.155 31.545 17.829h.047l29.15 40.921h.044c6.72 9.096 17.488 15.029 29.665 15.029c12.178 0 22.946-5.93 29.663-15.029h.05l.297-.462a37.588 37.588 0 0 0 2.12-3.307l43.672-68.256c-6.636 9.774-17.839 16.204-30.545 16.204"/></svg>',import:{name:"useScriptMatomoAnalytics",from:await e("./runtime/registry/matomo-analytics")}},{label:"Rybbit Analytics",category:"analytics",logo:"https://www.rybbit.io/rybbit.png",import:{name:"useScriptRybbitAnalytics",from:await e("./runtime/registry/rybbit-analytics")}},{label:"Segment",scriptBundling:t=>St("https://cdn.segment.com/analytics.js/v1",t?.writeKey||"","analytics.min.js"),logo:'<svg xmlns="http://www.w3.org/2000/svg" width="30.92" height="32" viewBox="0 0 256 265"><path fill="#4FB58B" d="m233.56 141.927l.17.013l17.892 1.87a4.927 4.927 0 0 1 3.225 1.707l.133.163l-.17.085a4.93 4.93 0 0 1 1.02 3.74a133.272 133.272 0 0 1-41.604 81.083a128.86 128.86 0 0 1-87.629 34.38a127.488 127.488 0 0 1-46.156-8.57l-.802-.312a4.716 4.716 0 0 1-2.686-2.533l-.077-.187a4.891 4.891 0 0 1-.083-3.66l7.062-17.23a4.846 4.846 0 0 1 6.118-2.799l.163.06c36.097 13.939 76.98 6.089 105.349-20.227a104.455 104.455 0 0 0 32.891-63.32a4.93 4.93 0 0 1 5.013-4.27zm-190.08 64.31l.251-.002l.253.002c8.12.093 14.658 6.659 14.746 14.749v.253c0 .084 0 .168-.002.252c-.141 8.284-6.97 14.886-15.254 14.745c-8.284-.141-14.885-6.97-14.745-15.254c.139-8.115 6.695-14.615 14.75-14.745M4.93 147.082h146.316a4.973 4.973 0 0 1 4.928 4.844l.002.171v18.316a4.974 4.974 0 0 1-4.76 5.01l-.17.005H4.93A4.975 4.975 0 0 1 0 170.584v-18.659a4.975 4.975 0 0 1 4.755-4.838zM169.56 7.311a4.974 4.974 0 0 1 2.848 2.635a5.096 5.096 0 0 1 0 3.867l-6.375 16.999a4.845 4.845 0 0 1-6.162 2.974A101.228 101.228 0 0 0 62.13 51.252a105.267 105.267 0 0 0-34.507 54.99a4.93 4.93 0 0 1-4.76 3.698h-1.105L4.25 105.733a4.886 4.886 0 0 1-3.103-2.295h-.085A4.929 4.929 0 0 1 .51 99.57a133.393 133.393 0 0 1 44.41-70.204C79.739.7 127.019-7.666 169.56 7.311m-64.807 73.434H251.07a4.972 4.972 0 0 1 4.922 4.67l.008.174v18.317a4.973 4.973 0 0 1-4.76 5.01l-.17.005H104.754a4.972 4.972 0 0 1-4.886-4.842l-.002-.173V85.759a4.972 4.972 0 0 1 4.715-5.008zm101.572-55.883l.252-.002l.253.002c8.12.093 14.658 6.659 14.746 14.748v.253c0 .085 0 .17-.002.253c-.14 8.284-6.97 14.885-15.254 14.744c-8.284-.14-14.885-6.97-14.744-15.253c.138-8.116 6.694-14.616 14.749-14.745"/></svg>',category:"tracking",import:{name:"useScriptSegment",from:await e("./runtime/registry/segment")}},{label:"Meta Pixel",src:"https://connect.facebook.net/en_US/fbevents.js",category:"tracking",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="47.91" height="32" viewBox="0 0 256 171"><defs><linearGradient id="logosMetaIcon0" x1="13.878%" x2="89.144%" y1="55.934%" y2="58.694%"><stop offset="0%" stop-color="#0064E1"/><stop offset="40%" stop-color="#0064E1"/><stop offset="83%" stop-color="#0073EE"/><stop offset="100%" stop-color="#0082FB"/></linearGradient><linearGradient id="logosMetaIcon1" x1="54.315%" x2="54.315%" y1="82.782%" y2="39.307%"><stop offset="0%" stop-color="#0082FB"/><stop offset="100%" stop-color="#0064E0"/></linearGradient></defs><path fill="#0081FB" d="M27.651 112.136c0 9.775 2.146 17.28 4.95 21.82c3.677 5.947 9.16 8.466 14.751 8.466c7.211 0 13.808-1.79 26.52-19.372c10.185-14.092 22.186-33.874 30.26-46.275l13.675-21.01c9.499-14.591 20.493-30.811 33.1-41.806C161.196 4.985 172.298 0 183.47 0c18.758 0 36.625 10.87 50.3 31.257C248.735 53.584 256 81.707 256 110.729c0 17.253-3.4 29.93-9.187 39.946c-5.591 9.686-16.488 19.363-34.818 19.363v-27.616c15.695 0 19.612-14.422 19.612-30.927c0-23.52-5.484-49.623-17.564-68.273c-8.574-13.23-19.684-21.313-31.907-21.313c-13.22 0-23.859 9.97-35.815 27.75c-6.356 9.445-12.882 20.956-20.208 33.944l-8.066 14.289c-16.203 28.728-20.307 35.271-28.408 46.07c-14.2 18.91-26.324 26.076-42.287 26.076c-18.935 0-30.91-8.2-38.325-20.556C2.973 139.413 0 126.202 0 111.148z"/><path fill="url(#logosMetaIcon0)" d="M21.802 33.206C34.48 13.666 52.774 0 73.757 0C85.91 0 97.99 3.597 110.605 13.897c13.798 11.261 28.505 29.805 46.853 60.368l6.58 10.967c15.881 26.459 24.917 40.07 30.205 46.49c6.802 8.243 11.565 10.7 17.752 10.7c15.695 0 19.612-14.422 19.612-30.927l24.393-.766c0 17.253-3.4 29.93-9.187 39.946c-5.591 9.686-16.488 19.363-34.818 19.363c-11.395 0-21.49-2.475-32.654-13.007c-8.582-8.083-18.615-22.443-26.334-35.352l-22.96-38.352C118.528 64.08 107.96 49.73 101.845 43.23c-6.578-6.988-15.036-15.428-28.532-15.428c-10.923 0-20.2 7.666-27.963 19.39z"/><path fill="url(#logosMetaIcon1)" d="M73.312 27.802c-10.923 0-20.2 7.666-27.963 19.39c-10.976 16.568-17.698 41.245-17.698 64.944c0 9.775 2.146 17.28 4.95 21.82L9.027 149.482C2.973 139.413 0 126.202 0 111.148C0 83.772 7.514 55.24 21.802 33.206C34.48 13.666 52.774 0 73.757 0z"/></svg>',import:{name:"useScriptMetaPixel",from:await e("./runtime/registry/meta-pixel")}},{label:"X Pixel",src:"https://static.ads-twitter.com/uwt.js",category:"tracking",logo:{dark:'<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 128 128" fill="white" stroke="white"><path d="M75.916 54.2L122.542 0h-11.05L71.008 47.06L38.672 0H1.376l48.898 71.164L1.376 128h11.05L55.18 78.303L89.328 128h37.296L75.913 54.2ZM60.782 71.79l-4.955-7.086l-39.42-56.386h16.972L65.19 53.824l4.954 7.086l41.353 59.15h-16.97L60.782 71.793Z"/></svg>',light:'<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 128 128" fill="black" stroke="black"><path d="M75.916 54.2L122.542 0h-11.05L71.008 47.06L38.672 0H1.376l48.898 71.164L1.376 128h11.05L55.18 78.303L89.328 128h37.296L75.913 54.2ZM60.782 71.79l-4.955-7.086l-39.42-56.386h16.972L65.19 53.824l4.954 7.086l41.353 59.15h-16.97L60.782 71.793Z"/></svg>'},import:{name:"useScriptXPixel",from:await e("./runtime/registry/x-pixel")}},{label:"Snapchat Pixel",src:"https://sc-static.net/scevent.min.js",category:"tracking",logo:'<svg width="50" height="50" viewBox="147.353 39.286 514.631 514.631" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"  xml:space="preserve"><path style="fill:#FFFC00;" d="M147.553,423.021v0.023c0.308,11.424,0.403,22.914,2.33,34.268 c2.042,12.012,4.961,23.725,10.53,34.627c7.529,14.756,17.869,27.217,30.921,37.396c9.371,7.309,19.608,13.111,30.94,16.771 c16.524,5.33,33.571,7.373,50.867,7.473c10.791,0.068,21.575,0.338,32.37,0.293c78.395-0.33,156.792,0.566,235.189-0.484 c10.403-0.141,20.636-1.41,30.846-3.277c19.569-3.582,36.864-11.932,51.661-25.133c17.245-15.381,28.88-34.205,34.132-56.924 c3.437-14.85,4.297-29.916,4.444-45.035v-3.016c0-1.17-0.445-256.892-0.486-260.272c-0.115-9.285-0.799-18.5-2.54-27.636 c-2.117-11.133-5.108-21.981-10.439-32.053c-5.629-10.641-12.68-20.209-21.401-28.57c-13.359-12.81-28.775-21.869-46.722-26.661 c-16.21-4.327-32.747-5.285-49.405-5.27c-0.027-0.004-0.09-0.173-0.094-0.255H278.56c-0.005,0.086-0.008,0.172-0.014,0.255 c-9.454,0.173-18.922,0.102-28.328,1.268c-10.304,1.281-20.509,3.21-30.262,6.812c-15.362,5.682-28.709,14.532-40.11,26.347 c-12.917,13.386-22.022,28.867-26.853,46.894c-4.31,16.084-5.248,32.488-5.271,49.008"/><path style="fill:#FFFFFF;" d="M407.001,473.488c-1.068,0-2.087-0.039-2.862-0.076c-0.615,0.053-1.25,0.076-1.886,0.076 c-22.437,0-37.439-10.607-50.678-19.973c-9.489-6.703-18.438-13.031-28.922-14.775c-5.149-0.854-10.271-1.287-15.22-1.287 c-8.917,0-15.964,1.383-21.109,2.389c-3.166,0.617-5.896,1.148-8.006,1.148c-2.21,0-4.895-0.49-6.014-4.311 c-0.887-3.014-1.523-5.934-2.137-8.746c-1.536-7.027-2.65-11.316-5.281-11.723c-28.141-4.342-44.768-10.738-48.08-18.484 c-0.347-0.814-0.541-1.633-0.584-2.443c-0.129-2.309,1.501-4.334,3.777-4.711c22.348-3.68,42.219-15.492,59.064-35.119 c13.049-15.195,19.457-29.713,20.145-31.316c0.03-0.072,0.065-0.148,0.101-0.217c3.247-6.588,3.893-12.281,1.926-16.916 c-3.626-8.551-15.635-12.361-23.58-14.882c-1.976-0.625-3.845-1.217-5.334-1.808c-7.043-2.782-18.626-8.66-17.083-16.773 c1.124-5.916,8.949-10.036,15.273-10.036c1.756,0,3.312,0.308,4.622,0.923c7.146,3.348,13.575,5.045,19.104,5.045 c6.876,0,10.197-2.618,11-3.362c-0.198-3.668-0.44-7.546-0.674-11.214c0-0.004-0.005-0.048-0.005-0.048 c-1.614-25.675-3.627-57.627,4.546-75.95c24.462-54.847,76.339-59.112,91.651-59.112c0.408,0,6.674-0.062,6.674-0.062 c0.283-0.005,0.59-0.009,0.908-0.009c15.354,0,67.339,4.27,91.816,59.15c8.173,18.335,6.158,50.314,4.539,76.016l-0.076,1.23 c-0.222,3.49-0.427,6.793-0.6,9.995c0.756,0.696,3.795,3.096,9.978,3.339c5.271-0.202,11.328-1.891,17.998-5.014 c2.062-0.968,4.345-1.169,5.895-1.169c2.343,0,4.727,0.456,6.714,1.285l0.106,0.041c5.66,2.009,9.367,6.024,9.447,10.242 c0.071,3.932-2.851,9.809-17.223,15.485c-1.472,0.583-3.35,1.179-5.334,1.808c-7.952,2.524-19.951,6.332-23.577,14.878 c-1.97,4.635-1.322,10.326,1.926,16.912c0.036,0.072,0.067,0.145,0.102,0.221c1,2.344,25.205,57.535,79.209,66.432 c2.275,0.379,3.908,2.406,3.778,4.711c-0.048,0.828-0.248,1.656-0.598,2.465c-3.289,7.703-19.915,14.09-48.064,18.438 c-2.642,0.408-3.755,4.678-5.277,11.668c-0.63,2.887-1.271,5.717-2.146,8.691c-0.819,2.797-2.641,4.164-5.567,4.164h-0.441 c-1.905,0-4.604-0.346-8.008-1.012c-5.95-1.158-12.623-2.236-21.109-2.236c-4.948,0-10.069,0.434-15.224,1.287 c-10.473,1.744-19.421,8.062-28.893,14.758C444.443,462.88,429.436,473.488,407.001,473.488"/><path style="fill:#020202;" d="M408.336,124.235c14.455,0,64.231,3.883,87.688,56.472c7.724,17.317,5.744,48.686,4.156,73.885 c-0.248,3.999-0.494,7.875-0.694,11.576l-0.084,1.591l1.062,1.185c0.429,0.476,4.444,4.672,13.374,5.017l0.144,0.008l0.15-0.003 c5.904-0.225,12.554-2.059,19.776-5.442c1.064-0.498,2.48-0.741,3.978-0.741c1.707,0,3.521,0.321,5.017,0.951l0.226,0.09 c3.787,1.327,6.464,3.829,6.505,6.093c0.022,1.28-0.935,5.891-14.359,11.194c-1.312,0.518-3.039,1.069-5.041,1.7 c-8.736,2.774-21.934,6.96-26.376,17.427c-2.501,5.896-1.816,12.854,2.034,20.678c1.584,3.697,26.52,59.865,82.631,69.111 c-0.011,0.266-0.079,0.557-0.229,0.9c-0.951,2.24-6.996,9.979-44.612,15.783c-5.886,0.902-7.328,7.5-9,15.17 c-0.604,2.746-1.218,5.518-2.062,8.381c-0.258,0.865-0.306,0.914-1.233,0.914c-0.128,0-0.278,0-0.442,0 c-1.668,0-4.2-0.346-7.135-0.922c-5.345-1.041-12.647-2.318-21.982-2.318c-5.21,0-10.577,0.453-15.962,1.352 c-11.511,1.914-20.872,8.535-30.786,15.543c-13.314,9.408-27.075,19.143-48.071,19.143c-0.917,0-1.812-0.031-2.709-0.076 l-0.236-0.01l-0.237,0.018c-0.515,0.045-1.034,0.068-1.564,0.068c-20.993,0-34.76-9.732-48.068-19.143 c-9.916-7.008-19.282-13.629-30.791-15.543c-5.38-0.896-10.752-1.352-15.959-1.352c-9.333,0-16.644,1.428-21.978,2.471 c-2.935,0.574-5.476,1.066-7.139,1.066c-1.362,0-1.388-0.08-1.676-1.064c-0.844-2.865-1.461-5.703-2.062-8.445 c-1.676-7.678-3.119-14.312-9.002-15.215c-37.613-5.809-43.659-13.561-44.613-15.795c-0.149-0.352-0.216-0.652-0.231-0.918 c56.11-9.238,81.041-65.408,82.63-69.119c3.857-7.818,4.541-14.775,2.032-20.678c-4.442-10.461-17.638-14.653-26.368-17.422 c-2.007-0.635-3.735-1.187-5.048-1.705c-11.336-4.479-14.823-8.991-14.305-11.725c0.601-3.153,6.067-6.359,10.837-6.359 c1.072,0,2.012,0.173,2.707,0.498c7.747,3.631,14.819,5.472,21.022,5.472c9.751,0,14.091-4.537,14.557-5.055l1.057-1.182 l-0.085-1.583c-0.197-3.699-0.44-7.574-0.696-11.565c-1.583-25.205-3.563-56.553,4.158-73.871 c23.37-52.396,72.903-56.435,87.525-56.435c0.36,0,6.717-0.065,6.717-0.065C407.744,124.239,408.033,124.235,408.336,124.235 M408.336,115.197h-0.017c-0.333,0-0.646,0-0.944,0.004c-2.376,0.024-6.282,0.062-6.633,0.066c-8.566,0-25.705,1.21-44.115,9.336 c-10.526,4.643-19.994,10.921-28.14,18.66c-9.712,9.221-17.624,20.59-23.512,33.796c-8.623,19.336-6.576,51.905-4.932,78.078 l0.006,0.041c0.176,2.803,0.361,5.73,0.53,8.582c-1.265,0.581-3.316,1.194-6.339,1.194c-4.864,0-10.648-1.555-17.187-4.619 c-1.924-0.896-4.12-1.349-6.543-1.349c-3.893,0-7.997,1.146-11.557,3.239c-4.479,2.63-7.373,6.347-8.159,10.468 c-0.518,2.726-0.493,8.114,5.492,13.578c3.292,3.008,8.128,5.782,14.37,8.249c1.638,0.645,3.582,1.261,5.641,1.914 c7.145,2.271,17.959,5.702,20.779,12.339c1.429,3.365,0.814,7.793-1.823,13.145c-0.069,0.146-0.138,0.289-0.201,0.439 c-0.659,1.539-6.807,15.465-19.418,30.152c-7.166,8.352-15.059,15.332-23.447,20.752c-10.238,6.617-21.316,10.943-32.923,12.855 c-4.558,0.748-7.813,4.809-7.559,9.424c0.078,1.33,0.39,2.656,0.931,3.939c0.004,0.008,0.009,0.016,0.013,0.023 c1.843,4.311,6.116,7.973,13.063,11.203c8.489,3.943,21.185,7.26,37.732,9.855c0.836,1.59,1.704,5.586,2.305,8.322 c0.629,2.908,1.285,5.898,2.22,9.074c1.009,3.441,3.626,7.553,10.349,7.553c2.548,0,5.478-0.574,8.871-1.232 c4.969-0.975,11.764-2.305,20.245-2.305c4.702,0,9.575,0.414,14.48,1.229c9.455,1.574,17.606,7.332,27.037,14 c13.804,9.758,29.429,20.803,53.302,20.803c0.651,0,1.304-0.021,1.949-0.066c0.789,0.037,1.767,0.066,2.799,0.066 c23.88,0,39.501-11.049,53.29-20.799l0.022-0.02c9.433-6.66,17.575-12.41,27.027-13.984c4.903-0.814,9.775-1.229,14.479-1.229 c8.102,0,14.517,1.033,20.245,2.15c3.738,0.736,6.643,1.09,8.872,1.09l0.218,0.004h0.226c4.917,0,8.53-2.699,9.909-7.422 c0.916-3.109,1.57-6.029,2.215-8.986c0.562-2.564,1.46-6.674,2.296-8.281c16.558-2.6,29.249-5.91,37.739-9.852 c6.931-3.215,11.199-6.873,13.053-11.166c0.556-1.287,0.881-2.621,0.954-3.979c0.261-4.607-2.999-8.676-7.56-9.424 c-51.585-8.502-74.824-61.506-75.785-63.758c-0.062-0.148-0.132-0.295-0.205-0.438c-2.637-5.354-3.246-9.777-1.816-13.148 c2.814-6.631,13.621-10.062,20.771-12.332c2.07-0.652,4.021-1.272,5.646-1.914c7.039-2.78,12.07-5.796,15.389-9.221 c3.964-4.083,4.736-7.995,4.688-10.555c-0.121-6.194-4.856-11.698-12.388-14.393c-2.544-1.052-5.445-1.607-8.399-1.607 c-2.011,0-4.989,0.276-7.808,1.592c-6.035,2.824-11.441,4.368-16.082,4.588c-2.468-0.125-4.199-0.66-5.32-1.171 c0.141-2.416,0.297-4.898,0.458-7.486l0.067-1.108c1.653-26.19,3.707-58.784-4.92-78.134c-5.913-13.253-13.853-24.651-23.604-33.892 c-8.178-7.744-17.678-14.021-28.242-18.661C434.052,116.402,416.914,115.197,408.336,115.197"/><rect x="147.553" y="39.443" style="fill:none;" width="514.231" height="514.23"/></svg>',import:{name:"useScriptSnapchatPixel",from:await e("./runtime/registry/snapchat-pixel")}},{label:"Google Adsense",scriptBundling:t=>t?.client?Lt("https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",{client:t?.client}):!1,category:"ads",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="36.09" height="32" viewBox="0 0 256 227"><path fill="#FBBC04" d="M161.8 62.158c11.581-19.822 4.705-45.154-15.355-56.603C126.376-5.878 100.723.899 89.142 20.72c-.51.888-.99 1.794-1.44 2.715L48.553 90.41a49.41 49.41 0 0 0-2.401 4.112L5.495 164.681l72.65 40.721l40.45-69.566a40.013 40.013 0 0 0 2.402-4.112l39.15-66.983a45.769 45.769 0 0 0 1.654-2.583"/><path fill="#34A853" d="M78.483 205.189c-11.515 20.142-37.49 27.553-57.434 15.931c-19.954-11.63-27.036-36.847-15.513-56.982c11.523-20.134 37.267-27.578 57.22-15.956c19.954 11.63 27.241 36.872 15.727 56.998"/><path fill="#4285F4" d="M235.257 75.417c-19.83-11.429-45.17-4.661-56.661 15.134l-41.478 71.67c-11.428 19.755-4.678 45.033 15.076 56.46l.107.062c19.835 11.433 45.18 4.66 56.67-15.142l41.469-71.663c11.426-19.76 4.67-45.042-15.09-56.468z"/></svg>',import:{name:"useScriptGoogleAdsense",from:await e("./runtime/registry/google-adsense")}},{label:"Carbon Ads",scriptBundling:!1,category:"ads",logo:'<svg width="85" height="39" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g fill="none" fill-rule="evenodd"><g fill="#27282F" fill-rule="nonzero"><path d="M2.927 17.98c0 3.248 1.225 4.295 4.933 4.295 1.362 0 3.573-.174 5.31-.488l.305 2.584c-1.633.385-4.015.629-5.717.629C2.212 25 0 22.555 0 18.155V7.887c0-4.401 2.212-6.845 7.759-6.845 1.701 0 4.083.244 5.717.628l-.306 2.585c-1.736-.315-3.948-.49-5.309-.49-3.709 0-4.934 1.049-4.934 4.296v9.92ZM27.988 24.65h-2.433l-.172-1.257C23.91 24.441 21.991 25 19.97 25c-3.118 0-4.42-1.886-4.42-4.89 0-3.528 1.748-4.785 5.106-4.785h4.454V12.67c0-2.13-.925-2.864-3.632-2.864-1.61 0-3.701.28-5.003.56l-.309-2.34c1.576-.42 3.7-.734 5.483-.734 4.763 0 6.34 1.886 6.34 5.518v11.84Zm-2.879-6.88H20.86c-1.782 0-2.433.559-2.433 2.27 0 1.747.309 2.445 2.365 2.445 1.439 0 3.05-.56 4.317-1.397V17.77ZM38.354 10.035c-1.497.784-3.221 1.995-4.523 2.993V25h-2.733V7.648h2.375l.162 2.423a19.862 19.862 0 0 1 4.36-2.78l.359 2.744ZM53.902 18.573c0 3.92-1.415 6.427-7.321 6.427-1.592 0-4.174-.213-6.154-.671V.424L43.397 0v8.298c1.203-.636 3.254-1.2 5.447-1.2 3.572 0 5.058 2.083 5.058 5.367v6.108ZM43.398 22.14c1.167.177 2.334.248 3.395.248 3.501 0 4.138-1.413 4.138-3.708v-6.32c0-1.766-.778-2.649-2.794-2.649-1.521 0-3.572.742-4.74 1.307v11.122ZM61.677 25c-4.833 0-6.738-2.83-6.738-6.392v-4.925c0-3.562 1.905-6.391 6.738-6.391 4.832 0 6.738 2.829 6.738 6.391v4.925c0 3.562-1.906 6.392-6.738 6.392Zm0-15.16c-2.586 0-3.88 1.258-3.88 3.738v5.134c0 2.48 1.293 3.738 3.88 3.738 2.586 0 3.88-1.258 3.88-3.738v-5.134c0-2.48-1.293-3.738-3.88-3.738ZM80.495 7.145c-1.92-.344-4.937.512-6.378 1.225l-.273-1.471h-2.32v17.06h2.866V11.066c1.775-.946 4.2-1.725 5.427-1.692.902.024 1.738.746 1.738 1.552v13.031h2.866V10.94c-.458-3.648-3.927-3.832-3.926-3.795Z"/></g><image opacity=".3" x="31" y="28" width="54" height="10" xlink:href="data:image/png;base64,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"/></g></svg>'},{label:"Intercom",scriptBundling(t){return t?.app_id?St("https://widget.intercom.io/widget",t?.app_id||""):!1},logo:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8.968 8.972"><path d="M7.853 0h-6.73C.496 0-.002.498-.002 1.117v6.73a1.12 1.12 0 0 0 1.126 1.126h6.73c.618 0 1.117-.498 1.117-1.117v-6.73A1.119 1.119 0 0 0 7.853 0zM5.68 1.645c0-.17.13-.3.3-.3s.3.13.3.3v3.998c0 .17-.13.3-.3.3s-.3-.13-.3-.3zm-1.495-.15c0-.17.13-.3.3-.3s.3.13.3.3v4.336c0 .17-.13.3-.3.3s-.3-.13-.3-.3zm-1.495.15c0-.17.13-.3.3-.3s.3.13.3.3v3.998c0 .17-.13.3-.3.3s-.3-.13-.3-.3zm-1.495.598c0-.17.13-.3.3-.3s.3.13.3.3v2.692c0 .17-.13.3-.3.3s-.3-.13-.3-.3zm6.48 4.566c-.05.04-1.156.967-3.2.967s-3.14-.927-3.2-.967a.29.29 0 0 1-.03-.419.29.29 0 0 1 .419-.03c.02 0 1.007.817 2.8.817 1.814 0 2.79-.817 2.79-.827.13-.1.32-.1.42.03a.3.3 0 0 1-.02.429zm.1-1.874c0 .17-.13.3-.3.3s-.3-.13-.3-.3V2.243c0-.17.13-.3.3-.3s.3.13.3.3z" fill="#1f8ded"/></svg>',category:"support",import:{name:"useScriptIntercom",from:await e("./runtime/registry/intercom")}},{label:"Hotjar",scriptBundling(t){return t?.id?Lt(`https://static.hotjar.com/c/hotjar-${t?.id||""}.js`,{sv:t?.sv||"6"}):!1},logo:'<svg xmlns="http://www.w3.org/2000/svg" width="28.45" height="32" viewBox="0 0 256 288"><path fill="#FF3C00" d="M256 100.585c0 53.068-23.654 81.018-49.308 99.403l-4.984 3.443l-5 3.23l-4.979 3.04l-4.925 2.877l-18.623 10.45c-.97.554-1.925 1.106-2.867 1.656l-5.484 3.303c-19.473 12.156-31.858 25.278-32.898 54.98l-.071 4.155H71.752c0-51.355 22.158-79.19 46.838-97.595l4.964-3.56a192.48 192.48 0 0 1 2.496-1.693l5-3.229l4.978-3.04l9.759-5.616l13.787-7.712l5.652-3.305c21.022-12.65 34.51-25.579 35.597-56.632l.071-4.155zM184.252.145c0 51.35-22.153 79.185-46.833 97.591l-4.964 3.56c-.831.574-1.664 1.138-2.497 1.693l-5 3.23l-4.979 3.04l-9.76 5.616l-13.788 7.713l-5.652 3.305c-.914.55-1.814 1.1-2.7 1.653l-5.131 3.351c-16.5 11.328-26.82 24.627-27.766 51.63l-.072 4.155H0c0-54.78 25.206-82.793 51.797-101.152l4.997-3.333l4.994-3.133l4.957-2.956L87.82 64.236l5.652-3.306c21.023-12.65 34.51-25.58 35.597-56.631l.072-4.155z"/></svg>',category:"marketing",import:{name:"useScriptHotjar",from:await e("./runtime/registry/hotjar")}},{label:"Clarity",scriptBundling(t){return t?.id?`https://www.clarity.ms/tag/${t?.id}`:!1},logo:"https://store-images.s-microsoft.com/image/apps.29332.512b1d3d-80ec-4aec-83bb-411008d2f7cd.76371b6f-9386-463f-bfb0-b75cffb86a4f.bd99f4b1-b18e-4380-aa79-93768763c90d.png",category:"marketing",import:{name:"useScriptClarity",from:await e("./runtime/registry/clarity")}},{label:"Stripe",scriptBundling:!1,category:"payments",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="76.57" height="32" viewBox="0 0 512 214"><path fill="#635BFF" d="M512 110.08c0-36.409-17.636-65.138-51.342-65.138c-33.85 0-54.33 28.73-54.33 64.854c0 42.808 24.179 64.426 58.88 64.426c16.925 0 29.725-3.84 39.396-9.244v-28.445c-9.67 4.836-20.764 7.823-34.844 7.823c-13.796 0-26.027-4.836-27.591-21.618h69.547c0-1.85.284-9.245.284-12.658m-70.258-13.511c0-16.071 9.814-22.756 18.774-22.756c8.675 0 17.92 6.685 17.92 22.756zm-90.31-51.627c-13.939 0-22.899 6.542-27.876 11.094l-1.85-8.818h-31.288v165.83l35.555-7.537l.143-40.249c5.12 3.698 12.657 8.96 25.173 8.96c25.458 0 48.64-20.48 48.64-65.564c-.142-41.245-23.609-63.716-48.498-63.716m-8.534 97.991c-8.391 0-13.37-2.986-16.782-6.684l-.143-52.765c3.698-4.124 8.818-6.968 16.925-6.968c12.942 0 21.902 14.506 21.902 33.137c0 19.058-8.818 33.28-21.902 33.28M241.493 36.551l35.698-7.68V0l-35.698 7.538zm0 10.809h35.698v124.444h-35.698zm-38.257 10.524L200.96 47.36h-30.72v124.444h35.556V87.467c8.39-10.951 22.613-8.96 27.022-7.396V47.36c-4.551-1.707-21.191-4.836-29.582 10.524m-71.112-41.386l-34.702 7.395l-.142 113.92c0 21.05 15.787 36.551 36.836 36.551c11.662 0 20.195-2.133 24.888-4.693V140.8c-4.55 1.849-27.022 8.391-27.022-12.658V77.653h27.022V47.36h-27.022zM35.982 83.484c0-5.546 4.551-7.68 12.09-7.68c10.808 0 24.461 3.272 35.27 9.103V51.484c-11.804-4.693-23.466-6.542-35.27-6.542C19.2 44.942 0 60.018 0 85.192c0 39.252 54.044 32.995 54.044 49.92c0 6.541-5.688 8.675-13.653 8.675c-11.804 0-26.88-4.836-38.827-11.378v33.849c13.227 5.689 26.596 8.106 38.827 8.106c29.582 0 49.92-14.648 49.92-40.106c-.142-42.382-54.329-34.845-54.329-50.774"/></svg>',import:{name:"useScriptStripe",from:await e("./runtime/registry/stripe")}},{label:"Lemon Squeezy",src:!1,category:"payments",logo:'<svg width="21" height="28" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="m6.929 17.186 7.511 3.472a3.846 3.846 0 0 1 1.943 1.983c.898 2.099-.33 4.246-2.255 5.018-1.926.772-3.979.275-4.912-1.908l-3.27-7.664c-.253-.595.384-1.178.983-.901ZM7.38 14.938l7.753-2.931c2.577-.974 5.392.869 5.354 3.547l-.003.105c-.055 2.608-2.792 4.36-5.312 3.438l-7.786-2.85a.694.694 0 0 1-.007-1.31ZM6.945 13.922l7.622-3.238C17.1 9.607 17.743 6.377 15.76 4.51a9.026 9.026 0 0 0-.078-.073c-1.945-1.805-5.16-1.17-6.267 1.208l-3.42 7.347c-.274.585.343 1.189.951.93ZM4.983 12.643l2.772-7.599a3.678 3.678 0 0 0-.076-2.732C6.78.214 4.344-.464 2.42.31.493 1.083-.595 2.84.34 5.023l3.29 7.656c.255.593 1.132.57 1.352-.036Z" fill="#FFC233"/></svg>',import:{name:"useScriptLemonSqueezy",from:await e("./runtime/registry/lemon-squeezy")}},{label:"Vimeo Player",category:"content",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="36.74" height="32" viewBox="0 0 256 223"><path fill="#32B8E8" d="M255.876 51.662c-1.139 24.968-18.545 59.157-52.209 102.55c-34.806 45.327-64.254 67.989-88.343 67.989c-14.918 0-27.551-13.799-37.863-41.406c-6.892-25.306-13.775-50.61-20.664-75.915c-7.663-27.592-15.878-41.406-24.661-41.406c-1.915 0-8.617 4.038-20.091 12.081L0 60.008a3257.325 3257.325 0 0 0 37.36-33.38C54.21 12.038 66.86 4.366 75.29 3.59c19.925-1.917 32.187 11.728 36.79 40.938c4.974 31.514 8.415 51.116 10.35 58.788c5.742 26.145 12.06 39.201 18.965 39.201c5.358 0 13.407-8.478 24.138-25.436c10.722-16.963 16.464-29.868 17.24-38.733c1.525-14.638-4.22-21.975-17.24-21.975c-6.128 0-12.447 1.413-18.946 4.206c12.58-41.29 36.618-61.343 72.1-60.199c26.304.773 38.705 17.867 37.19 51.282"/></svg>',import:{name:"useScriptVimeoPlayer",from:await e("./runtime/registry/vimeo-player")}},{label:"YouTube Player",category:"content",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="45.52" height="32" viewBox="0 0 256 180"><path fill="red" d="M250.346 28.075A32.18 32.18 0 0 0 227.69 5.418C207.824 0 127.87 0 127.87 0S47.912.164 28.046 5.582A32.18 32.18 0 0 0 5.39 28.24c-6.009 35.298-8.34 89.084.165 122.97a32.18 32.18 0 0 0 22.656 22.657c19.866 5.418 99.822 5.418 99.822 5.418s79.955 0 99.82-5.418a32.18 32.18 0 0 0 22.657-22.657c6.338-35.348 8.291-89.1-.164-123.134"/><path fill="#FFF" d="m102.421 128.06l66.328-38.418l-66.328-38.418z"/></svg>',import:{name:"useScriptYouTubePlayer",from:await e("./runtime/registry/youtube-player")}},{label:"Google Maps",category:"content",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="22.33" height="32" viewBox="0 0 256 367"><path fill="#34A853" d="M70.585 271.865a370.712 370.712 0 0 1 28.911 42.642c7.374 13.982 10.448 23.463 15.837 40.31c3.305 9.308 6.292 12.086 12.714 12.086c6.998 0 10.173-4.726 12.626-12.035c5.094-15.91 9.091-28.052 15.397-39.525c12.374-22.15 27.75-41.833 42.858-60.75c4.09-5.354 30.534-36.545 42.439-61.156c0 0 14.632-27.035 14.632-64.792c0-35.318-14.43-59.813-14.43-59.813l-41.545 11.126l-25.23 66.451l-6.242 9.163l-1.248 1.66l-1.66 2.078l-2.914 3.319l-4.164 4.163l-22.467 18.304l-56.17 32.432z"/><path fill="#FBBC04" d="M12.612 188.892c13.709 31.313 40.145 58.839 58.031 82.995l95.001-112.534s-13.384 17.504-37.662 17.504c-27.043 0-48.89-21.595-48.89-48.825c0-18.673 11.234-31.501 11.234-31.501l-64.489 17.28z"/><path fill="#4285F4" d="M166.705 5.787c31.552 10.173 58.558 31.53 74.893 63.023l-75.925 90.478s11.234-13.06 11.234-31.617c0-27.864-23.463-48.68-48.81-48.68c-23.969 0-37.735 17.475-37.735 17.475v-57z"/><path fill="#1A73E8" d="M30.015 45.765C48.86 23.218 82.02 0 127.736 0c22.18 0 38.89 5.823 38.89 5.823L90.29 96.516H36.205z"/><path fill="#EA4335" d="M12.612 188.892S0 164.194 0 128.414c0-33.817 13.146-63.377 30.015-82.649l60.318 50.759z"/></svg>',import:{name:"useScriptGoogleMaps",from:await e("./runtime/registry/google-maps")}},{label:"Crisp",category:"support",logo:{light:'<svg height="30" width="35" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><filter id="a" height="138.7%" width="131.4%" x="-15.7%" y="-15.1%"><feMorphology in="SourceAlpha" operator="dilate" radius="1" result="shadowSpreadOuter1"/><feOffset dy="1" in="shadowSpreadOuter1" result="shadowOffsetOuter1"/><feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="1"/><feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"/><feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/></filter><path id="b" d="M14.23 20.46l-9.65 1.1L3 5.12 30.07 2l1.58 16.46-9.37 1.07-3.5 5.72-4.55-4.8z"/></defs><g fill="none" fill-rule="evenodd"><use fill="#000" filter="url(#a)" xlink:href="#b"/><use fill="#1972f5" stroke="#1972f5" stroke-width="2" xlink:href="#b"/></g></svg>',dark:'<svg height="30" width="35" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><filter id="a" height="138.7%" width="131.4%" x="-15.7%" y="-15.1%"><feMorphology in="SourceAlpha" operator="dilate" radius="1" result="shadowSpreadOuter1"/><feOffset dy="1" in="shadowSpreadOuter1" result="shadowOffsetOuter1"/><feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="1"/><feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"/><feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/></filter><path id="b" d="M14.23 20.46l-9.65 1.1L3 5.12 30.07 2l1.58 16.46-9.37 1.07-3.5 5.72-4.55-4.8z"/></defs><g fill="none" fill-rule="evenodd"><use fill="#000" filter="url(#a)" xlink:href="#b"/><use fill="#fff" stroke="#fff" stroke-width="2" xlink:href="#b"/></g></svg>'},import:{name:"useScriptCrisp",from:await e("./runtime/registry/crisp")}},{label:"NPM",scriptBundling(t){return Qi(t?.file||"",`https://unpkg.com/${t?.packageName||""}@${t?.version||"latest"}`)},logo:'<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 256 256"><path fill="#C12127" d="M0 256V0h256v256z"/><path fill="#FFF" d="M48 48h160v160h-32V80h-48v128H48z"/></svg>',category:"utility",import:{name:"useScriptNpm",from:await e("./runtime/registry/npm")}},{label:"Google Tag Manager",category:"tracking",import:{name:"useScriptGoogleTagManager",from:await e("./runtime/registry/google-tag-manager")},logo:'<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 256 256"><path fill="#8AB4F8" d="m150.262 245.516l-44.437-43.331l95.433-97.454l46.007 45.091z"/><path fill="#4285F4" d="M150.45 53.938L106.176 8.731L9.36 104.629c-12.48 12.48-12.48 32.713 0 45.207l95.36 95.986l45.09-42.182l-72.654-76.407z"/><path fill="#8AB4F8" d="m246.625 105.37l-96-96c-12.494-12.494-32.756-12.494-45.25 0c-12.495 12.495-12.495 32.757 0 45.252l96 96c12.494 12.494 32.756 12.494 45.25 0c12.495-12.495 12.495-32.757 0-45.251"/><circle cx="127.265" cy="224.731" r="31.273" fill="#246FDB"/></svg>',scriptBundling(t){return t?.id?Lt("https://www.googletagmanager.com/gtm.js",{id:t.id,l:t.l,gtm_auth:t.auth,gtm_preview:t.preview,gtm_cookies_win:t.cookiesWin?"x":void 0,gtm_debug:t.debug?"x":void 0,gtm_npa:t.npa?"1":void 0,gtm_data_layer:t.dataLayer,gtm_env:t.envName,gtm_auth_referrer_policy:t.authReferrerPolicy}):!1}},{label:"Google Analytics",category:"analytics",import:{name:"useScriptGoogleAnalytics",from:await e("./runtime/registry/google-analytics")},logo:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" width="192px" height="192px" viewBox="0 0 192 192" enable-background="new 0 0 192 192" xml:space="preserve"><rect x="0" y="0" fill="none" width="192" height="192"/><g><g><path fill="#F9AB00" d="M130,29v132c0,14.77,10.19,23,21,23c10,0,21-7,21-23V30c0-13.54-10-22-21-22S130,17.33,130,29z"/></g><g><path fill="#E37400" d="M75,96v65c0,14.77,10.19,23,21,23c10,0,21-7,21-23V97c0-13.54-10-22-21-22S75,84.33,75,96z"/></g><g><circle fill="#E37400" cx="41" cy="163" r="21"/></g></g></svg>',scriptBundling(t){return t?.id?Lt("https://www.googletagmanager.com/gtag/js",{id:t?.id,l:t?.l}):!1}},{label:"Umami Analytics",category:"analytics",logo:'<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="currentColor" d="M2.203 8.611H.857a.845.845 0 0 0-.841.841v.858a13 13 0 0 0-.016.6c0 6.627 5.373 12 12 12c6.527 0 11.837-5.212 11.996-11.701c0-.025.004-.05.004-.075V9.452a.845.845 0 0 0-.841-.841h-1.346c-1.159-4.329-5.112-7.521-9.805-7.521S3.363 4.282 2.203 8.611m18.444 0H3.37c1.127-3.702 4.57-6.399 8.638-6.399c4.069 0 7.512 2.697 8.639 6.399"/></svg>',import:{name:"useScriptUmamiAnalytics",from:await e("./runtime/registry/umami-analytics")}}]}function s0(e){return new Intl.DateTimeFormat("en-US",{hour:"numeric",minute:"numeric",second:"numeric",hour12:!0}).format(e)}function n0(e){return M1(e)?new URL(e).origin:e}function r0(e){return e<1e3?e+"ms":e<6e4?(e/1e3).toFixed(2)+"s":(e/6e4).toFixed(2)+"m"}const o0=je();async function i0(e){const t=await fetch(e,{headers:{"Accept-Encoding":"gzip"}});return c0(await l0(t))}async function l0(e){const t=e.body.getReader(),s=+e.headers.get("Content-Length");if(s)return s;{let n=0;for(;;){const{done:r,value:o}=await t.read();if(r)return n;n+=o.length}}}function c0(e){const t=["Bytes","KB","MB","GB","TB"];if(e===0)return"0 Byte";const s=Math.floor(Math.log(e)/Math.log(1024));return`${Number.parseFloat((e/1024**s).toFixed(2))} ${t[s]}`}const a0={class:"relative n-bg-base flex flex-col"},f0={class:"sticky top-0 z-2 px-4 pt-4 bg-background/80 backdrop-blur-lg"},u0={class:"flex justify-between items-start",mb2:""},d0={class:"flex space-x-5"},h0={"text-xl":"",flex:"","items-center":"","gap-2":""},p0={class:"flex items-center space-x-3 text-xl"},g0={class:"n-select-tabs flex flex-inline flex-wrap items-center border n-border-base rounded-lg n-bg-base"},m0={class:"px-5 py-2"},y0={"text-lg":"",flex:"","items-center":""},w0={class:"px-5 py-2"},b0={"text-lg":"",flex:"","items-center":""},v0={class:"px-5 py-2"},_0={"text-lg":"",flex:"","items-center":""},x0=["value","title"],C0={class:"items-center space-x-3 hidden lg:flex"},S0={class:"opacity-80 text-sm"},T0={class:"flex-row flex h-full",style:{"min-height":"calc(100vh - 64px)"}},R0={class:"mx-auto flex flex-col w-full bg-white dark:bg-black dark:bg-dark-700 bg-light-200"},M0={key:0,class:"h-full relative max-h-full p-4"},E0={key:0},A0={class:"space-y-3"},k0={class:"flex items-center justify-between w-full gap-7"},O0={class:"flex items-center gap-7"},L0={class:"flex items-center gap-1"},H0=["innerHTML"],P0=["src"],F0=["href"],j0={class:"flex flex-items-center gap-3"},N0=["onClick"],I0={class:"capitalize"},B0={class:"capitalize"},D0={key:0},U0={key:1},V0={class:"space-y-2"},W0={class:"opacity-40"},z0={key:0,class:"font-bold px-2 py-[2px] bg-green-50 text-green-700 rounded-lg"},q0={key:1,class:"font-bold px-2 py-[2px] bg-gray-100 text-gray-700 rounded-lg"},G0={key:2,class:"font-bold px-2 py-[2px] bg-red-100 text-red-700 rounded-lg"},K0={key:3,class:"font-bold px-2 py-[2px] bg-yellow-100 text-yellow-700 rounded-lg"},J0={key:1,class:"px-2 py-[2px] bg-gray-100 text-gray-700 rounded-lg font-mono"},Z0={key:2,class:"px-2 py-[2px] bg-gray-100 text-gray-700 rounded-lg font-mono"},Y0={key:1,class:"h-full max-h-full overflow-hidden"},X0=Gt({__name:"app",async setup(e){let t,s;const n=([t,s]=Uc(()=>t0()),t=await t,s(),t),r=je({}),o=ot({});function i(f){r.value=Object.fromEntries(Object.entries({...f}).map(([h,u])=>{if(u.registry=n.find(x=>l(x.label)===u.registryKey),u.registry){const x=u.registry.label.toLowerCase().replace(/ /g,"-");u.docs=`https://scripts.nuxt.com/scripts/${u.registry.category}/${x}`}const w=u.events.find(x=>x.status==="loading")?.at||0,y=u.events.find(x=>x.status==="loaded")?.at||0;w&&y&&(u.loadTime=r0(y-w));const M=u.src;return o[M]||i0(u.src).then(x=>{o[M]=x,u.size=x}).catch(()=>{u.size="",o[M]=""}),[h,u]}))}function l(f){return f.replace(/([\s_-])+/g," ").split(" ").map((h,u)=>u===0?h.toLowerCase():h.charAt(0).toUpperCase()+h.slice(1).toLowerCase()).join("")}const c=je(null);e0(async f=>{o0.value=f.devtools,f.host.nuxt.hooks.hook("scripts:updated",h=>{i(h.scripts)}),c.value=f.host.nuxt.$config.public["nuxt-scripts"].version,i(f.host.nuxt._scripts)});const d=je("scripts");function a(f){d.value="docs",setTimeout(()=>{const h=document.querySelector("iframe");h&&(h.src=f)},100)}return(f,h)=>{const u=Tr,w=Pu,y=kn("VTooltip"),M=Uu,x=Vu,k=Qu;return U(),Y("div",a0,[B("header",f0,[B("div",u0,[B("div",d0,[B("h1",h0,[X(u,{icon:"carbon:script",class:"text-blue-300"}),h[1]||(h[1]=Pe(" Scripts ",-1)),X(w,{class:"text-sm"},{default:ve(()=>[Pe(" v"+ge(Z(c)||""),1)]),_:1})])]),B("div",p0,[B("fieldset",g0,[(U(),Y(fe,null,xs(["scripts","docs"],(g,v)=>B("label",{key:v,class:Ae(["relative n-border-base hover:n-bg-active cursor-pointer",[v?"border-l n-border-base ml--1px":"",g===Z(d)?"n-bg-active":""]])},[g==="scripts"?(U(),Y("div",{key:0,class:Ae([g===Z(d)?"":"op35"])},[X(y,null,{popper:ve(()=>[...h[2]||(h[2]=[Pe(" Scripts ",-1)])]),default:ve(()=>[B("div",m0,[B("h2",y0,[X(u,{icon:"carbon:script opacity-50"})])])]),_:1})],2)):g==="debug"?(U(),Y("div",{key:1,class:Ae([g===Z(d)?"":"op35"])},[X(y,null,{popper:ve(()=>[...h[3]||(h[3]=[Pe(" Debug ",-1)])]),default:ve(()=>[B("div",w0,[B("h2",b0,[X(u,{icon:"carbon:debug opacity-50"})])])]),_:1})],2)):g==="docs"?(U(),Y("div",{key:2,class:Ae([g===Z(d)?"":"op35"])},[X(y,null,{popper:ve(()=>[...h[4]||(h[4]=[Pe(" Documentation ",-1)])]),default:ve(()=>[B("div",v0,[B("h2",_0,[X(u,{icon:"carbon:book opacity-50"})])])]),_:1})],2)):Me("",!0),Cc(B("input",{"onUpdate:modelValue":h[0]||(h[0]=b=>ue(d)?d.value=b:null),type:"radio",value:g,title:g,class:"absolute cursor-pointer pointer-events-none inset-0 op-0.1"},null,8,x0),[[Qa,Z(d)]])],2)),64))])]),B("div",C0,[B("div",S0,[X(M,{href:"https://github.com/nuxt/scripts",target:"_blank"},{default:ve(()=>[X(u,{icon:"logos:github-icon",class:"mr-[2px]"}),h[5]||(h[5]=Pe(" Submit an issue ",-1))]),_:1})]),h[6]||(h[6]=xa('<a href="https://scripts.nuxt.com" target="_blank" class="flex items-end gap-1.5 font-semibold text-xl dark:text-white font-title"><svg height="25" color="text-black dark:text-white" viewBox="0 0 1467 238" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M377 200C379.16 200 381 198.209 381 196V103C381 103 386 112 395 127L434 194C435.785 197.74 439.744 200 443 200H470V50H443C441.202 50 439 51.4941 439 54V148L421 116L385 55C383.248 51.8912 379.479 50 376 50H350V200H377Z" fill="currentColor"></path><path d="M726 92H739C742.314 92 745 89.3137 745 86V60H773V92H800V116H773V159C773 169.5 778.057 174 787 174H800V200H783C759.948 200 745 185.071 745 160V116H726V92Z" fill="currentColor"></path><path d="M591 92V154C591 168.004 585.742 179.809 578 188C570.258 196.191 559.566 200 545 200C530.434 200 518.742 196.191 511 188C503.389 179.809 498 168.004 498 154V92H514C517.412 92 520.769 92.622 523 95C525.231 97.2459 526 98.5652 526 102V154C526 162.059 526.457 167.037 530 171C533.543 174.831 537.914 176 545 176C552.217 176 555.457 174.831 559 171C562.543 167.037 563 162.059 563 154V102C563 98.5652 563.769 96.378 566 94C567.96 91.9107 570.028 91.9599 573 92C573.411 92.0055 574.586 92 575 92H591Z" fill="currentColor"></path><path d="M676 144L710 92H684C680.723 92 677.812 93.1758 676 96L660 120L645 97C643.188 94.1758 639.277 92 636 92H611L645 143L608 200H634C637.25 200 640.182 196.787 642 194L660 167L679 195C680.818 197.787 683.75 200 687 200H713L676 144Z" fill="currentColor"></path><path d="M168 200H279C282.542 200 285.932 198.756 289 197C292.068 195.244 295.23 193.041 297 190C298.77 186.959 300.002 183.51 300 179.999C299.998 176.488 298.773 173.04 297 170.001L222 41C220.23 37.96 218.067 35.7552 215 34C211.933 32.2448 207.542 31 204 31C200.458 31 197.067 32.2448 194 34C190.933 35.7552 188.77 37.96 187 41L168 74L130 9.99764C128.228 6.95784 126.068 3.75491 123 2C119.932 0.245087 116.542 0 113 0C109.458 0 106.068 0.245087 103 2C99.9323 3.75491 96.7717 6.95784 95 9.99764L2 170.001C0.226979 173.04 0.00154312 176.488 1.90993e-06 179.999C-0.0015393 183.51 0.229648 186.959 2 190C3.77035 193.04 6.93245 195.244 10 197C13.0675 198.756 16.4578 200 20 200H90C117.737 200 137.925 187.558 152 164L186 105L204 74L259 168H186L168 200ZM89 168H40L113 42L150 105L125.491 147.725C116.144 163.01 105.488 168 89 168Z" fill="#00DC82"></path><path d="M893.083 200C882.767 200 873.691 198.288 865.856 194.368C858.021 190.319 851.818 184.701 847.248 177.516C842.808 170.2 840.392 161.708 840 152.041H868.207C868.86 159.096 871.341 164.648 875.65 168.698C880.09 172.747 885.901 174.772 893.083 174.772C899.743 174.772 905.032 173.401 908.95 170.657C912.998 167.914 915.022 163.995 915.022 158.9C915.022 155.372 913.847 152.564 911.496 150.473C909.276 148.252 906.468 146.554 903.073 145.378C899.678 144.203 894.846 142.831 888.578 141.263C879.307 139.042 871.667 136.691 865.66 134.209C859.784 131.596 854.691 127.481 850.382 121.863C846.072 116.245 843.918 108.538 843.918 98.7396C843.918 91.2931 845.942 84.6305 849.99 78.7516C854.038 72.8728 859.653 68.3004 866.836 65.0344C874.018 61.6377 882.179 59.9394 891.32 59.9394C901.114 59.9394 909.733 61.7031 917.177 65.2303C924.751 68.7576 930.627 73.7873 934.806 80.3193C939.115 86.8514 941.4 94.4285 941.662 103.051H913.651C913.128 97.5639 910.908 93.2527 906.991 90.1174C903.073 86.8513 897.85 85.2183 891.32 85.2183C885.575 85.2183 881.004 86.4594 877.609 88.9416C874.214 91.4238 872.516 94.6898 872.516 98.7396C872.516 102.528 873.691 105.533 876.042 107.754C878.392 109.975 881.396 111.738 885.052 113.045C888.709 114.221 893.736 115.527 900.135 116.964C909.537 119.185 917.177 121.471 923.053 123.823C928.929 126.174 933.957 130.159 938.136 135.776C942.445 141.263 944.6 148.84 944.6 158.508C944.6 166.999 942.445 174.38 938.136 180.651C933.826 186.922 927.754 191.756 919.919 195.152C912.214 198.549 903.269 200 893.083 200Z" fill="#00DC82"></path><path d="M1005.43 200C995.507 200 986.519 198.026 978.684 193.585C970.98 189.143 964.973 183.068 960.663 175.36C956.485 167.522 954.395 158.834 954.395 149.298C954.395 139.761 956.485 131.138 960.663 123.431C964.973 115.592 970.98 109.452 978.684 105.01C986.519 100.569 995.399 98.3477 1005.32 98.3477C1013.94 98.3477 1021.78 99.9807 1028.83 103.247C1036.01 106.513 1041.76 111.085 1046.07 116.964C1050.51 122.843 1052.99 129.505 1053.51 136.952H1026.09C1025.3 132.51 1023.02 128.852 1019.23 125.978C1015.44 123.104 1011.07 121.667 1006.11 121.667C998.925 121.667 993.245 124.215 989.066 129.31C984.887 134.404 982.798 141.067 982.798 149.298C982.798 157.528 984.887 164.191 989.066 169.285C993.375 174.25 999.186 176.732 1006.5 176.732C1011.46 176.732 1015.77 175.36 1019.43 172.617C1023.08 169.873 1025.43 166.281 1026.48 161.839H1054.29C1053.38 169.024 1050.64 175.556 1046.07 181.435C1041.5 187.314 1035.62 191.952 1028.44 195.348C1021.39 198.614 1013.79 200 1005.43 200Z" fill="#00DC82"></path><path d="M1115.15 122.647C1108.62 122.647 1103.72 125.129 1100.46 130.093C1097.2 135.058 1095.56 141.916 1095.56 150.669V200H1068.14V99.9154H1095.56V116.376C1098.17 110.367 1101.37 106.121 1105.16 103.639C1109.08 101.156 1114.11 99.9154 1120.24 99.9154H1131.41V122.647H1115.15Z" fill="#00DC82"></path><path d="M1171.5 200H1144.08V99.9154H1171.5V200ZM1143.3 85.8062V58H1172.29V85.8062H1143.3Z" fill="#00DC82"></path><path d="M1193.09 238V99.9154H1220.32V110.693C1223.32 106.774 1227.24 103.769 1232.07 101.679C1236.9 99.4582 1242.45 98.3477 1248.72 98.3477C1257.86 98.3477 1265.82 100.438 1272.62 104.618C1279.41 108.799 1284.63 114.743 1288.29 122.451C1292.07 130.159 1293.97 139.108 1293.97 149.298C1293.97 159.487 1291.94 168.436 1287.89 176.144C1283.98 183.721 1278.43 189.665 1271.24 193.977C1264.06 198.157 1255.73 200 1246.33 200C1240.72 200 1235.66 199.333 1231.09 197.504C1226.52 195.675 1222.93 193.127 1220.32 189.861V238H1193.09ZM1243.04 176.732C1250.22 176.732 1256.03 174.25 1260.47 169.285C1264.91 164.191 1267.13 157.528 1267.13 149.298C1267.13 140.937 1264.91 134.274 1260.47 129.31C1256.03 124.215 1250.22 121.667 1243.04 121.667C1235.72 121.667 1229.85 124.215 1225.41 129.31C1220.97 134.274 1218.75 140.937 1218.75 149.298C1218.75 157.528 1220.97 164.191 1225.41 169.285C1229.85 174.25 1235.72 176.732 1243.04 176.732Z" fill="#00DC82"></path><path d="M1319.6 70.7172H1346.83V99.9154H1373.27V122.647H1346.83V163C1346.83 172.406 1351.46 177.109 1360.74 177.109H1373.27V200.037H1357.01C1345.52 200.037 1336.38 196.901 1329.59 190.63C1322.93 184.36 1319.6 175.541 1319.6 164.176V122.647H1300.6V99.9154H1319.6V70.7172Z" fill="#00DC82"></path><path d="M1428.61 200C1416.46 200 1406.6 197.112 1399.03 190.841C1391.59 184.44 1387.6 175.948 1387.08 165.366H1410.59C1411.11 169.808 1412.94 173.335 1416.07 175.948C1419.34 178.43 1423.51 179.671 1428.61 179.671C1432.79 179.671 1436.18 178.757 1438.79 176.928C1441.54 175.099 1442.91 172.813 1442.91 170.069C1442.91 166.411 1441.34 163.864 1438.21 162.427C1435.07 160.99 1430.11 159.683 1423.32 158.508C1416.27 157.201 1410.52 155.764 1406.08 154.197C1401.64 152.629 1397.79 149.82 1394.52 145.77C1391.39 141.59 1389.82 135.711 1389.82 128.134C1389.82 122.386 1391.33 117.291 1394.33 112.849C1397.46 108.276 1401.71 104.749 1407.06 102.267C1412.41 99.6541 1418.42 98.3477 1425.08 98.3477C1436.97 98.3477 1446.56 101.287 1453.88 107.166C1461.32 113.045 1465.3 120.818 1465.82 130.485H1442.12C1441.6 126.435 1439.71 123.3 1436.44 121.079C1433.31 118.728 1429.78 117.552 1425.87 117.552C1421.95 117.552 1418.81 118.401 1416.46 120.099C1414.11 121.798 1412.94 124.149 1412.94 127.154C1412.94 130.812 1414.44 133.294 1417.44 134.6C1420.58 135.776 1425.47 136.821 1432.13 137.736C1439.32 138.781 1445.19 140.087 1449.76 141.655C1454.46 143.092 1458.51 145.966 1461.91 150.277C1465.3 154.588 1467 160.859 1467 169.09C1467 178.496 1463.47 186.073 1456.42 191.821C1449.5 197.439 1440.23 200 1428.61 200Z" fill="#00DC82"></path></svg></a>',1))])])]),B("div",T0,[B("main",R0,[Z(d)==="scripts"?(U(),Y("div",M0,[Object.keys(Z(r)||{}).length?Me("",!0):(U(),Y("div",E0,[...h[7]||(h[7]=[B("div",null,"No scripts loaded.",-1)])])),B("div",A0,[(U(!0),Y(fe,null,xs(Z(r),(g,v)=>(U(),Ee(k,{key:v,class:"w-full"},{text:ve(()=>[B("div",k0,[B("div",O0,[B("div",L0,[g.registry?(U(),Y("div",{key:0,class:"flex items-center max-w-6 h-6",innerHTML:g.registry.logo?.dark||g.registry.logo},null,8,H0)):g.src.startsWith("/")?Me("",!0):(U(),Y("img",{key:1,src:`https://www.google.com/s2/favicons?domain=${Z(n0)(g.src)}`,class:"w-4 h-4 rounded-lg"},null,8,P0)),B("div",null,[B("a",{title:"View script source",class:"text-base hover:bg-gray-800/50 px-2 transition py-1 rounded-xl font-semibold flex gap-2 items-center",target:"_blank",href:g.src},[B("div",null,ge(g.registry?.label||g.key),1)],8,F0),B("div",j0,[g.docs?(U(),Y("button",{key:0,type:"button",class:"ml-2 opacity-50 hover:opacity-70 transition ml-1 text-xs underline",onClick:b=>a(g.docs)}," View docs ",8,N0)):Me("",!0),(U(!0),Y(fe,null,xs(Object.keys(g.registryMeta),b=>(U(),Y("div",{key:b,class:"text-xs text-gray-500"},[B("span",I0,ge(b),1),Pe(": "+ge(g.registryMeta[b]),1)]))),128))])])])]),B("div",null,[h[8]||(h[8]=B("div",{class:"opacity-70 text-xs"}," Status ",-1)),B("div",B0,ge(g.$script.status.value),1)]),Z(o)[g.src]?(U(),Y("div",D0,[h[9]||(h[9]=B("div",{class:"opacity-70 text-xs"}," Size ",-1)),B("div",null,ge(Z(o)[g.src]),1)])):Me("",!0),g.loadTime?(U(),Y("div",U0,[h[10]||(h[10]=B("div",{class:"opacity-70 text-xs"}," Time to loaded ",-1)),B("div",null,ge(g.loadTime),1)])):Me("",!0),B("div",null,[g.status==="awaitingLoad"?(U(),Ee(x,{key:0,onClick:b=>g.load()},{default:ve(()=>[...h[11]||(h[11]=[Pe(" Load ",-1)])]),_:2},1032,["onClick"])):g.status==="loaded"?(U(),Ee(x,{key:1,onClick:b=>g.remove()},{default:ve(()=>[...h[12]||(h[12]=[Pe(" Remove ",-1)])]),_:2},1032,["onClick"])):Me("",!0)])])]),default:ve(()=>[B("div",V0,[(U(!0),Y(fe,null,xs(g.events,(b,L)=>(U(),Y("div",{key:L,class:"flex gap-3 text-xs justify-start items-center"},[B("div",W0,ge(Z(s0)(b.at)),1),b.type==="status"?(U(),Y(fe,{key:0},[b.status==="loaded"?(U(),Y("div",z0,ge(b.status),1)):b.status==="awaitingLoad"?(U(),Y("div",q0,ge(b.status),1)):b.status==="removed"||b.status==="error"?(U(),Y("div",G0,ge(b.status),1)):b.status==="loading"?(U(),Y("div",K0,ge(b.status),1)):Me("",!0)],64)):b.type==="fn-call"&&b.args?(U(),Y("div",J0,ge(`${b.fn}(${b.args?.map(D=>JSON.stringify(D,null,2)).join(", ")||""})`),1)):b.type==="fn-call"&&!b.args?(U(),Y("div",Z0," QUEUED "+ge(b.fn),1)):Me("",!0)]))),128))])]),_:2},1024))),128))])])):Z(d)==="docs"?(U(),Y("div",Y0,[...h[13]||(h[13]=[B("iframe",{src:"https://scripts.nuxt.com/docs/getting-started",class:"w-full h-full border-none",style:{"min-height":"calc(100vh - 100px)"}},null,-1)])])):Me("",!0)])])])}}}),Q0={__name:"nuxt-error-page",props:{error:Object},setup(e){const s=e.error;s.stack&&s.stack.split(`
`).splice(1).map(f=>({text:f.replace("webpack:/","").replace(".vue",".js").trim(),internal:f.includes("node_modules")&&!f.includes(".cache")||f.includes("internal")||f.includes("new Promise")})).map(f=>`<span class="stack${f.internal?" internal":""}">${f.text}</span>`).join(`
`);const n=Number(s.statusCode||500),r=n===404,o=s.statusMessage??(r?"Page Not Found":"Internal Server Error"),i=s.message||s.toString(),l=void 0,a=r?Ir(()=>Mo(()=>import("./1mHV-1pA.js"),__vite__mapDeps([0,1,2]),import.meta.url)):Ir(()=>Mo(()=>import("./CYHtqd0e.js"),__vite__mapDeps([3,1,4]),import.meta.url));return(f,h)=>(U(),Ee(Z(a),Bl(Vi({statusCode:Z(n),statusMessage:Z(o),description:Z(i),stack:Z(l)})),null,16))}},$0={key:0},Ao={__name:"nuxt-root",setup(e){const t=()=>null,s=pe(),n=s.deferHydration();if(s.isHydrating){const d=s.hooks.hookOnce("app:error",n);Qe().beforeEach(d)}const r=!1;_i(pl,gl()),s.hooks.callHookWith(d=>d.map(a=>a()),"vue:setup");const o=_r(),i=!1,l=/bot\b|chrome-lighthouse|facebookexternalhit|google\b/i;hi((d,a,f)=>{if(s.hooks.callHook("vue:error",d,a,f).catch(h=>console.error("[nuxt] Error in `vue:error` hook",h)),l.test(navigator.userAgent))return s.hooks.callHook("app:error",d),console.error(`[nuxt] Not rendering error page for bot with user agent \`${navigator.userAgent}\`:`,d),!1;if(Ef(d)&&(d.fatal||d.unhandled))return s.runWithContext(()=>Rf(d)),!1});const c=!1;return(d,a)=>(U(),Ee(ga,{onResolve:Z(n)},{default:ve(()=>[Z(i)?(U(),Y("div",$0)):Z(o)?(U(),Ee(Z(Q0),{key:1,error:Z(o)},null,8,["error"])):Z(c)?(U(),Ee(Z(t),{key:2,context:Z(c)},null,8,["context"])):Z(r)?(U(),Ee(Bc(Z(r)),{key:3})):(U(),Ee(Z(X0),{key:4}))]),_:1},8,["onResolve"]))}};let ko;{let e;ko=async function(){if(e)return e;const s=!!(window.__NUXT__?.serverRendered??document.getElementById("__NUXT_DATA__")?.dataset.ssr==="true"),n=s?n1(Ao):s1(Ao),r=df({vueApp:n});async function o(i){await r.callHook("app:error",i),r.payload.error||=xr(i)}n.config.errorHandler=o,r.hook("app:suspense:resolve",()=>{n.config.errorHandler===o&&(n.config.errorHandler=void 0)}),!s&&po.id&&r.hook("app:suspense:resolve",()=>{document.getElementById(po.id)?.remove()});try{await gf(r,ku)}catch(i){o(i)}try{await r.hooks.callHook("app:created",n),await r.hooks.callHook("app:beforeMount",n),n.mount(af),await r.hooks.callHook("app:mounted",n),await lr()}catch(i){o(i)}return n},e=ko().catch(t=>{throw console.error("Error while mounting app:",t),t})}export{Sl as _,B as a,X as b,Y as c,Pe as d,Rl as e,pe as f,vl as g,hr as h,It as i,U as o,ge as t,e2 as u,ve as w};
