{"data": [{"gid": "1211327530244676", "actual_time_minutes": null, "assignee": null, "assignee_status": "inbox", "completed": false, "completed_at": null, "created_at": "2025-09-11T15:45:14.153Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": "Option 2", "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [{"project": {"gid": "1211327530244673", "name": "Test", "resource_type": "project"}, "section": {"gid": "1211327530244701", "name": "Section 1", "resource_type": "section"}}], "modified_at": "2025-09-11T15:49:25.291Z", "name": "Single Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": null, "permalink_url": "https://app.asana.com/1/1211283429810937/project/1211327530244673/task/1211327530244676", "projects": [{"gid": "1211327530244673", "name": "Test", "resource_type": "project"}], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244678", "actual_time_minutes": null, "assignee": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "assignee_status": "inbox", "assignee_section": {"gid": "1211283430236192", "name": "Recently assigned", "resource_type": "section"}, "completed": true, "completed_at": "2025-09-11T15:47:08.454Z", "created_at": "2025-09-11T15:45:27.131Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": {"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": "Option 1", "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [{"project": {"gid": "1211327530244673", "name": "Test", "resource_type": "project"}, "section": {"gid": "1211327530244701", "name": "Section 1", "resource_type": "section"}}], "modified_at": "2025-09-11T15:49:25.094Z", "name": "Cleared Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": null, "permalink_url": "https://app.asana.com/1/1211283429810937/project/1211327530244673/task/1211327530244678", "projects": [{"gid": "1211327530244673", "name": "Test", "resource_type": "project"}], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244680", "actual_time_minutes": null, "assignee": null, "assignee_status": "upcoming", "completed": false, "completed_at": null, "created_at": "2025-09-11T15:45:37.659Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": "Option 2", "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [{"project": {"gid": "1211327530244673", "name": "Test", "resource_type": "project"}, "section": {"gid": "1211327530244702", "name": "Section 2", "resource_type": "section"}}], "modified_at": "2025-09-11T15:48:50.079Z", "name": "Parent Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": null, "permalink_url": "https://app.asana.com/1/1211283429810937/project/1211327530244673/task/1211327530244680", "projects": [{"gid": "1211327530244673", "name": "Test", "resource_type": "project"}], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [{"gid": "1211327530244690", "actual_time_minutes": null, "assignee": null, "assignee_status": "upcoming", "completed": false, "completed_at": null, "created_at": "2025-09-11T15:46:38.518Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": null, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": null, "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [], "modified_at": "2025-09-11T15:46:53.017Z", "name": "Child Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": {"gid": "1211327530244680", "name": "Parent Task", "resource_type": "task", "resource_subtype": "default_task"}, "permalink_url": "https://app.asana.com/1/1211283429810937/task/1211327530244690", "projects": [], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244691", "actual_time_minutes": null, "assignee": null, "assignee_status": "inbox", "completed": true, "completed_at": "2025-09-11T15:47:07.379Z", "created_at": "2025-09-11T15:46:53.073Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": {"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": "Option 1", "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [], "modified_at": "2025-09-11T15:49:18.334Z", "name": "Cleared Child Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": {"gid": "1211327530244680", "name": "Parent Task", "resource_type": "task", "resource_subtype": "default_task"}, "permalink_url": "https://app.asana.com/1/1211283429810937/task/1211327530244691", "projects": [], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244693", "actual_time_minutes": null, "assignee": null, "assignee_status": "upcoming", "completed": false, "completed_at": null, "created_at": "2025-09-11T15:47:17.701Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": "Option 2", "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [], "modified_at": "2025-09-11T15:49:31.881Z", "name": "Parent Child Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": {"gid": "1211327530244680", "name": "Parent Task", "resource_type": "task", "resource_subtype": "default_task"}, "permalink_url": "https://app.asana.com/1/1211283429810937/task/1211327530244693", "projects": [], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [{"gid": "1211327530244694", "actual_time_minutes": null, "assignee": null, "assignee_status": "upcoming", "completed": false, "completed_at": null, "created_at": "2025-09-11T15:47:37.672Z", "custom_fields": [], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [], "modified_at": "2025-09-11T15:47:42.677Z", "name": "Grandchild Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": {"gid": "1211327530244693", "name": "Parent Child Task", "resource_type": "task", "resource_subtype": "default_task"}, "permalink_url": "https://app.asana.com/1/1211283429810937/task/1211327530244694", "projects": [], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244695", "actual_time_minutes": null, "assignee": null, "assignee_status": "upcoming", "completed": true, "completed_at": "2025-09-11T15:47:51.219Z", "created_at": "2025-09-11T15:47:42.735Z", "custom_fields": [], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [], "modified_at": "2025-09-11T15:47:51.341Z", "name": "Cleared Grandchild Task", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": {"gid": "1211327530244693", "name": "Parent Child Task", "resource_type": "task", "resource_subtype": "default_task"}, "permalink_url": "https://app.asana.com/1/1211283429810937/task/1211327530244695", "projects": [], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}], "tags": [], "resource_subtype": "default_task", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244696", "actual_time_minutes": null, "assignee": null, "assignee_status": "inbox", "completed": false, "completed_at": null, "created_at": "2025-09-11T15:47:51.600Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": null, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": null, "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [{"project": {"gid": "1211327530244673", "name": "Test", "resource_type": "project"}, "section": {"gid": "1211327530244702", "name": "Section 2", "resource_type": "section"}}], "modified_at": "2025-09-11T15:49:22.232Z", "name": "Milestone", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": null, "permalink_url": "https://app.asana.com/1/1211283429810937/project/1211327530244673/task/1211327530244696", "projects": [{"gid": "1211327530244673", "name": "Test", "resource_type": "project"}], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "milestone", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}, {"gid": "1211327530244698", "actual_time_minutes": null, "assignee": null, "assignee_status": "upcoming", "completed": true, "completed_at": "2025-09-11T15:48:21.303Z", "created_at": "2025-09-11T15:48:03.392Z", "custom_fields": [{"gid": "1211327530244683", "enabled": true, "enum_options": [{"gid": "1211327530244684", "color": "green", "enabled": true, "name": "Option 1", "resource_type": "enum_option"}, {"gid": "1211327530244685", "color": "red", "enabled": true, "name": "Option 2", "resource_type": "enum_option"}], "enum_value": null, "name": "Status", "description": "", "created_by": {"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}, "display_value": null, "resource_subtype": "enum", "resource_type": "custom_field", "is_formula_field": false, "is_value_read_only": false, "type": "enum"}], "due_at": null, "due_on": null, "followers": [{"gid": "1211283429810925", "name": "Undervolta", "resource_type": "user"}], "hearted": false, "hearts": [], "liked": false, "likes": [], "memberships": [{"project": {"gid": "1211327530244673", "name": "Test", "resource_type": "project"}, "section": {"gid": "1211327530244702", "name": "Section 2", "resource_type": "section"}}], "modified_at": "2025-09-11T15:48:54.633Z", "name": "Cleared Milestone", "notes": "", "num_hearts": 0, "num_likes": 0, "parent": null, "permalink_url": "https://app.asana.com/1/1211283429810937/project/1211327530244673/task/1211327530244698", "projects": [{"gid": "1211327530244673", "name": "Test", "resource_type": "project"}], "resource_type": "task", "start_at": null, "start_on": null, "subtasks": [], "tags": [], "resource_subtype": "milestone", "workspace": {"gid": "1211283429810937", "name": "My workspace", "resource_type": "workspace"}}]}