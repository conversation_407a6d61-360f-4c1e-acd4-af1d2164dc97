export default defineI18nLocale(async locale => {
	return {
		lang: {
			name: 'English',
			en: 'English',
			id: 'Indonesian'
		},
		menu: {
			overview: {
				name: 'Overview',
				features: 'Features'
			},
			dev: {
				name: 'Development',
				progress: 'Progress',
				bugReport: 'Bug Report',
				featureReq: 'Feature Request',
				patchNote: 'Patch Notes',
				blog: 'Blog'
			},
			download: 'Download',
			support: 'Support'
		},
		footer: {
			slogan: 'Infinite Ways to Create. Infinite Ways to Play.',
			account: 'Official Accounts',
			contact1: 'Need Something?',
			contact2: 'Contact'
		},
		home: {
			title: 'Home',
			desc: 'v2.0 Major Update in Progress',
			subtle: 'A massive rework of the game, including new features and balance changes.',
			card_1: {
				title: 'Card 1',
				desc: 'A card component has a figure, a body part, and inside body there are title and actions parts',
				btnText: 'Button 1'
			},
			card_2: {
				title: 'Card 2',
				desc: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl nec ultricies ultricies, nunc nisl ultricies nunc, nec ultricies nisl nisl sit amet nunc.',
				btnText: 'Button 2'
			},
			card_3: {
				title: 'Card 3',
				desc: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl nec ultricies ultricies, nunc nisl ultricies nunc, nec ultricies nisl nisl sit amet nunc.',
				btnText: 'Button 3'
			}
		},
		action: {
			registerLabel: 'Your Email',
			register: 'Register',
			checkCbt: 'Interested in Closed Beta Test'
		}
	}
});
