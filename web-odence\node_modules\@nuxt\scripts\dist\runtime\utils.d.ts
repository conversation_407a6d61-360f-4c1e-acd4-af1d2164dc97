import type { ObjectSchema } from 'valibot';
import type { UseScriptInput } from '@unhead/vue';
import type { EmptyOptionsSchema, InferIfSchema, NuxtUseScriptOptions, RegistryScriptInput, UseFunctionType, ScriptRegistry, UseScriptContext } from '#nuxt-scripts/types';
export type MaybePromise<T> = Promise<T> | T;
type OptionsFn<O> = (options: InferIfSchema<O>) => ({
    scriptInput?: UseScriptInput;
    scriptOptions?: NuxtUseScriptOptions;
    schema?: O extends ObjectSchema<any, any> ? O : undefined;
    clientInit?: () => void;
});
export declare function scriptRuntimeConfig<T extends keyof ScriptRegistry>(key: T): ScriptRegistry[T];
export declare function useRegistryScript<T extends Record<string | symbol, any>, O = EmptyOptionsSchema>(registryKey: keyof ScriptRegistry | string, optionsFn: OptionsFn<O>, _userOptions?: RegistryScriptInput<O>): UseScriptContext<UseFunctionType<NuxtUseScriptOptions<T>, T>>;
export declare function pick(obj: Record<string, any>, keys: string[]): Record<string, any>;
export {};
