import { useRuntimeConfig } from "#internal/nitro";
const endpoint = "https://challenges.cloudflare.com/turnstile/v0/siteverify";
export const verifyTurnstileToken = async (token, event, signal) => {
  const secretKey = useRuntimeConfig(event).turnstile.secretKey;
  return await $fetch(endpoint, {
    method: "POST",
    body: `secret=${encodeURIComponent(secretKey)}&response=${encodeURIComponent(token)}`,
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    },
    signal
  });
};
