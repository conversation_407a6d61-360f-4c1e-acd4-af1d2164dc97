{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../app/*"], "@/*": ["../app/*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "vue-i18n": ["../node_modules/vue-i18n"], "@intlify/shared": ["../node_modules/@intlify/shared"], "@intlify/message-compiler": ["../node_modules/@intlify/message-compiler"], "@intlify/core-base": ["../node_modules/@intlify/core-base"], "@intlify/core": ["../node_modules/@intlify/core"], "@intlify/utils/h3": ["../node_modules/@intlify/utils"], "ufo": ["../node_modules/ufo"], "@types/cloudflare-turnstile": ["../node_modules/@types/cloudflare-turnstile"], "#shared": ["../shared"], "#shared/*": ["../shared/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#image": ["../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/@nuxt/image/dist/runtime/*"], "#color-mode-options": ["./color-mode-options.mjs"], "#i18n": ["../node_modules/@nuxtjs/i18n/dist/runtime/composables/index-server"], "#i18n-kit": ["../node_modules/@nuxtjs/i18n/dist/runtime/kit"], "#i18n-kit/*": ["../node_modules/@nuxtjs/i18n/dist/runtime/kit/*"], "#internal-i18n-types": ["../node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-scripts-validator": ["../node_modules/@nuxt/scripts/dist/runtime/validation/valibot"], "#nuxt-scripts": ["../node_modules/@nuxt/scripts/dist/runtime"], "#nuxt-scripts/*": ["../node_modules/@nuxt/scripts/dist/runtime/*"], "vee-validate": ["../node_modules/vee-validate/dist/vee-validate"], "@vee-validate/yup": ["../node_modules/@vee-validate/yup/dist/vee-validate-yup"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/runtime/server", "../node_modules/dist/runtime/server", "../shared/**/*.d.ts", "./types/nitro.d.ts", "../server/**/*"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxt/image/node_modules", "../node_modules/@nuxt/eslint/node_modules", "../node_modules/@nuxt/fonts/node_modules", "../node_modules/@nuxtjs/color-mode/node_modules", "../node_modules/@nuxtjs/i18n/node_modules", "../node_modules/@nuxt/scripts/node_modules", "../node_modules/@nuxtjs/turnstile/node_modules", "../node_modules/@vee-validate/nuxt/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist"]}