// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
	compatibilityDate: '2025-07-15',
	devtools: { enabled: true },
	modules: [
		'@nuxt/image',
		'@nuxt/eslint',
		'@nuxt/fonts',
		'@nuxtjs/color-mode',
		'@nuxtjs/i18n',
		'@nuxtjs/turnstile',
		'@vee-validate/nuxt',
	],
	vite: {
		plugins: [
			tailwindcss(),
		],
	},
	css: [ '~/assets/css/main.css' ],
	i18n: {
    	defaultLocale: 'en',
		locales: [
			{code: 'en', file: {path: 'en.ts'}},
			{code: 'id', file: {path: 'id.ts'}}
		]
	},
	colorMode: {
		preference: 'system',
		fallback: 'dark',
		hid: 'nuxt-color-mode-script',
		globalName: '__NUXT_COLOR_MODE__',
		componentName: 'ColorScheme',
		classPrefix: '',
		classSuffix: '',
		storage: 'localStorage', 
		storageKey: 'nuxt-color-mode'
	},
	image: {
		providers: {
			imagekit: {
				name: 'imagekit',
				provider: '~/providers/imagekit.ts',
				options: {
					baseUrl: 'https://ik.imagekit.io/undervolta/',
				}
			}
		},
		provider: 'imagekit',
		quality: 90,
		formats: ['avif','webp'],
		domains: ['img.youtube.com','i.vimeocdn.com','ik.imagekit.io'],
		alias: {
			youtube: 'https://img.youtube.com',
			vimeo:   'https://i.vimeocdn.com',
			imagekit: 'https://ik.imagekit.io/undervolta',
		}
	},
	fonts: {
		families: [
			{
				name: 'Plus Jakarta Sans',
				provider: 'local',
				weights: [400, 500],
				global: true,
				src: '~/assets/fonts/PlusJakartaSans.ttf'
			}
		]
	},
	veeValidate: {
		autoImports: true,
		componentNames: {
			Form: 'VeeForm',
			Field: 'VeeField',
			FieldArray: 'VeeFieldArray',
			ErrorMessage: 'VeeErrorMessage',
		},
	},
	turnstile: {
		siteKey: process.env.NUXT_PUBLIC_TURNSTILE_SITE_KEY
	},
	runtimeConfig: {
		turnstile: {
			secretKey: process.env.NUXT_TURNSTILE_SECRET_KEY
		}
	}
})
