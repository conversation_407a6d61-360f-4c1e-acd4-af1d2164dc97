/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxt/image" />
/// <reference types="@nuxt/fonts" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@nuxtjs/turnstile" />
/// <reference types="@vee-validate/nuxt" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference path="types/builder-env.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="types/runtime-config.d.ts" />
/// <reference types="nuxt/app" />
/// <reference path="types/i18n-plugin.d.ts" />
/// <reference types="@types/cloudflare-turnstile" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="types/components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="module/nuxt-scripts.d.ts" />
/// <reference path="types/nitro.d.ts" />
/// <reference path="./eslint-typegen.d.ts" />

export {}
