import type { Stripe } from '@stripe/stripe-js';
import type { RegistryScriptInput } from '#nuxt-scripts/types';
export declare const StripeOptions: import("valibot").ObjectSchema<{
    readonly advancedFraudSignals: import("valibot").OptionalSchema<import("valibot").BooleanSchema<undefined>, undefined>;
}, undefined>;
export type StripeInput = RegistryScriptInput<typeof StripeOptions, false>;
export interface StripeApi {
    Stripe: Stripe;
}
export declare function useScriptStripe<T extends StripeApi>(_options?: StripeInput): import("#nuxt-scripts/types").UseScriptContext<T>;
