<script setup>
import { withQuery } from "ufo";
import { computed, onBeforeUnmount, onMounted, ref } from "vue";
import { useScriptTriggerElement } from "../composables/useScriptTriggerElement";
const props = defineProps({
  serve: { type: String, required: true },
  placement: { type: String, required: true },
  format: { type: String, required: true },
  trigger: { type: [String, Array, Boolean], required: false }
});
const emit = defineEmits(["error", "ready"]);
const attrId = `_carbonads_js`;
const carbonadsEl = ref(import.meta.client ? document.getElementById(attrId) : null);
const status = ref("awaitingLoad");
function loadCarbon() {
  if (!carbonadsEl.value) {
    return;
  }
  status.value = "loading";
  const script = document.createElement("script");
  script.setAttribute("src", withQuery("https://cdn.carbonads.com/carbon.js", {
    serve: props.serve,
    placement: props.placement,
    format: props.format
  }));
  script.setAttribute("id", attrId);
  script.onerror = (err) => {
    if (status.value !== "error") {
      status.value = "error";
      emit("error", err);
    }
  };
  script.onload = () => {
    if (status.value !== "loaded") {
      status.value = "loaded";
      emit("ready", script);
    }
  };
  carbonadsEl.value.appendChild(script);
}
const trigger = useScriptTriggerElement({ trigger: props.trigger, el: carbonadsEl });
onMounted(() => {
  if (trigger === "onNuxtReady") {
    loadCarbon();
  } else {
    trigger.then(loadCarbon);
  }
});
const rootAttrs = computed(() => {
  return {
    ...trigger instanceof Promise ? trigger.ssrAttrs || {} : {}
  };
});
onBeforeUnmount(() => {
  if (carbonadsEl.value) {
    carbonadsEl.value.remove();
  }
});
</script>

<template>
  <div ref="carbonadsEl" v-bind="rootAttrs">
    <slot v-if="status === 'awaitingLoad'" name="awaitingLoad" />
    <slot v-else-if="status === 'loading'" name="loading" />
    <slot v-else-if="status === 'error'" name="error" />
  </div>
</template>
