import type { HTMLAttributes, ImgHTMLAttributes, Ref } from 'vue';
import type { ElementScriptTrigger } from '../types.js';
export type YoutubeThumbnailSize = '1' | '2' | '3' | 'default' | 'mq1' | 'mq2' | 'mq3' | 'mqdefault' | '0' | 'hq1' | 'hq2' | 'hq3' | 'hqdefault' | 'sd1' | 'sd2' | 'sd3' | 'sddefault' | 'hq720' | 'maxresdefault';
type __VLS_Props = {
    placeholderAttrs?: ImgHTMLAttributes;
    rootAttrs?: HTMLAttributes;
    aboveTheFold?: boolean;
    trigger?: ElementScriptTrigger;
    videoId: string;
    playerVars?: YT.PlayerVars;
    width?: number;
    height?: number;
    /**
     * Whether to use youtube-nocookie.com for embedding.
     *
     * @default false
     */
    cookies?: boolean;
    playerOptions?: YT.PlayerOptions;
    thumbnailSize?: YoutubeThumbnailSize;
    webp?: boolean;
};
declare var __VLS_1: {
    placeholder: any;
}, __VLS_3: {}, __VLS_9: {}, __VLS_11: {}, __VLS_13: {};
type __VLS_Slots = {} & {
    placeholder?: (props: typeof __VLS_1) => any;
} & {
    loading?: (props: typeof __VLS_3) => any;
} & {
    awaitingLoad?: (props: typeof __VLS_9) => any;
} & {
    error?: (props: typeof __VLS_11) => any;
} & {
    default?: (props: typeof __VLS_13) => any;
};
declare const __VLS_component: import("vue").DefineComponent<__VLS_Props, {
    player: Ref<YT.Player | undefined, YT.Player | undefined>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    error: (e: YT.OnErrorEvent, target: YT.Player) => any;
    ready: (e: YT.PlayerEvent) => any;
    "state-change": (e: YT.OnStateChangeEvent, target: YT.Player) => any;
    "playback-quality-change": (e: YT.OnPlaybackQualityChangeEvent, target: YT.Player) => any;
    "playback-rate-change": (e: YT.OnPlaybackRateChangeEvent, target: YT.Player) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    onError?: ((e: YT.OnErrorEvent, target: YT.Player) => any) | undefined;
    onReady?: ((e: YT.PlayerEvent) => any) | undefined;
    "onState-change"?: ((e: YT.OnStateChangeEvent, target: YT.Player) => any) | undefined;
    "onPlayback-quality-change"?: ((e: YT.OnPlaybackQualityChangeEvent, target: YT.Player) => any) | undefined;
    "onPlayback-rate-change"?: ((e: YT.OnPlaybackRateChangeEvent, target: YT.Player) => any) | undefined;
}>, {
    trigger: ElementScriptTrigger;
    width: number;
    height: number;
    playerVars: YT.PlayerVars;
    cookies: boolean;
    thumbnailSize: YoutubeThumbnailSize;
    webp: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
