{"version": 3, "sources": ["../../../../@intlify/core/dist/core.node.mjs"], "sourcesContent": ["/*!\n  * core v11.1.12\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { registerMessageCompiler, compile, registerMessageResolver, resolveValue, registerLocaleFallbacker, fallbackWithLocale<PERSON>hain } from '@intlify/core-base';\nexport * from '@intlify/core-base';\nimport { getGlobalThis } from '@intlify/shared';\n\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n    if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n    }\n    if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n    }\n}\n\n{\n    initFeatureFlags();\n}\n// register message compiler at @intlify/core\nregisterMessageCompiler(compile);\n// register message resolver at @intlify/core\nregisterMessageResolver(resolveValue);\n// register fallback locale at @intlify/core\nregisterLocaleFallbacker(fallbackWithLocaleChain);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,SAAS,mBAAmB;AACxB,MAAI,OAAO,8BAA8B,WAAW;AAChD,kBAAc,EAAE,4BAA4B;AAAA,EAChD;AACA,MAAI,OAAO,sCAAsC,WAAW;AACxD,kBAAc,EAAE,oCAAoC;AAAA,EACxD;AACJ;AAEA;AACI,mBAAiB;AACrB;AAEA,wBAAwB,OAAO;AAE/B,wBAAwB,YAAY;AAEpC,yBAAyB,uBAAuB;", "names": []}