import { withQuery } from "ufo";
import { useRegistryScript } from "#nuxt-scripts/utils";
import { object, string, optional, boolean, union, literal } from "#nuxt-scripts-validator";
export const GoogleTagManagerOptions = object({
  /** GTM container ID (format: GTM-XXXXXX) */
  id: string(),
  /** Optional dataLayer variable name */
  l: optional(string()),
  /** Authentication token for environment-specific container versions */
  auth: optional(string()),
  /** Preview environment name */
  preview: optional(string()),
  /** Forces GTM cookies to take precedence when true */
  cookiesWin: optional(union([boolean(), literal("x")])),
  /** Enables debug mode when true */
  debug: optional(union([boolean(), literal("x")])),
  /** No Personal Advertising - disables advertising features when true */
  npa: optional(union([boolean(), literal("1")])),
  /** Custom dataLayer name (alternative to "l" property) */
  dataLayer: optional(string()),
  /** Environment name for environment-specific container */
  envName: optional(string()),
  /** Referrer policy for analytics requests */
  authReferrerPolicy: optional(string())
});
export function useScriptGoogleTagManager(options) {
  return useRegistryScript(
    options?.key || "googleTagManager",
    (opts) => {
      const dataLayerName = opts?.l ?? opts?.dataLayer ?? "dataLayer";
      return {
        scriptInput: {
          src: withQuery("https://www.googletagmanager.com/gtm.js", {
            id: opts.id,
            l: opts.l,
            gtm_auth: opts.auth,
            gtm_preview: opts.preview,
            gtm_cookies_win: opts.cookiesWin ? "x" : void 0,
            gtm_debug: opts.debug ? "x" : void 0,
            gtm_npa: opts.npa ? "1" : void 0,
            gtm_data_layer: opts.dataLayer,
            gtm_env: opts.envName,
            gtm_auth_referrer_policy: opts.authReferrerPolicy
          })
        },
        schema: import.meta.dev ? GoogleTagManagerOptions : void 0,
        scriptOptions: {
          use: () => {
            return {
              dataLayer: window[dataLayerName],
              google_tag_manager: window.google_tag_manager
            };
          },
          performanceMarkFeature: "nuxt-third-parties-gtm",
          tagPriority: 1
        },
        clientInit: import.meta.server ? void 0 : () => {
          window[dataLayerName] = window[dataLayerName] || [];
          function gtag(...args) {
            window[dataLayerName].push(args);
          }
          options?.onBeforeGtmStart?.(gtag);
          window[dataLayerName].push({
            "gtm.start": (/* @__PURE__ */ new Date()).getTime(),
            "event": "gtm.js"
          });
        }
      };
    },
    options
  );
}
