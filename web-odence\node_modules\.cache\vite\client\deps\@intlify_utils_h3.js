// node_modules/@intlify/utils/dist/shared/utils.9f8159f5.mjs
var objectToString = Object.prototype.toString;
var toTypeString = (value) => objectToString.call(value);
function isURL(val) {
  return toTypeString(val) === "[object URL]";
}
function isURLSearchParams(val) {
  return toTypeString(val) === "[object URLSearchParams]";
}
function isLocale(val) {
  return toTypeString(val) === "[object Intl.Locale]";
}
function toLocale(val) {
  return isLocale(val) ? val : new Intl.Locale(val);
}
function validateLangTag(lang) {
  try {
    Intl.getCanonicalLocales(lang);
    return true;
  } catch {
    return false;
  }
}
function parseAcceptLanguage(value) {
  return value.split(",").map((tag) => tag.split(";")[0]).filter(
    (tag) => !(tag === "*" || tag === "")
  );
}
function createPathIndexLanguageParser(index = 0) {
  return (path) => {
    const rawPath = typeof path === "string" ? path : path.pathname;
    const normalizedPath = rawPath.split("?")[0];
    const parts = normalizedPath.split("/");
    if (parts[0] === "") {
      parts.shift();
    }
    return parts.length > index ? parts[index] || "" : "";
  };
}
var pathLanguageParser = createPathIndexLanguageParser();
var DEFAULT_LANG_TAG = "en-US";
var DEFAULT_COOKIE_NAME = "i18n_locale";
var ACCEPT_LANGUAGE_HEADER = "accept-language";
function parseDefaultHeader(input) {
  return [input];
}
function getHeaderLanguagesWithGetter(getter, {
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  const langString = getter();
  return langString ? name === ACCEPT_LANGUAGE_HEADER ? parser === parseDefaultHeader ? parseAcceptLanguage(langString) : parser(langString) : parser(langString) : [];
}
function getLocaleWithGetter(getter) {
  return toLocale(getter());
}
function validateLocale(locale) {
  if (!(isLocale(locale) || typeof locale === "string" && validateLangTag(locale))) {
    throw new SyntaxError(`locale is invalid: ${locale.toString()}`);
  }
}
function mapToLocaleFromLanguageTag(getter, ...args) {
  return Reflect.apply(getter, null, args).map(
    (lang) => getLocaleWithGetter(() => lang)
  );
}
function getPathLanguage(path, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {
  return (parser || pathLanguageParser)(path) || lang;
}
function getPathLocale(path, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {
  return new Intl.Locale(getPathLanguage(path, { lang, parser }));
}
function getURLSearchParams(input) {
  if (isURLSearchParams(input)) {
    return input;
  } else if (isURL(input)) {
    return input.searchParams;
  } else {
    return new URLSearchParams(input);
  }
}
function getQueryLanguage(query, { lang = DEFAULT_LANG_TAG, name = "lang" } = {}) {
  const queryParams = getURLSearchParams(query);
  return queryParams.get(name) || lang;
}
function getQueryLocale(query, { lang = DEFAULT_LANG_TAG, name = "locale" } = {}) {
  return new Intl.Locale(getQueryLanguage(query, { lang, name }));
}

// node_modules/@intlify/utils/dist/h3.mjs
import { setCookie, getRequestURL, getCookie, getHeaders } from "h3";
function getHeaderLanguages(event, {
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  const getter = () => {
    const headers = getHeaders(event);
    return headers[name];
  };
  return getHeaderLanguagesWithGetter(getter, { name, parser });
}
function getHeaderLanguage(event, {
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  return getHeaderLanguages(event, { name, parser })[0] || "";
}
function getHeaderLocales(event, {
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  return mapToLocaleFromLanguageTag(getHeaderLanguages, event, { name, parser });
}
function tryHeaderLocales(event, {
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  try {
    return getHeaderLocales(event, { name, parser });
  } catch {
    return null;
  }
}
function getHeaderLocale(event, {
  lang = DEFAULT_LANG_TAG,
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  return getLocaleWithGetter(() => getHeaderLanguages(event, { name, parser })[0] || lang);
}
function tryHeaderLocale(event, {
  lang = DEFAULT_LANG_TAG,
  name = ACCEPT_LANGUAGE_HEADER,
  parser = parseDefaultHeader
} = {}) {
  try {
    return getHeaderLocale(event, { lang, name, parser });
  } catch {
    return null;
  }
}
function getCookieLocale(event, { lang = DEFAULT_LANG_TAG, name = DEFAULT_COOKIE_NAME } = {}) {
  return getLocaleWithGetter(() => getCookie(event, name) || lang);
}
function tryCookieLocale(event, { lang = DEFAULT_LANG_TAG, name = DEFAULT_COOKIE_NAME } = {}) {
  try {
    return getCookieLocale(event, { lang, name });
  } catch {
    return null;
  }
}
function setCookieLocale(event, locale, options = { name: DEFAULT_COOKIE_NAME }) {
  validateLocale(locale);
  setCookie(event, options.name, locale.toString(), options);
}
function getPathLocale2(event, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {
  return getPathLocale(getRequestURL(event), { lang, parser });
}
function tryPathLocale(event, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {
  try {
    return getPathLocale2(event, { lang, parser });
  } catch {
    return null;
  }
}
function getQueryLocale2(event, { lang = DEFAULT_LANG_TAG, name = "locale" } = {}) {
  return getQueryLocale(getRequestURL(event), { lang, name });
}
function tryQueryLocale(event, { lang = DEFAULT_LANG_TAG, name = "locale" } = {}) {
  try {
    return getQueryLocale2(event, { lang, name });
  } catch {
    return null;
  }
}
export {
  getCookieLocale,
  getHeaderLanguage,
  getHeaderLanguages,
  getHeaderLocale,
  getHeaderLocales,
  getPathLocale2 as getPathLocale,
  getQueryLocale2 as getQueryLocale,
  setCookieLocale,
  tryCookieLocale,
  tryHeaderLocale,
  tryHeaderLocales,
  tryPathLocale,
  tryQueryLocale
};
//# sourceMappingURL=@intlify_utils_h3.js.map
