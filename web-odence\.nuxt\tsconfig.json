{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "vue-i18n": ["../node_modules/vue-i18n/dist/vue-i18n", "../node_modules/vue-i18n"], "@intlify/shared": ["../node_modules/@intlify/shared/dist/shared", "../node_modules/@intlify/shared"], "@intlify/message-compiler": ["../node_modules/@intlify/message-compiler/dist/message-compiler", "../node_modules/@intlify/message-compiler"], "@intlify/core-base": ["../node_modules/@intlify/core-base/dist/core-base", "../node_modules/@intlify/core-base"], "@intlify/core": ["../node_modules/@intlify/core/dist/core.node", "../node_modules/@intlify/core"], "@intlify/utils/h3": ["../node_modules/@intlify/utils/dist/h3", "../node_modules/@intlify/utils"], "ufo": ["../node_modules/ufo/dist/index", "../node_modules/ufo"], "@types/cloudflare-turnstile": ["../node_modules/@types/cloudflare-turnstile"], "~": ["../app"], "~/*": ["../app/*"], "@": ["../app"], "@/*": ["../app/*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "#shared/*": ["../shared/*"], "#app": ["../node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/nuxt/dist/app/compat/vue-demi"], "#image": ["../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/@nuxt/image/dist/runtime/*"], "#color-mode-options": ["./color-mode-options.mjs"], "#i18n": ["../node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "#i18n-kit": ["../node_modules/@nuxtjs/i18n/dist/runtime/kit"], "#i18n-kit/*": ["../node_modules/@nuxtjs/i18n/dist/runtime/kit/*"], "#internal-i18n-types": ["../node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-scripts-validator": ["../node_modules/@nuxt/scripts/dist/runtime/validation/valibot"], "#nuxt-scripts": ["../node_modules/@nuxt/scripts/dist/runtime"], "#nuxt-scripts/*": ["../node_modules/@nuxt/scripts/dist/runtime/*"], "vee-validate": ["../node_modules/vee-validate/dist/vee-validate"], "@vee-validate/yup": ["../node_modules/@vee-validate/yup/dist/vee-validate-yup"], "#vue-router": ["../node_modules/vue-router"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables"], "#imports": ["./imports"], "#app-manifest": ["./manifest/meta/8b023649-4ec9-4ed1-9b4f-86c61699bd4e"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../i18n/**/*", "./nuxt.d.ts", "../app/**/*", "../modules/*/runtime/**/*", "../layers/*/app/**/*", "../layers/*/modules/*/runtime/**/*", "../shared/**/*.d.ts", "../modules/*/shared/**/*.d.ts", "../layers/*/shared/**/*.d.ts", "../*.d.ts", "../layers/*/*.d.ts", "./nuxt.node.d.ts", "../modules/*.*", "../nuxt.config.*", "../.config/nuxt.*", "../layers/*/nuxt.config.*", "../layers/*/.config/nuxt.*", "../layers/*/modules/**/*", "../shared/**/*", "../modules/*/shared/**/*", "../layers/*/shared/**/*", "../node_modules/runtime", "../node_modules/dist/runtime"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxt/image/node_modules", "../node_modules/@nuxt/eslint/node_modules", "../node_modules/@nuxt/fonts/node_modules", "../node_modules/@nuxtjs/color-mode/node_modules", "../node_modules/@nuxtjs/i18n/node_modules", "../node_modules/@nuxt/scripts/node_modules", "../node_modules/@nuxtjs/turnstile/node_modules", "../node_modules/@vee-validate/nuxt/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist", "../.data", "../modules/*/runtime/server/**/*", "../layers/*/server/**/*", "../layers/*/modules/*/runtime/server/**/*", "../node_modules/runtime/server", "../node_modules/dist/runtime/server"]}