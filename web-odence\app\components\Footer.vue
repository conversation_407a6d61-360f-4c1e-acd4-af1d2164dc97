<script setup lang="ts">
	import { PaperAirplaneIcon } from '@heroicons/vue/24/solid';
	const { locale } = useI18n();
</script>

<template>
	<footer id="contact"
		class="bg-base-100 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-10 py-10 px-6 sm:px-9 md:px-12 lg:px-15 2xl:mx-4">
		<!-- Studio -->
		<div class="grid grid-rows-2 justify-items-center items-center md:max-lg:col-span-2 lg:justify-items-start">
			<div class="tracking-wider font-anta text-2xl text-orange-700 dark:text-orange-200">
				<a href="https://lefinitas.com" target="_blank" rel="noopener noreferrer" aria-label="Lefinitas">LEFINITAS</a>
			</div>
			<div class="text-sm md:text-base text-center lg:text-left lg:text-lg">{{ $t('footer.slogan') }}</div>
		</div>

		<!-- Account -->
		<div class="grid grid-rows-2 justify-center gap-2 lg:gap-1 items-center">
			<div class="flex justify-center items-center text-lg font-medium tracking-wide lg:text-xl">{{
				$t('footer.account') }}</div>
			<div class="grid grid-cols-6 gap-5 lg:max-xl:self-start" aria-label="Social Media" role="link">
				<a href="https://facebook.com/undervolta" target="_blank" class="footer-account" aria-label="Facebook">
					<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-facebook size-7"
						viewBox="0 0 16 16">
						<path
							d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951" />
					</svg>
				</a>
				<a href="https://instagram.com/undervolta" target="_blank" class="footer-account" aria-label="Instagram" rel="noopener noreferrer">
					<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-instagram size-7"
						viewBox="0 0 16 16">
						<path
							d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.9 3.9 0 0 0-1.417.923A3.9 3.9 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.9 3.9 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.9 3.9 0 0 0-.923-1.417A3.9 3.9 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599s.453.546.598.92c.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.5 2.5 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.5 2.5 0 0 1-.92-.598 2.5 2.5 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233s.008-2.388.046-3.231c.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92s.546-.453.92-.598c.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92m-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217m0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334" />
					</svg>
				</a>
				<a href="https://youtube.com/@undervolta" target="_blank" class="footer-account" aria-label="Youtube" rel="noopener noreferrer">
					<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-youtube size-7"
						viewBox="0 0 16 16">
						<path
							d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z" />
					</svg>
				</a>
				<a href="https://discord.com/invite/5rXGW7SPkF" target="_blank" class="footer-account" aria-label="Discord" rel="noopener noreferrer">
					<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-discord size-7"
						viewBox="0 0 16 16">
						<path
							d="M13.545 2.907a13.2 13.2 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.2 12.2 0 0 0-3.658 0 8 8 0 0 0-.412-.833.05.05 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.04.04 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032q.003.022.021.037a13.3 13.3 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019q.463-.63.818-1.329a.05.05 0 0 0-.01-.059l-.018-.011a9 9 0 0 1-1.248-.595.05.05 0 0 1-.02-.066l.015-.019q.127-.095.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.05.05 0 0 1 .053.007q.121.1.248.195a.05.05 0 0 1-.004.085 8 8 0 0 1-1.249.594.05.05 0 0 0-.03.03.05.05 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.2 13.2 0 0 0 4.001-2.02.05.05 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.03.03 0 0 0-.02-.019m-8.198 7.307c-.789 0-1.438-.724-1.438-1.612s.637-1.613 1.438-1.613c.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612m5.316 0c-.788 0-1.438-.724-1.438-1.612s.637-1.613 1.438-1.613c.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612" />
					</svg>
				</a>
				<a href="https://x.com/undervolta" target="_blank" class="footer-account" aria-label="X" rel="noopener noreferrer">
					<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-twitter-x size-7"
						viewBox="0 0 16 16">
						<path
							d="M12.6.75h2.454l-5.36 6.142L16 15.25h-4.937l-3.867-5.07-4.425 5.07H.316l5.733-6.57L0 .75h5.063l3.495 4.633L12.601.75Zm-.86 13.028h1.36L4.323 2.145H2.865z" />
					</svg>
				</a>
				<a v-if="locale === 'id'" href="https://trakteer.id/undervolta" target="_blank" aria-label="Trakteer" class="footer-account" rel="noopener noreferrer">
					<svg viewBox="0 0 36.139912 36.252059" version="1.1" id="svg1" fill="currentColor" class="size-7"
						xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
						<g id="layer1" transform="translate(-479.72513,-88.952102)">
							<path
								d="m 486.60638,88.952105 c -3.8123,0 -6.88123,3.06945 -6.88123,6.88175 v 22.489065 c 0,3.81231 3.06893,6.88124 6.88123,6.88124 h 22.37745 c 3.8123,0 6.88123,-3.06893 6.88123,-6.88124 V 95.833855 c 0,-3.8123 -3.06893,-6.88175 -6.88123,-6.88175 z m 9.8795,5.77381 c 0.91541,0.0143 1.80479,0.50533 2.27428,1.42058 l 0.2341,0.45579 -0.62477,0.22221 c -1.07121,0.38047 -1.91686,1.66797 -1.76682,2.69027 0.0654,0.4462 0.0437,0.45901 -0.63045,0.35863 -1.22747,-0.18276 -2.10323,-1.24124 -2.10323,-2.54248 0,-0.80262 0.16503,-1.26789 0.6196,-1.74873 0.55771,-0.58992 1.28531,-0.8674 1.99729,-0.85627 z m 2.98845,2.55384 c 0.41023,-0.0178 0.82512,0.0734 1.19683,0.28474 0.89722,0.51021 1.32844,1.54265 1.05781,2.532665 l -0.14934,0.54674 h -1.98489 c -1.89646,0 -1.99191,-0.0123 -2.13269,-0.27543 -0.22339,-0.417405 -0.0874,-1.719695 0.22324,-2.138895 0.43493,-0.58713 1.10533,-0.9201 1.78904,-0.94982 z m -1.68052,4.136195 c 4.76098,0 4.92092,0.008 5.14801,0.2589 0.33203,0.36687 0.30324,1.01248 -0.0584,1.30534 -0.27306,0.22113 -0.61985,0.2372 -5.08961,0.2372 -4.46977,0 -4.81654,-0.0161 -5.08961,-0.2372 -0.36167,-0.29286 -0.39097,-0.93847 -0.0589,-1.30534 0.22711,-0.25095 0.38754,-0.2589 5.14852,-0.2589 z m -4.95422,3.08818 h 4.88963 4.88962 v 0.38447 c 0,0.32175 0.0775,0.41627 0.47749,0.58343 0.61534,0.25711 0.90727,0.52715 1.20871,1.11776 0.22612,0.44304 0.24392,0.82045 0.24392,5.24826 0,4.53263 -0.0131,4.79685 -0.25994,5.28081 -0.14294,0.28018 -0.50072,0.6683 -0.79478,0.863 l -0.53485,0.35398 -4.96301,0.0326 c -4.6368,0.0303 -4.99845,0.0162 -5.4932,-0.21136 -0.65589,-0.3016 -1.1512,-0.93287 -1.3255,-1.6893 -0.0762,-0.33056 -0.13409,-2.33623 -0.13591,-4.70255 -0.003,-4.68592 0.0622,-5.15194 0.82579,-5.82239 0.24226,-0.21273 0.56024,-0.42489 0.70642,-0.47129 0.20061,-0.0637 0.26561,-0.1922 0.26561,-0.52555 z m 3.16105,3.99149 c -1.04188,-0.008 -2.13165,1.06774 -2.13165,2.10374 0,0.85751 0.34664,1.37128 2.15387,3.19516 0.96736,0.97628 1.76635,1.77508 1.7756,1.77508 0.009,0 0.79367,-0.78286 1.74305,-1.73994 0.94937,-0.95709 1.82746,-1.92618 1.9513,-2.15336 0.28369,-0.52037 0.28893,-1.40173 0.0119,-1.98541 -0.52484,-1.10601 -2.05534,-1.47547 -3.13935,-0.75809 -0.44212,0.29256 -0.59397,0.33614 -0.7555,0.21756 -0.43777,-0.32138 -1.25063,-0.65205 -1.60921,-0.65474 z" />
						</g>
					</svg>
				</a>
				<a v-else href="https://ko-fi.com/undervolta" target="_blank" aria-label="Ko-Fi" class="footer-account" rel="noopener noreferrer">
					<svg viewBox="0 0 241 194" fill="currentColor" version="1.1" class="size-7" id="svg7"
						xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
						<mask maskUnits="userSpaceOnUse" id="mask8">
							<g id="g9">
								<path d="M 240.469,0.958984 H -0.******** V 193.918 H 240.469 Z" fill="#currentColor"
									id="path8" />
							</g>
						</mask>
						<mask maskUnits="userSpaceOnUse" id="mask9">
							<g id="g10">
								<path d="M 240.469,0.958984 H -0.******** V 193.918 H 240.469 Z" fill="#currentColor"
									id="path9" />
							</g>
						</mask>
						<mask maskUnits="userSpaceOnUse" id="mask10">
							<g id="g11">
								<path d="M 240.469,0.958984 H -0.******** V 193.918 H 240.469 Z" fill="#currentColor"
									id="path10" />
							</g>
						</mask>
						<mask maskUnits="userSpaceOnUse" id="mask11">
							<g id="g12">
								<path d="M 240.469,0.958984 H -0.******** V 193.918 H 240.469 Z" fill="#currentColor"
									id="path11" />
							</g>
						</mask>
						<mask maskUnits="userSpaceOnUse" id="mask12">
							<g id="g13">
								<path d="M 240.469,0.958984 H -0.******** V 193.918 H 240.469 Z" fill="#currentColor"
									id="path12" />
							</g>
						</mask>
						<mask maskUnits="userSpaceOnUse" id="mask13">
							<g id="g14">
								<path d="M 240.469,0.958984 H -0.******** V 193.918 H 240.469 Z" fill="#currentColor"
									id="path13" />
							</g>
						</mask>
						<path
							d="M 114.54492 0.95898438 C 91.984724 0.95898437 70.286131 1.1424456 52.832031 3.3535156 C 38.127431 5.2288056 24.885816 12.115194 15.541016 22.746094 C 5.3726452 34.320994 -0.005859375 49.887025 -0.005859375 67.765625 C -0.005859375 97.922325 1.1984366 124.91112 15.972656 149.82812 C 32.660256 178.25512 61.131566 193.91016 96.134766 193.91016 L 96.128906 193.91797 L 103.40039 193.91797 C 149.04139 193.91797 172.86886 167.63284 183.00586 151.96484 L 183.08984 151.83789 C 186.23784 146.87189 188.89888 141.82777 191.04688 136.75977 C 219.82188 129.73377 240.46875 103.8856 240.46875 73 L 240.46875 69.363281 C 240.46875 51.262981 234.36822 35.443003 222.82422 23.595703 C 211.45822 11.932203 195.28922 4.7604956 176.07422 2.8535156 C 161.62922 1.4090356 151.25892 0.95898438 114.54492 0.95898438 z M 114.54492 16.158203 C 151.37292 16.158203 160.91859 16.614463 174.55859 17.976562 C 206.61659 21.156963 225.25586 40.936722 225.25586 69.357422 L 225.25586 72.994141 C 225.25586 99.368741 205.93116 120.96637 179.78516 123.23438 C 177.73916 130.05838 174.55928 136.87327 170.23828 143.69727 C 160.23528 159.15527 140.22839 178.70703 103.40039 178.70703 L 96.126953 178.70703 C 66.116053 178.70703 42.926794 165.75142 29.058594 142.10742 C 16.780494 121.42242 15.191406 98.456478 15.191406 67.767578 L 15.197266 67.767578 C 15.197266 37.528678 33.386559 21.164194 54.755859 18.433594 C 70.898659 16.387194 90.906924 16.158203 114.54492 16.158203 z M 55.851562 92.027344 C 56.277777 93.584904 56.800845 95.111354 57.408203 96.605469 C 56.800845 95.111354 56.277777 93.584904 55.851562 92.027344 z M 146.87109 92.03125 C 146.44315 93.589192 145.91794 95.117029 145.30664 96.611328 C 145.91794 95.117029 146.44315 93.589192 146.87109 92.03125 z M 145.30664 96.611328 C 144.69534 98.105627 143.9969 99.566124 143.22266 100.99414 C 143.9969 99.566124 144.69534 98.105627 145.30664 96.611328 z " />
						<path
							d="M 114.53711 33.208984 C 89.759308 33.208984 72.027234 33.436022 58.615234 35.482422 C 39.748334 38.213022 32.240234 48.895434 32.240234 67.990234 L 32.246094 67.990234 C 32.246094 97.317134 34.065981 116.18445 43.613281 133.68945 C 54.523081 153.92445 74.3026 161.65234 96.8125 161.65234 L 103.85742 161.65234 C 133.41142 161.65234 147.73636 147.32813 155.69336 134.82812 C 159.55836 128.46113 162.9652 121.41687 164.7832 112.54688 L 166.14648 106.86328 L 174.33203 106.86328 C 192.52103 106.86328 208.20703 92.090197 208.20703 73.216797 L 208.20703 69.808594 C 208.20703 48.667294 195.02459 37.522928 172.05859 34.798828 C 159.10259 33.664728 151.37111 33.208984 114.53711 33.208984 z M 171.8418 53.21875 C 183.4358 53.21875 189.80273 60.043525 189.80273 70.953125 C 189.80273 81.862725 183.4358 88.457031 171.8418 88.457031 C 168.20831 88.457031 166.16216 86.414137 166.1582 83.6875 L 166.1582 57.996094 C 166.1582 55.265494 168.2048 53.21875 171.8418 53.21875 z M 80.900391 54.810547 C 90.226191 54.810547 96.815418 59.582534 101.13672 66.177734 C 105.91272 59.588734 112.28061 54.810547 121.59961 54.810547 C 137.28661 54.810547 148.19531 67.767814 148.19531 82.320312 C 148.19531 95.738813 140.69492 107.33183 130.91992 116.42383 C 124.77992 122.56183 114.78198 128.92948 107.95898 133.02148 C 106.13398 134.15648 103.86672 134.8418 101.13672 134.8418 C 98.861818 134.8418 96.813778 134.15648 94.767578 133.02148 C 87.950578 128.92948 77.947178 122.56183 71.580078 116.42383 C 62.032578 107.32583 54.53125 95.732513 54.53125 82.320312 L 54.525391 82.320312 C 54.525391 67.767813 64.985791 54.810547 80.900391 54.810547 z " />
					</svg>
				</a>
			</div>
		</div>

		<!-- Contact -->
		<div class="grid grid-rows-2 justify-center items-center gap-2 lg:gap-1 lg:justify-end">
			<div class="flex justify-center text-lg font-medium tracking-wide lg:justify-end lg:text-xl">{{
				$t('footer.contact1') }}</div>
			<div class="items-center lg:max-xl:self-start">
				<a href="mailto:<EMAIL>" class="flex items-center justify-center group">
					<div
						class="flex border-1 rounded-s-lg border-orange-700 dark:border-orange-200 p-2 px-4 text-sm xl:text-base xl:px-6">
						<EMAIL></div>
					<div
						class="flex border-1 rounded-e-lg border-orange-700 bg-orange-700 text-neutral-content p-2 px-4 text-sm font-semibold xl:text-base xl:px-6 items-center">
						<div class="xl:hidden">{{ $t('footer.contact2') }}</div>
						<div
							class="max-xl:hidden transition-transform translate-x-3 group-hover:-translate-x-0 ease-in-out duration-300 ">
							{{ $t('footer.contact2') }}</div>
						<PaperAirplaneIcon
							class="max-xl:hidden size-5 ms-3 transition-all scale-0 opacity-0 group-hover:opacity-100 group-hover:scale-100 ease-in-out duration-300" />
					</div>
				</a>
			</div>
		</div>

		<!-- Copyright -->
		<div class="flex flex-col md:flex-none md:col-span-2 lg:col-span-3 items-center justify-center gap-1">
			<p class="text-center text-sm sm:hidden">Copyright © {{new Date().getFullYear()}} Undervolta</p>
			<p class="text-center text-sm sm:hidden">All Rights Reserved</p>
			<p class="text-center text-sm hidden sm:flex lg:text-base">Copyright © {{new Date().getFullYear()}}
				Undervolta - All Rights Reserved</p>
		</div>
	</footer>
</template>
