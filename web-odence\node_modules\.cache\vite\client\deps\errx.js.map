{"version": 3, "sources": ["../../../../errx/dist/index.js"], "sourcesContent": ["const IS_ABSOLUTE_RE = /^[/\\\\](?![/\\\\])|^[/\\\\]{2}(?!\\.)|^[a-z]:[/\\\\]/i;\nconst LINE_RE = /^\\s+at (?:(?<function>[^)]+) \\()?(?<source>[^)]+)\\)?$/u;\nconst SOURCE_RE = /^(?<source>.+):(?<line>\\d+):(?<column>\\d+)$/u;\nfunction captureRawStackTrace() {\n  if (!Error.captureStackTrace) {\n    return;\n  }\n  const stack = new Error();\n  Error.captureStackTrace(stack);\n  return stack.stack;\n}\nfunction captureStackTrace() {\n  const stack = captureRawStackTrace();\n  return stack ? parseRawStackTrace(stack) : [];\n}\nfunction parseRawStackTrace(stacktrace) {\n  const trace = [];\n  for (const line of stacktrace.split(\"\\n\")) {\n    const parsed = LINE_RE.exec(line)?.groups;\n    if (!parsed) {\n      continue;\n    }\n    if (!parsed.source) {\n      continue;\n    }\n    const parsedSource = SOURCE_RE.exec(parsed.source)?.groups;\n    if (parsedSource) {\n      Object.assign(parsed, parsedSource);\n    }\n    if (IS_ABSOLUTE_RE.test(parsed.source)) {\n      parsed.source = `file://${parsed.source}`;\n    }\n    if (parsed.source === import.meta.url) {\n      continue;\n    }\n    for (const key of [\"line\", \"column\"]) {\n      if (parsed[key]) {\n        parsed[key] = Number(parsed[key]);\n      }\n    }\n    trace.push(parsed);\n  }\n  return trace;\n}\n\nexport { captureRawStackTrace, captureStackTrace, parseRawStackTrace };\n"], "mappings": ";AAAA,IAAM,iBAAiB;AACvB,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,SAAS,uBAAuB;AAC9B,MAAI,CAAC,MAAM,mBAAmB;AAC5B;AAAA,EACF;AACA,QAAM,QAAQ,IAAI,MAAM;AACxB,QAAM,kBAAkB,KAAK;AAC7B,SAAO,MAAM;AACf;AACA,SAAS,oBAAoB;AAC3B,QAAM,QAAQ,qBAAqB;AACnC,SAAO,QAAQ,mBAAmB,KAAK,IAAI,CAAC;AAC9C;AACA,SAAS,mBAAmB,YAAY;AACtC,QAAM,QAAQ,CAAC;AACf,aAAW,QAAQ,WAAW,MAAM,IAAI,GAAG;AACzC,UAAM,SAAS,QAAQ,KAAK,IAAI,GAAG;AACnC,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,QAAI,CAAC,OAAO,QAAQ;AAClB;AAAA,IACF;AACA,UAAM,eAAe,UAAU,KAAK,OAAO,MAAM,GAAG;AACpD,QAAI,cAAc;AAChB,aAAO,OAAO,QAAQ,YAAY;AAAA,IACpC;AACA,QAAI,eAAe,KAAK,OAAO,MAAM,GAAG;AACtC,aAAO,SAAS,UAAU,OAAO,MAAM;AAAA,IACzC;AACA,QAAI,OAAO,WAAW,YAAY,KAAK;AACrC;AAAA,IACF;AACA,eAAW,OAAO,CAAC,QAAQ,QAAQ,GAAG;AACpC,UAAI,OAAO,GAAG,GAAG;AACf,eAAO,GAAG,IAAI,OAAO,OAAO,GAAG,CAAC;AAAA,MAClC;AAAA,IACF;AACA,UAAM,KAAK,MAAM;AAAA,EACnB;AACA,SAAO;AACT;", "names": []}