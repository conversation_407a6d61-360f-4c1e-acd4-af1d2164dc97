import type { ElementScriptTrigger } from '../types.js';
type __VLS_Props = {
    trigger?: ElementScriptTrigger;
    publishableKey: string;
    pricingTableId: string;
    clientReferenceId?: string;
    customerEmail?: string;
    customerSessionClientSecret?: string;
};
declare var __VLS_1: {}, __VLS_3: {}, __VLS_5: {}, __VLS_7: {};
type __VLS_Slots = {} & {
    loading?: (props: typeof __VLS_1) => any;
} & {
    awaitingLoad?: (props: typeof __VLS_3) => any;
} & {
    error?: (props: typeof __VLS_5) => any;
} & {
    default?: (props: typeof __VLS_7) => any;
};
declare const __VLS_component: import("vue").DefineComponent<__VLS_Props, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    error: () => any;
    ready: (args_0: import("../types.js").UseScriptContext<Record<string | symbol, any>>) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    onError?: (() => any) | undefined;
    onReady?: ((args_0: import("../types.js").UseScriptContext<Record<string | symbol, any>>) => any) | undefined;
}>, {
    trigger: ElementScriptTrigger;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
