import type { ElementScriptTrigger } from '#nuxt-scripts/types';
type __VLS_Props = {
    modelValue?: string;
    trigger?: ElementScriptTrigger;
    element?: string;
    siteKey?: string;
    options?: Omit<Partial<Turnstile.RenderParameters>, 'callback'>;
    resetInterval?: number;
};
declare const _default: import("vue").DefineComponent<__VLS_Props, {
    reset: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {} & {
    "update:modelValue": (token: string) => any;
}, string, import("vue").PublicProps, Readonly<__VLS_Props> & Readonly<{
    "onUpdate:modelValue"?: ((token: string) => any) | undefined;
}>, {
    element: string;
    options: Omit<Partial<Turnstile.RenderParameters>, "callback">;
    resetInterval: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, false, {}, any>;
export default _default;
