{"name": "@nuxtjs/turnstile", "version": "1.1.0", "license": "MIT", "description": "Cloudflare Turnstile integration for Nuxt", "repository": {"type": "git", "url": "git+https://github.com/nuxt-modules/turnstile.git"}, "keywords": ["nuxt", "module", "nuxt-module", "<PERSON><PERSON>a", "recaptcha", "cloudflare"], "author": {"name": "<PERSON> <<EMAIL>>", "url": "https://github.com/danielroe"}, "type": "module", "exports": {".": {"types": "./dist/types.d.mts", "import": "./dist/module.mjs"}, "./runtime/*": "./dist/runtime/*"}, "main": "./dist/module.mjs", "typesVersions": {"*": {".": ["./dist/types.d.mts"]}}, "files": ["dist"], "scripts": {"build": "pnpm dev:prepare && nuxt-module-build build", "dev": "nuxt dev playground", "dev:build": "nuxt build playground", "dev:prepare": "pnpm nuxt-module-build build --stub && nuxt prepare && nuxt prepare playground", "playground:prepare": "nuxt prepare playground", "docs:dev": "nuxt dev docs", "docs:build": "nuxt generate docs", "lint": "pnpm lint:all:eslint", "lint:all:eslint": "pnpm lint:eslint .", "lint:eslint": "eslint --fix", "prepack": "pnpm build", "prepublishOnly": "pnpm lint && pnpm test", "release": "bumpp && npm publish", "test": "vitest run --coverage", "test:types": "vue-tsc --noEmit && cd playground && vue-tsc --noEmit"}, "dependencies": {"@nuxt/kit": "^3.16.0", "@types/cloudflare-turnstile": "0.2.2", "defu": "^6.1.4", "pathe": "^2.0.3"}, "devDependencies": {"@nuxt/eslint-config": "1.9.0", "@nuxt/module-builder": "1.0.2", "@nuxt/schema": "3.19.1", "@nuxt/scripts": "0.11.13", "@nuxt/test-utils": "3.19.2", "@types/node": "22.18.1", "@vitest/coverage-v8": "3.2.4", "bumpp": "10.2.3", "conventional-changelog-conventionalcommits": "9.1.0", "eslint": "9.35.0", "expect-type": "1.2.2", "get-port-please": "3.2.0", "lint-staged": "16.1.6", "nuxt": "3.19.1", "playwright": "1.55.0", "typescript": "5.9.2", "vitest": "3.2.4", "vue-tsc": "3.0.6"}, "peerDependencies": {"@nuxt/scripts": "^0.11.0"}, "resolutions": {"@nuxt/kit": "3.19.1", "@nuxtjs/turnstile": "link:."}, "packageManager": "pnpm@10.15.1"}